# Testing with Vitest

This project uses Vitest as the testing framework for Vue.js components and utility functions.

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode (for development)
pnpm test:watch

# Run tests with coverage report
pnpm test:coverage

# Run tests with UI
pnpm test:ui
```

## Test File Structure

- Component tests should be placed in `__tests__` directories next to the components they test
- Utility function tests should be placed in `__tests__` directories next to the utilities they test
- Test files should be named with `.spec.js` or `.test.js` extensions

## Example Component Test

```js
import { mount } from '@vue/test-utils';
import MyComponent from '../MyComponent.vue';

describe('MyComponent.vue', () => {
  it('renders correctly', () => {
    const wrapper = mount(MyComponent, {
      propsData: { 
        // props here
      }
    });
    
    // assertions here
  });
});
```

## Example Utility Test

```js
import { myFunction } from '../myUtils';

describe('myFunction', () => {
  it('works correctly', () => {
    expect(myFunction(1, 2)).toBe(3);
  });
});
```

## Testing Vue Components

When testing Vue components, use the `@vue/test-utils` library to mount and interact with components:

```js
import { mount, shallowMount } from '@vue/test-utils';
```

- Use `mount` for full rendering including child components
- Use `shallowMount` for shallow rendering (stubs child components)

## Testing Async Code

For testing asynchronous code, use async/await:

```js
it('tests async code', async () => {
  const wrapper = mount(MyComponent);
  await wrapper.vm.$nextTick();
  // assertions here
});
```

## Mocking Dependencies

Use Vitest's mocking capabilities to mock dependencies:

```js
import { vi } from 'vitest';

// Mock a function
const mockFn = vi.fn();
mockFn.mockReturnValue('mocked value');

// Mock a module
vi.mock('axios', () => ({
  default: {
    get: vi.fn(() => Promise.resolve({ data: 'mocked data' }))
  }
}));
```

## Test Coverage

Run tests with coverage to see how much of your code is covered by tests:

```bash
pnpm test:coverage
```

This will generate a coverage report in the `coverage` directory.
