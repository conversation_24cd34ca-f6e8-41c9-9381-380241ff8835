import Vue from 'vue';
import CompositionApi from '@vue/composition-api';
import { vi } from 'vitest';

// Install Vue Composition API
Vue.use(CompositionApi);

// Global Vue configuration for tests
Vue.config.productionTip = false;
Vue.config.devtools = false;

// Mock global variables that might be used in components
global.window = Object.create(window);
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
  writable: true,
});

Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
  writable: true,
});

// Mock location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost/',
    host: 'localhost',
    pathname: '/',
    hash: '',
  },
  writable: true,
});
