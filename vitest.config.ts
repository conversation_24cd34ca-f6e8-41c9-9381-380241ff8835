import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue2';
import { resolve } from 'path';

export default defineConfig({
    plugins: [vue()],
    test: {
      globals: true,
      environment: 'jsdom',
      include: ['**/*.{test,spec}.{js,ts,jsx,tsx,vue}'],
      exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json', 'html'],
      },
      deps: {
        optimizer: {
          web: {
            include: ['vue', '@vue/composition-api']
          }
        }
      },
      root: '.',
      setupFiles: ['./tests/setup.ts'],
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        vue: 'vue/dist/vue.esm.js',
      },
    },
  });
