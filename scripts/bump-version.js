import fs from 'node:fs';
import path from 'node:path';

const pkgPath = path.resolve('package.json');
const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));

const now = new Date();
const yyyy = now.getFullYear();
const month = now.getMonth() + 1; // 1-12

const [curYear, curMonth, curBuild] = pkg.version.split('.').map(Number);

let build = 1;
if (curYear === yyyy && curMonth === month) {
  build = (curBuild || 0) + 1;
}

pkg.version = `${yyyy}.${month}.${build}`;

fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2) + '\n');
console.log(`🔖 New version: ${pkg.version}`);
