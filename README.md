# TOPS Browser

The foundational stack of this project is pnpm, Vite, Vue 2.7, TypeScript and Vitest.

Further details are in distributed README files which can be found in their respective directories.

## CLI Commands

```bash
# Install dependencies
pnpm install

# Run in dev mode
pnpm run dev

# Build for production
pnpm run build[:mode]
pnpm run build:test # Includes version bump, but eventually will be handled by CI

# Optionally run post-build
pnpm run preview

# Run all tests
pnpm test

# Run tests in watch mode (for development)
pnpm test:watch
```

## Commit Message Conventions

- `feat:` A new feature
- `fix:` A bug fix
- `docs:` Documentation only changes
- `style:` Changes that do not affect the meaning of the code (styling, white-space, formatting, missing semi-colons, etc)
- `refactor:` A code change that neither fixes a bug or adds a feature
- `test:` Adding missing tests
- `chore:` Changes to the build process or auxiliary tools and libraries such as documentation generation
