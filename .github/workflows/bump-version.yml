name: bump-version
on:
  push:
    branches: [test]
concurrency:
  group: bump-version-test
  cancel-in-progress: false

jobs:
  bump:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
          fetch-depth: 0

      - uses: pnpm/action-setup@v3
        with:
          version: 8
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Bump version
        run: |
          node scripts/bump-version.js
          if git diff --quiet; then
            echo "No version change needed"
            exit 0
          fi
          git config user.name  "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git add package.json
          git commit -m "chore(version): bump to $(jq -r .version package.json) [skip ci]"
          git push
