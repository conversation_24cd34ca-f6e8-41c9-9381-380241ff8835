{"name": "tops-browser", "private": true, "version": "2025.7.34", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "build:test": "node scripts/bump-version.js && vite build --mode test", "build:qa": "vite build --mode qa", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "lint": "eslint 'src/**/*.{js,ts,vue}' --fix", "lint:check": "eslint 'src/**/*.{js,ts,vue}'", "version:bump": "node scripts/bump-version.js"}, "devDependencies": {"@eslint/js": "^9.31.0", "@testing-library/vue": "^5.8.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-vue2": "^2.3.3", "@vue/test-utils": "^1.3.0", "eslint": "^9.31.0", "happy-dom": "^17.4.7", "jsdom": "^26.1.0", "typescript": "~5.7.3", "vite": "^6.3.5", "vite-plugin-style-import": "^2.0.0", "vitest": "^3.1.4", "vue-eslint-parser": "^10.2.0"}, "dependencies": {"@sentry/browser": "4.4.2", "@vue/composition-api": "^1.7.2", "axios": "^0.21.4", "bowser": "^2.11.0", "cookies-js": "^1.2.3", "date-fns": "1.29.0", "element-ui": "1.3.5", "fuse.js": "^6.6.2", "gsap": "^3.13.0", "is_js": "^0.9.0", "jquery": "^3.7.1", "lodash-es": "^4.17.21", "numeral": "^2.0.6", "pikaday": "^1.8.2", "pinia": "2.0.36", "portal-vue": "^2.1.7", "vue": "2.7.16", "vue-axios": "^2.1.5", "vue-meta": "^1.6.0", "vue-router": "^2.8.1", "vue2-daterange-picker": "^0.6.8", "vuedraggable": "2.24.3", "vuex": "^2.5.0", "vuex-persistedstate": "^2.7.1"}}