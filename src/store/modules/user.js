import Hub from '../../events/hub';
import StandardRequest from '@/api/StandardRequest.js';

import {
  isNil,
  forEach,
  castArray
} from 'lodash-es';

import {
  PRODUCTS,
  EVENT_USER_CHANGED,
  PRODUCTKEY_WEB_PORTAL,
  EVENT_PRODUCTS_CHANGED,
  EVENT_ORG_UNITS_CHANGED
} from '@/config.js';

const state = {
  Key: '',
  UserID: '',
  Name: '',
  Type: '',
  Products: [],
  OrgUnits: [],
  Profile: {}
};

const getters = {
  USER__state: state => state,
  USER__products: state => { // Products available to the user based on the User Products available
    let apps = [];

    apps.push({
      key: PRODUCTKEY_WEB_PORTAL,
      label: 'None selected',
      orgUnitRequired: false
    });

    forEach(state.Products, product => {
      // NOTE: Ignore webTOPS Portal
      if (product !== PRODUCTKEY_WEB_PORTAL && !isNil(PRODUCTS[product])) {
        apps.push(PRODUCTS[product]);
      }
    });

    return apps;
  }
};

const actions = {
  USER__getID (context, props) {
    new StandardRequest(context, {
      noun: 'User',
      verb: 'GetIDForUsers',
      data: {
        Key: castArray(props.keys)
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  USER__getKey (context, props) {
    new StandardRequest(context, {
      noun: 'User',
      verb: 'GetKeyForID',
      data: {
        Value: props.id
      }
    })
    .responseMiddleware(response => {
      response.Key = Number(response.Key);
    })
    .success(props.success);
  }
};

const mutations = {
  SET_USER_DATA (state, props) {
    state.Key = props.Key;
    state.UserID = props.UserID;
    state.Name = props.Name;
    state.Type = props.Type;
    state.Products = (props.Products === undefined) ? [] : props.Products.slice();
    state.OrgUnits = (props.OrgUnits === undefined) ? [] : props.OrgUnits.slice();
    state.Profile = (props.Profile === undefined) ? {} : props.Profile;

    Hub.$emit(EVENT_USER_CHANGED);
    Hub.$emit(EVENT_ORG_UNITS_CHANGED);
    Hub.$emit(EVENT_PRODUCTS_CHANGED);
  },

  SET_ORG_UNITS (state, props) {
    state.OrgUnits = props.slice();

    Hub.$emit(EVENT_ORG_UNITS_CHANGED);
  },

  CLEAR_USER_DATA (state) {
    state.Key = '';
    state.UserID = '';
    state.Name = '';
    state.Type = '';
    state.Products = [];
    state.OrgUnits = [];
    state.Profile = {};

    Hub.$emit(EVENT_USER_CHANGED);
    Hub.$emit(EVENT_ORG_UNITS_CHANGED);
    Hub.$emit(EVENT_PRODUCTS_CHANGED);
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
