import StandardRequest from '@/api/StandardRequest.js';

import {
  get,
  forEach,
  toNumber
} from 'lodash-es';

const state = {};

const getters = {};

const actions = {
  USERVIEW__getAvaliableColumns (context, props) {
    new StandardRequest(context, {
      noun: 'UserView',
      verb: 'GetAvailableGridColumns',
      data: {
        View: props.viewKey,
        Grid: props.gridKey
      }
    })
    .responseMiddleware(response => {
      forEach(response, column => {
        column.Key = toNumber(column.Key);
        column.Type = toNumber(column.Type);
      });
    })
    .success(props.callback);
  },

  USERVIEW__getAvaliableFields (context, props) {
    new StandardRequest(context, {
      noun: 'UserView',
      verb: 'GetAvailableGridFields',
      data: {
        View: props.viewKey,
        Grid: props.gridKey
      }
    })
    .success(props.callback);
  },

  USERVIEW__getList (context, props) {
    new StandardRequest(context, {
      noun: 'UserView',
      verb: 'GetList',
      data: {
        View: props.viewKey,
        IgnoreDefault: get(props, 'ignoreDefault', true), // If true then the users default view is excluded
        OrgUnit: get(props, 'orgUnit', '')
      }
    })
    .success(props.callback);
  },

  // USERVIEW__saveViews (context, props) {
  //   new StandardRequest(context, {
  //     noun: 'UserView',
  //     verb: 'Update',
  //     data: {
  //       OverwriteUpdate: get(props, 'force', false), // If true then the object will be updated even if it has been updated by someone else since last read
  //       lKey: props.key,
  //       lView: props.viewKey,
  //       lUser: props.userKey,
  //       lOrganizationalUnitKey: get(props, 'orgUnit', ''),
  //       sName: props.name,
  //       sDescription: get(props, 'description', '')
  //     }
  //   })
  //   .success(props.callback);
  // },

  USERVIEW__delete (context, props) {
    new StandardRequest(context, {
      noun: 'UserView',
      verb: 'Delete',
      data: {
        OverwriteUpdate: get(props, 'force', false), // If true then the object will be updated even if it has been updated by someone else since last read
        lKey: props.key
      }
    })
    .success(props.callback);
  },

  USERVIEW__deleteDefaultView (context, props) {
    new StandardRequest(context, {
      noun: 'UserView',
      verb: 'DeleteDefaultView',
      data: {
        View: props.key
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
