import { get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  LOT__getTypes (context, props) {
    new StandardRequest(context, {
      noun: 'StorageLot',
      verb: 'GetTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
