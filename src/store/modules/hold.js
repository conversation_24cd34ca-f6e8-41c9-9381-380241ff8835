import { forEach } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';

const state = {};

const getters = {};

const actions = {
  HOLD__getReasons (context, props) {
    new StandardRequest(context, {
      noun: 'Hold',
      verb: 'GetReasons',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      forEach(response, reason => {
        reason.Key = Number(reason.Key);
      });
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
