import { get, forEach } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  RECORD__getData (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetRecordViewData',
      data: props.data
    })
    .success(props.callback);
  },

  RECORD__getSettings (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetRecordViewSettings',
      data: {
        UserViewKey: get(props, 'userViewKey', '')
      }
    })
    .success(props.callback);
  },

  RECORD__create (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'Create',
      data: props.data
    })
    .success(props.callback)
    .fail(props.fail);
  },

  RECORD__update (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'Update',
      lastRead: get(props, 'lastRead', ''),
      data: props.data
    })
    .success(props.callback)
    .fail(props.fail);
  },

  RECORD__delete (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'Delete',
      data: props.data
    })
    .success(props.callback);
  },

  RECORD__undelete (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'Undelete',
      data: props.data
    })
    .success(props.callback);
  },

  RECORD__getMetadata (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetMetadata',
      data: { Key: props.key }
    })
    .success(props.callback);
  },

  RECORD__getDefaults (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetNewDefaults',
      data: props.data
    })
    .success(props.callback);
  },

  RECORD__getSubterminals (context, props) {
    new CacheableRequest(context, {
      noun: props.noun,
      verb: 'GetSubterminals'
    })
    .success(props.callback);
  },

  RECORD__createFilterClauses (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'CreateFilterClauses',
      data: props.data
    })
    .responseMiddleware(response => {
      forEach(response, clause => {
        clause.And = clause.And === 'true';
        clause.Or = clause.Or === 'true';
        clause.Not = clause.Not === 'true';
        clause.OpenParen = clause.OpenParen === 'true';
        clause.FieldID = clause.FieldID;
        clause.FieldName = clause.FieldName;
        clause.Operator = clause.Operator;
        clause.Value = clause.Value;
        clause.DisplayValue = clause.DisplayValue;
        clause.CloseParen = clause.CloseParen === 'true';
      });
    })
    .success(props.callback);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
