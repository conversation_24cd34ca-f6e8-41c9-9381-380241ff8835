import PDFRequest from '@/api/PDFRequest.js';
import CSVFileRequest from '@/api/CSVFileRequest.js';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

import {
  get,
  find,
  isEmpty,
  forEach,
  castArray
} from 'lodash-es';

const state = {
  // @TODO
  // Clear viewSettings on beforeDestroy()?
  viewSettings: {
    Key: 0,
    Grids: [{ Limit: 0 }]
  }
};

const getters = {
  RECORDS__settings: state => {
    // Clone pure object while dropping the hooks that <PERSON><PERSON><PERSON> has in place.
    // This allows me to mutate the object without violating the
    // direct state mutation restriction. Not generally advised.
    return JSON.parse(JSON.stringify(state.viewSettings));
  }
};

const mutations = {
  STORE_VIEW_SETTINGS (state, props = null) {
    if (isEmpty(props)) {
      props = {
        Key: 0,
        Grids: [{ Limit: 0 }]
      };
    }

    state.viewSettings = props;
  }
};

const actions = {
  RECORDS__update (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'Update',
      data: props.data
    })
    .success(props.callback);
  },

  RECORDS__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: props.noun,
      verb: 'GetStatuses',
      data: {}
    })
    .success(props.callback);
  },

  RECORDS__getData (context, props) {
    let requestData = {
      Key: props.data.Key,
      User: context.rootState.user.Key,
      Description: props.data.Description,
      Grids: castArray(props.data.Grids)
    };

    // Append additional parameters to be sent in the data section
    if (!isEmpty(props.dataAdditional)) {
      forEach(props.dataAdditional, function (value, key) {
        requestData[key] = value;
      });
    }

    if ('StepKey' in props.data) {
      requestData.StepKey = props.data.StepKey;
    }

    if ('ToolID' in props.data) {
      requestData.ToolID = props.data.ToolID;
    }

    if ('isReadyCallsVisible' in props.data) {
      requestData.bShowCallsReadyToProcess = props.data.isReadyCallsVisible;
    }

    if ('driverKey' in props) {
      requestData.DriverKey = props.driverKey;
    }

    if ('employeeKey' in props) {
      requestData.EmployeeKey = props.employeeKey;
    }

    if ('gridKey' in props) {
      requestData.GridKey = props.gridKey;
    }

    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetRecordsViewData',
      data: requestData
    })
    .responseMiddleware(response => {
      const records = response.Grids ? castArray(response.Grids) : [response];

      records.forEach(grid => {
        const items = Array.isArray(grid) ? grid : [grid];

        items.forEach(column => {
          if ('bActive' in column) column.bActive = column.bActive === '1';
          if ('bInventoried' in column) column.bInventoried = column.bInventoried === '1';
          if ('bRetow' in column) column.bRetow = column.bRetow === '1';
          if ('bTow' in column) column.bTow = column.bTow === '1';
          if ('lCallKey' in column) column.lCallKey = Number(column.lCallKey);
          if ('lBarCode' in column) column.lBarCode = column.lBarCode === null ? null : Number(column.lBarCode);
          if ('tcAppliedAmount' in column) column.tcAppliedAmount = column.tcAppliedAmount === '' ? null : Number(column.tcAppliedAmount);
          if ('tcBalance' in column) column.tcBalance = column.tcBalance === '' ? null : Number(column.tcBalance);
          if ('tcPaymentAmount' in column) column.tcPaymentAmount = column.tcPaymentAmount === '' ? null : Number(column.tcPaymentAmount);
          if ('tcPrice' in column) column.tcPrice = column.tcPrice === '' ? null : Number(column.tcPrice);
          if ('bReconcilable' in column) column.bReconcilable = column.bReconcilable === '1';
          if ('bConfirmable' in column) column.bConfirmable = column.bConfirmable === '1';
        });
      });
    })
    .success(props.callback)
    .fail(props.failCallback);
  },

  RECORDS__getSettings (context, props) {
    let viewKey = get(props, 'userViewKey', '');

    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetRecordsViewSettings',
      data: {
        UserViewKey: viewKey
      }
    })
    .responseMiddleware(response => {
      if ('Type' in response) response.Type = Number(response.Type);
      if ('AccessRight' in response) response.AccessRight = Number(response.AccessRight);

      forEach(response.Grids, grid => {
        if ('Key' in grid) grid.Key = Number(grid.Key);
        if ('Limit' in grid) grid.Limit = Number(grid.Limit);
        if ('NumPerPage' in grid) grid.NumPerPage = Number(grid.NumPerPage);

        forEach(grid.Columns, column => {
          if ('Key' in column) column.Key = Number(column.Key);
          if ('Ordered' in column) column.Ordered = column.Ordered === 'true';
          if ('Ascending' in column) column.Ascending = column.Ascending === 'true';
          if ('Pinned' in column) column.Pinned = column.Pinned === 'true';
          if ('Width' in column) column.Width = Number(column.Width);
          if ('OrderPriority' in column) column.OrderPriority = Number(column.OrderPriority);
          if ('Type' in column) column.Type = Number(column.Type);
        });

        // Use API-provided filters when loading a saved view.
        // Otherwise, clear them in favor of client-side management.
        if (!viewKey) {
          let localFilter = find(context.rootState.cachedFilters, filter => {
            return Number(filter.gridKey) === Number(grid.Key);
          });

          if (!isEmpty(localFilter)) {
            grid.Filters = get(localFilter, 'content', []);
          }
        }

        context.commit('REMOVE_CACHED_FILTER', grid.Key);
        context.commit('ADD_CACHED_FILTER', {
          gridKey: grid.Key,
          content: grid.Filters
        });
      });
    })
    .success(props.callback);
  },

  RECORDS__cacheSettings (context, props) {
    context.commit('STORE_VIEW_SETTINGS', props);
  },

  RECORDS__exportCSV (context, props) {
    new CSVFileRequest(context, {
      noun: props.noun,
      verb: 'GetRecordsViewDataAsCSVFile',
      data: {
        Key: props.viewKey,
        GridKey: props.gridKey,
        ShowInactive: get(props, 'showInactive', false),
        User: context.rootState.user.Key,
        Name: props.name,
        Description: props.description,
        Grids: props.grids,
        StepKey: get(props, 'stepKey', '')
      }
    })
    .respondAs('CSV')
    .success(props.callback);
  },

  RECORDS__exportPDF (context, props) {
    new PDFRequest(context, {
      noun: props.noun,
      verb: 'GetRecordsViewDataAsPDF',
      data: {
        Key: props.viewKey,
        GridKey: props.gridKey,
        ShowInactive: get(props, 'showInactive', false),
        User: context.rootState.user.Key,
        Name: props.name,
        Description: props.description,
        Grids: props.grids,
        StepKey: get(props, 'stepKey', '')
      }
    })
    .respondAs('PDF')
    .success(props.callback);
  },

  RECORDS__saveSettings (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'SaveRecordsViewSettings',
      data: {
        UserViewKey: get(props, 'data.UserViewKey', ''), // Empty = Default view, -1 = New view
        View: props.data.Key,
        Name: get(props, 'data.Name', '') || get(props, 'data.Title', ''), // Required if userViewKey == -1
        Description: get(props, 'data.Description', ''),
        OrgUnit: get(props, 'data.OrgUnit', ''), // Not Empty = Saved as Group Settings
        Grids: props.data.Grids
      }
    })
    .responseMiddleware(response => {
      forEach(response.Grids, grid => {
        context.commit('REMOVE_CACHED_FILTER', grid.Key);
        context.commit('ADD_CACHED_FILTER', {
          gridKey: grid.Key,
          content: grid.Filters
        });
      });
    })
    .success(props.callback);
  },

  RECORDS__getFilterPresets (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetFilterValueOptions',
      data: {
        Grid: props.gridKey,
        FieldID: props.field
      }
    })
    .success(props.callback);
  },

  RECORDS__getQuickSearchColumns (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'GetQuickSearchColumns',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
        item.Type = Number(item.Type);
      });
    })
    .success(props.success);
  },

  RECORDS__createQuickSearch (context, props) {
    new StandardRequest(context, {
      noun: props.noun,
      verb: 'CreateQuickSearch',
      data: {
        Key: props.columnKey,
        ID: props.columnId,
        Value: props.value
      }
    })
    .success(props.success);
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
