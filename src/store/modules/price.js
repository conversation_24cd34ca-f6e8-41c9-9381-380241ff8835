import { get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';

const state = {
  activeTab: 'services',
  activeServiceKey: null,
  activeCustomerKey: null,
  serviceFilter: null,
  serviceVariant: 'full', // ['full', 'compact', 'minimal']
  customerFilter: null,
  serviceSketchIsVisible: false,

  services: [],
  customers: [],
  unitTypes: []
};

const getters = {
  'price.activeService' (state) {
    if (!state.services) return {};
    if (!state.activeServiceKey) return {};

    return state.services.find(service => service.Key === state.activeServiceKey);
  },

  'price.activeCustomer' (state) {
    if (!state.customers) return {};
    if (!state.activeCustomerKey) return {};

    return state.customers.find(customer => customer.Key === state.activeCustomerKey);
  }
};

const actions = {
  PRICE__getServices (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetServices',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PRICE__getCustomers (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetCustomers',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PRICE__getPricingTypes (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetPricingTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PRICE__getUnitTypes (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetUnitTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        ServiceKey: get(props, 'serviceKey', '')
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PRICE__getTowClasses (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetTowClasses',
      data: {}
    })
    .responseMiddleware(response => {
      // if (has(response, 'bNoCharge')) response.bNoCharge = response.bNoCharge === '1';
    })
    .success(props.success);
  },

  PRICE__getFilteredServices (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetFilteredServices',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
        if ('Calculated' in item) item.Calculated = item.Calculated === 'true';
      });
    })
    .success(props.success);
  },

  PRICE__getServiceDetails (context, props) {
    new StandardRequest(context, {
      noun: 'Price',
      verb: 'GetServiceDetails',
      data: {
        Key: props.key
      }
    })
    .responseMiddleware(response => {
      if ('Key' in response) response.Key = Number(response.Key);
      if ('LienService' in response) response.LienService = response.LienService === 'true';
      if ('NoNegativePrice' in response) response.NoNegativePrice = response.NoNegativePrice === 'true';
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
