import tops from '@/utils/tops.js';
import { commonResponseTransforms } from '@/utils/responseTransformers.js';

const state = {
  makes: [],
  models: [],
  isLoading: false,
  error: null
};

const getters = {
  isDataLoaded: state => state.makes.length > 0 && state.models.length > 0,

  makeModels: state => {
    return state.models
      .filter(model => model.Name && model.Name.trim())
      .map(model => {
        const make = state.makes.find(mk => mk.Key === model.Make) || {};
        const makeName = make.Value || 'Unknown Make';
        const modelName = model.Name || 'Unknown Model';

        return {
          makeKey: make.Key,
          modelKey: model.Key,
          key: `${make.Key}-${model.Key}`,
          description: `${makeName} ${modelName}`,
          shortCode: modelName.substr(0, 10).toUpperCase(),
          active: make.Active !== false // Default to true if not specified
        };
      })
      .filter(item => item.active)
      .sort((a, b) => a.description.localeCompare(b.description));
  }
};

const actions = {
  async VEHICLE__hydrate({ state, getters }) {
    if (getters.isDataLoaded) return;

    state.isLoading = true;
    state.error = null;

    try {
      const [makesResponse, modelsResponse] = await Promise.all([
        tops.Call.GetMakes({
          Parameters: {
            IncludeHeader: false,
            NameValuePairs: true
          }
        }).transformResponse(commonResponseTransforms.vehicleMakes),

        tops.Call.GetAllModels({}).transformResponse(commonResponseTransforms.vehicleModels)
      ]);

      state.makes = makesResponse.data.Data || makesResponse.data;
      state.models = modelsResponse.data.Data || modelsResponse.data;

    } catch (error) {
      state.error = error.message || 'Failed to load vehicle data';
      throw error;
    } finally {
      state.isLoading = false;
    }
  }
};

export default {
  namespaced: true,
  state,
  getters,
  actions
};
