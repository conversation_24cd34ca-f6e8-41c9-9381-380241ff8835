import { get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  TOWTICKET__getStatuses (context, props) {
    new CacheableRequest(context, {
      noun: 'TowTicket',
      verb: 'GetStatuses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.success);
  },

  TOWTICKET__create (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchCreate',
      data: {
        Prefix: get(props, 'prefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__changePrefix (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchChangePrefix',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        ChangePrefixTo: get(props, 'newPrefix', ''),
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__assignDriver (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchDriverAssign',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        DriverKey: get(props, 'driverKey', ''),
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__assignLot (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchLotAssign',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        LotKey: get(props, 'lotKey', ''),
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__assignCompany (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchSubterminalAssign',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        SubterminalKey: get(props, 'companyKey', ''),
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__void (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchVoid',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        VoidReason: get(props, 'voidReason', ''),
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  },

  TOWTICKET__unvoid (context, props) {
    new StandardRequest(context, {
      noun: 'TowTicket',
      verb: 'BatchUnvoid',
      data: {
        Prefix: get(props, 'targetPrefix', ''),
        StartNumber: props.startNumber,
        Quantity: props.quantity,
        Continue: get(props, 'continue', false)
      }
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
