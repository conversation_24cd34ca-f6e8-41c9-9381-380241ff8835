import tops from '@/utils/tops';
import { commonResponseTransforms } from '@/utils/responseTransformers.js';

const state = {
  jobs: [],
  messages: [],
  selectedTab: 'job-offers',
  loading: false,
  error: null
};

const getters = {
  newJobs: (state) => state.jobs.filter(job => job.New),
  newJobsAndMessages: (state) => state.jobs.filter(job => job.New).length + state.messages.length,
  hasError: (state) => Boolean(state.error),
};

const mutations = {
  setJobs(state, jobs) {
    state.jobs = jobs;
  },
  setMessages(state, messages) {
    state.messages = messages;
  },
  setLoading(state, loading) {
    state.loading = loading;
  },
  setError(state, error) {
    state.error = error;
  },
  clearError(state) {
    state.error = null;
  }
};

const actions = {
  async fetchNewOffers({ commit }) {
    commit('clearError');
    commit('setLoading', true);

    try {
      const response = await tops.Dispatches.GetNewJobsAndMessages({}, { cancelPrevious: true })
        .transformResponse(commonResponseTransforms.jobsAndMessages)
        .send();

      const data = response.data?.Data;
      if (!data) {
        throw new Error('Invalid jobs offers structure');
      }

      const { Jobs = [], Messages = [] } = data;

      commit('setJobs', Jobs);
      commit('setMessages', Messages);

      return { Jobs, Messages };
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch job offers';
      commit('setError', errorMessage);
      throw error;
    } finally {
      commit('setLoading', false);
    }
  },

  async getJobDetails({ commit }, jobKey) {
    try {
      const response = await tops.Job.GetDetails({ Key: jobKey })
        .transformResponse(commonResponseTransforms.jobDetails)
        .send();

      const data = response.data?.Data;
      if (!data) {
        throw new Error('Invalid job details structure');
      }

      return data;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch job details';
      commit('setError', errorMessage);
      throw error;
    }
  },

  async getJobActions({ commit }, jobKey) {
    try {
      const response = await tops.Job.GetPossibleActions({ Key: jobKey })
        .transformResponse(commonResponseTransforms.jobActions)
        .send();
      return response.data.Data?.Actions;
    } catch (error) {
      const errorMessage = error.message || 'Failed to fetch job actions';
      commit('setError', errorMessage);
      throw error;
    }
  },

  async handleJobAction({ commit }, payload) {
    try {
      // Extract core required fields
      const { jobKey, responseTag, responseValue, ...additionalData } = payload;

      // Build the API payload with core fields and any additional data
      const apiPayload = {
        Key: jobKey,
        ResponseTag: responseTag,
        ResponseValue: responseValue,
        ...additionalData
      };

      const response = await tops.Job.HandleAction(apiPayload);

      return response;
    } catch (error) {
      const errorMessage = error.message || 'Failed to handle job action';
      commit('setError', errorMessage);
      throw error;
    }
  },

  async acknowledgeMessage({ commit }, messageKey) {
    try {
      const response = await tops.Message.Acknowledge({ Key: messageKey });
      if (response.data.Result === 'ERROR') throw response.data.Error.Message;

      const index = state.messages.findIndex(msg => msg.Key === messageKey);
      if (index !== -1) state.messages.splice(index, 1);

      return response;
    } catch (error) {
      const errorMessage = error.message || 'Failed to acknowledge message';
      commit('setError', errorMessage);
      throw error;
    }
  },

  clearError({ commit }) {
    commit('clearError');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
