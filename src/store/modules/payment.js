import { castArray, get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {
  customers: [],
  subcompanies: [],

  selectedPaymentKey: '',
  selectedCustomerKey: '',
  selectedSubcompanyKey: '',
  selectedSubcompany: {},

  isTowPayAvailable: false,
  filterCallsByCustomer: true,
  selectNewlyAddedPayment: true,
  rejectZeroBalances: true,
  rejectUnconfirmed: true,
  showDeletedPayments: false
};

const getters = {};

const actions = {
  PAYMENT__getCreditCardTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Payment',
      verb: 'GetCreditCardType',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  PAYMENT__getPaymentTypes (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'GetPaymentType',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  PAYMENT__getReceiptTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Payment',
      verb: 'GetReceiptType',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.callback);
  },

  PAYMENT__getProcessingTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Payment',
      verb: 'GetProcessingTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  PAYMENT__getCardStatusTypes (context, props) {
    new CacheableRequest(context, {
      noun: 'Payment',
      verb: 'GetCardStatusTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .success(props.callback);
  },

  PAYMENT__getCustomers (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'GetCustomers',
      data: {}
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PAYMENT__getApplications (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'GetApplications',
      data: {
        Key: get(props, 'key', '')
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  },

  PAYMENT__applyToCall (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'ApplyPaymentToCalls',
      data: {
        Key: props.key,
        Calls: castArray(props.call)
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  PAYMENT__createOverpaymentInvoice (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'CreateOverpaymentInvoice',
      data: {
        Key: props.key,
        Amount: props.amount
      }
    })
    .success(props.success);
  },

  PAYMENT__getCCPaymentInfo (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'GetCCPaymentInfo',
      data: {
        SubterminalKey: props.subterminalKey
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  PAYMENT__sendLinkToPay (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'SendLinkToPay',
      data: {
        SubterminalKey: props.subterminalKey,
        CustomerKey: props.customerKey,
        EmailPhone: props.emailPhone,
        Amount: props.amount,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  PAYMENT__sendTowPayIntentToReader (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'SendTowPayIntentToReader',
      data: {
        SubterminalKey: props.subterminalKey,
        CustomerKey: props.customerKey,
        ReaderID: props.readerID,
        ReaderType: props.readerType,
        Amount: props.amount,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  PAYMENT__cancelTowPayReaderIntent (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'CancelTowPayReaderIntent',
      data: {
        IntentID: props.intentID,
        ReaderID: props.readerID,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG'
      }
    })
    .responseMiddleware(response => {
      // ...
    })
    .success(props.success);
  },

  PAYMENT__createTowPayNotPresentPayment (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'CreateTowPayNotPresentPayment',
      lastRead: props.lastRead,
      data: {
        SubterminalKey: props.subterminalKey,
        CustomerKey: props.customerKey,
        Amount: props.amount,
        PaymentMethodID: props.paymentMethodId,
        Name: props.name,
        Brand: props.brand,
        Expiry: props.expiry,
        LastFour: props.lastFour,
        Note: props.note,
        Debug: import.meta.env.VITE_APP_MODE === 'DEBUG',
        ReceiptType: props.receiptType,
        EmployeeKey: props.employeeKey
      }
    })
    .responseMiddleware(response => {
      if ('PaymentKey' in response) response.PaymentKey = Number(response.PaymentKey);
    })
    .success(props.success)
    .fail(props.fail);
  },

  PAYMENT__getSubterminals (context, props) {
    new StandardRequest(context, {
      noun: 'Payment',
      verb: 'GetSubterminals',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        if ('Key' in item) item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
