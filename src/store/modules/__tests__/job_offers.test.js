import { describe, it, expect, vi, beforeEach } from 'vitest';
import jobOffersModule from '../job_offers.js';

// Mock the tops API
const mockSend = vi.fn();
const mockTransformResponse = vi.fn(() => ({ send: mockSend }));

vi.mock('@/utils/tops', () => ({
  default: {
    Dispatches: {
      GetNewJobsAndMessages: vi.fn(() => ({
        transformResponse: mockTransformResponse
      }))
    },
    Job: {
      GetDetails: vi.fn(() => ({
        transformResponse: mockTransformResponse
      })),
      GetPossibleActions: vi.fn(() => ({
        transformResponse: mockTransformResponse
      })),
      HandleAction: vi.fn()
    },
    Message: {
      Acknowledge: vi.fn()
    }
  }
}));

// Mock response transformers
vi.mock('@/utils/responseTransformers.js', () => ({
  commonResponseTransforms: {
    jobsAndMessages: {},
    jobDetails: {},
    jobActions: {}
  }
}));

describe('job_offers store module', () => {
  let store;
  let mockCommit;

  beforeEach(() => {
    vi.clearAllMocks();
    store = jobOffersModule;
    mockCommit = vi.fn();
  });

  describe('fetchNewOffers action', () => {
    it('should handle successful response with valid data', async () => {
      const mockResponse = {
        data: {
          Data: {
            Jobs: [{ Key: 1, Name: 'Test Job' }],
            Messages: [{ Key: 1, Text: 'Test Message' }]
          }
        }
      };

      mockSend.mockResolvedValue(mockResponse);

      const result = await store.actions.fetchNewOffers({ commit: mockCommit });

      expect(mockCommit).toHaveBeenCalledWith('clearError');
      expect(mockCommit).toHaveBeenCalledWith('setLoading', true);
      expect(mockCommit).toHaveBeenCalledWith('setJobs', mockResponse.data.Data.Jobs);
      expect(mockCommit).toHaveBeenCalledWith('setMessages', mockResponse.data.Data.Messages);
      expect(mockCommit).toHaveBeenCalledWith('setLoading', false);
      expect(result).toEqual({
        Jobs: mockResponse.data.Data.Jobs,
        Messages: mockResponse.data.Data.Messages
      });
    });

    it('should handle response with missing Data property', async () => {
      const mockResponse = {
        data: {} // Missing Data property
      };

      mockSend.mockResolvedValue(mockResponse);

      await expect(store.actions.fetchNewOffers({ commit: mockCommit }))
        .rejects.toThrow('Invalid response structure: missing Data property');

      expect(mockCommit).toHaveBeenCalledWith('clearError');
      expect(mockCommit).toHaveBeenCalledWith('setLoading', true);
      expect(mockCommit).toHaveBeenCalledWith('setError', 'Invalid response structure: missing Data property');
      expect(mockCommit).toHaveBeenCalledWith('setLoading', false);
    });

    it('should handle response with null data', async () => {
      const mockResponse = {
        data: {
          Data: null
        }
      };

      mockSend.mockResolvedValue(mockResponse);

      await expect(store.actions.fetchNewOffers({ commit: mockCommit }))
        .rejects.toThrow('Invalid response structure: missing Data property');
    });

    it('should handle response with undefined Data', async () => {
      const mockResponse = {
        data: {
          Data: undefined
        }
      };

      mockSend.mockResolvedValue(mockResponse);

      await expect(store.actions.fetchNewOffers({ commit: mockCommit }))
        .rejects.toThrow('Invalid response structure: missing Data property');
    });

    it('should use default empty arrays when Jobs or Messages are missing', async () => {
      const mockResponse = {
        data: {
          Data: {} // Missing Jobs and Messages
        }
      };

      mockSend.mockResolvedValue(mockResponse);

      const result = await store.actions.fetchNewOffers({ commit: mockCommit });

      expect(mockCommit).toHaveBeenCalledWith('setJobs', []);
      expect(mockCommit).toHaveBeenCalledWith('setMessages', []);
      expect(result).toEqual({
        Jobs: [],
        Messages: []
      });
    });

    it('should handle API errors', async () => {
      const mockError = new Error('API Error');
      mockSend.mockRejectedValue(mockError);

      await expect(store.actions.fetchNewOffers({ commit: mockCommit }))
        .rejects.toThrow('API Error');

      expect(mockCommit).toHaveBeenCalledWith('setError', 'API Error');
      expect(mockCommit).toHaveBeenCalledWith('setLoading', false);
    });
  });

  describe('getJobDetails action', () => {
    it('should handle successful response with valid data', async () => {
      const mockResponse = {
        data: {
          Data: { Key: 1, Name: 'Test Job Details' }
        }
      };

      mockSend.mockResolvedValue(mockResponse);

      const result = await store.actions.getJobDetails({ commit: mockCommit }, 1);

      expect(result).toEqual(mockResponse.data.Data);
    });

    it('should handle response with missing Data property', async () => {
      const mockResponse = {
        data: {} // Missing Data property
      };

      mockSend.mockResolvedValue(mockResponse);

      await expect(store.actions.getJobDetails({ commit: mockCommit }, 1))
        .rejects.toThrow('Invalid response structure: missing Data property');

      expect(mockCommit).toHaveBeenCalledWith('setError', 'Invalid response structure: missing Data property');
    });
  });
});
