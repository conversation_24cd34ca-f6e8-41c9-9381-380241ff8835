import { get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';
import CacheableRequest from '@/api/CacheableRequest.js';

const state = {};

const getters = {};

const actions = {
  CONTACT__getTypes (context, props) {
    new StandardRequest(context, {
      noun: 'Contact',
      verb: 'GetTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
      });
    })
    .success(props.success);
  }
};

const mutations = {};

export default {
  state,
  getters,
  actions,
  mutations
};
