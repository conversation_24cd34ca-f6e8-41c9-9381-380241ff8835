import is from 'is_js';
import Hub from '../../events/hub';
import { get, toUpper } from 'lodash-es';
import projectPackage from '../../../package.json';
import { EVENT_INSTANCE_CHANGED } from '@/config.js';
import StandardRequest from '@/api/StandardRequest.js';

const state = {
  Key: '',
  Authentication: ''
};

const getters = {
  INSTANCE__isValid: state => is.all.truthy(state.Key, state.Authentication),
  INSTANCE__key: state => state.Key
};

const actions = {
  INSTANCE__kill (context, props) {
    new StandardRequest(context, {
      noun: 'Instance',
      verb: 'Delete',
      data: {
        OverwriteUpdate: true,
        biInstanceKey: state.Key
      }
    })
    .success(props.callback);
  },

  INSTANCE__setWindowName (context) {
    context.commit('SET_WINDOW_NAME');
  }
};

const mutations = {
  SET_INSTANCE_DATA (state, props) {
    state.Key = get(props, 'Key', state.Key);
    state.Authentication = get(props, 'Authentication', state.Authentication);

    Hub.$emit(EVENT_INSTANCE_CHANGED);
  },

  CLEAR_INSTANCE_DATA (state) {
    state.Key = '';
    state.Authentication = '';
    Hub.$emit(EVENT_INSTANCE_CHANGED);
  },

  SET_WINDOW_NAME () {
    // @Developer
    // Supports simultaneous sessions in multiple browser tabs. Useful
    // for targeting this tab with <a target="instance_key">.
    window.name = toUpper(projectPackage.name) + state.Key;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
