import { get } from 'lodash-es';
import StandardRequest from '@/api/StandardRequest.js';

const state = {
  options: [],
  isLoading: false,
  isLoaded: false
};

const getters = {
  TOWTYPE__allOptions: state => state.options,

  TOWTYPE__activeOptions: state => state.options.filter(option => option.Active),

  TOWTYPE__isLoading: state => state.isLoading,

  TOWTYPE__isLoaded: state => state.isLoaded,

  TOWTYPE__isDropoff: state => towTypeKey => {
    if (!towTypeKey) return false;

    const dropoffTowType = state.options.find(option =>
      option.Value && option.Value.toLowerCase() === 'dropoff'
    );

    return dropoffTowType && Number(towTypeKey) === Number(dropoffTowType.Key);
  }
};

const actions = {
  TOWTYPE__getClasses (context, props) {
    new StandardRequest(context, {
      noun: 'TowType',
      verb: 'GetTowClasses',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        Filters: get(props, 'filters', [])
      }
    })
    .success(props.callback);
  },

  TOWTYPE__getAll (context, props) {
    context.commit('TOWTYPE__SET_LOADING', true);

    new StandardRequest(context, {
      noun: 'Call',
      verb: 'GetTowTypes',
      data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        }
      }
    })
    .responseMiddleware(response => {
      response.forEach(item => {
        item.Key = Number(item.Key);
        item.Active = Number(item.Active) === 1;
      });

      context.commit('TOWTYPE__SET_OPTIONS', response);
      context.commit('TOWTYPE__SET_LOADED', true);
      context.commit('TOWTYPE__SET_LOADING', false);
    })
    .success(props ? props.callback : null);
  }
};

const mutations = {
  TOWTYPE__SET_OPTIONS (state, options) {
    state.options = options;
  },

  TOWTYPE__SET_LOADING (state, isLoading) {
    state.isLoading = isLoading;
  },

  TOWTYPE__SET_LOADED (state, isLoaded) {
    state.isLoaded = isLoaded;
  }
};

export default {
  state,
  getters,
  actions,
  mutations
};
