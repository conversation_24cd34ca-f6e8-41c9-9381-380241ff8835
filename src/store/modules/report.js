import { get } from 'lodash-es';
import { prepareApiData } from '@/utils/filters.js';
import StandardRequest from '@/api/StandardRequest.js';

const state = {
  searchTerm: '',
  selectedReportKey: '',
  reports: [],
  showDescriptions: false
};

const actions = {
  REPORT__getData (context, props) {
    new StandardRequest(context, {
      noun: 'Report',
      verb: 'GetReportData',
      data: {
        Key: props.reportKey,
        Associative: props.withLabels, // Boolean
        ResponseType: get(props, 'respondAs', 'JSON'), // ['JSON', 'CSV', 'CSVFile', 'PDF']
        Parameters: props.parameters // JSON Object
      }
    })
    .success(props.callback);
  },

  REPORT__getParameters (context, props) {
    new StandardRequest(context, {
      noun: 'Report',
      verb: 'GetReportParameters',
      data: {
        Key: props.reportKey
      }
    })
    .success(props.callback);
  },

  REPORT__getCreate (context, props) {
    return new StandardRequest(context, {
      noun: 'Report',
      verb: 'CreateReport',
      data: prepareApiData({
        Key: props.reportKey,
        Parameters: props.parameters
      }),
      isLazy: true
    })
    .render();
  },

  REPORT__GetEmailDefaults (context, props) {
    new StandardRequest(context, {
      noun: 'Report',
      verb: 'GetEmailDefaults',
      data: {
        Key: props.key
      }
    })
    .success(props.success);
  },

  REPORT__EmailReport (context, props) {
    new StandardRequest(context, {
      noun: 'Report',
      verb: 'EmailReport',
      data: {
        Key: props.key,
        To: props.to,
        ReplyTo: props.replyTo,
        Subject: props.subject,
        Body: props.body,
        EmailCustomers: props.emailCustomers,
        UseCompanyEmailAddresses: props.useCompanyEmailAddresses,
        Parameters: props.parameters
      }
    })
    .success(props.success);
  }
};

export default { state, actions };
