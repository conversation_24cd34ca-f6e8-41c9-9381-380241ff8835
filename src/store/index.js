import Vue from 'vue';
import Vuex from 'vuex';
import Hub from '../events/hub';
import * as actions from './actions';
import * as getters from './getters';
import { castArray, reject } from 'lodash-es';
import createPersistedState from 'vuex-persistedstate';

import lot from './modules/lot';
import map from './modules/map';
import user from './modules/user';
import role from './modules/role';
import call from './modules/call';
import hold from './modules/hold';
import lien from './modules/lien';
import image from './modules/image';
import label from './modules/label';
import truck from './modules/truck';
import stash from './modules/stash';
import quote from './modules/quote';
import price from './modules/price';
import driver from './modules/driver';
import record from './modules/record';
import report from './modules/report';
import contact from './modules/contact';
import license from './modules/license';
import records from './modules/records';
import utility from './modules/utility';
import vehicle from './modules/vehicle';
import payment from './modules/payment';
import towLien from './modules/towlien';
import addCall from './modules/add_call';
import towType from './modules/tow_type';
import checkout from './modules/checkout';
import customer from './modules/customer';
import dispatch from './modules/dispatch';
import employee from './modules/employee';
import instance from './modules/instance';
import location from './modules/location';
import userView from './modules/user_view';
import topsCall from './modules/tops_call';
import callTools from './modules/call_tools';
import orderLine from './modules/order_line';
import towStatus from './modules/tow_status';
import towTicket from './modules/tow_ticket';
import preference from './modules/preference';
import assignCall from './modules/assign_call';
import topsCompany from './modules/tops_company';
import txiCustomer from './modules/txi_customer';
import notification from './modules/notification';
import motorClubBilling from './modules/motor_club_billing';
import jobOffers from './modules/job_offers';

import {
  PRODUCTS,
  EVENT_PRODUCT_CHANGED,
  PRODUCTKEY_WEB_PORTAL,
  EVENT_API_MODE_CHANGED,
  EVENT_APP_MODE_CHANGED,
  EVENT_ORG_UNIT_CHANGED
} from '../config';

Vue.use(Vuex);

const state = {
  appMode: import.meta.env.VITE_APP_MODE,
  apiMode: import.meta.env.VITE_API_MODE,
  busy: false,
  orgUnitKey: '',
  currentView: '',
  cachedFilters: [],
  cachedRecords: [],
  selectedRecords: [],
  product: PRODUCTS[PRODUCTKEY_WEB_PORTAL] // Default product is the web portal
};

const mutations = {
  TOGGLE_BUSY(state) {
    state.busy = !state.busy;
  },

  SET_BUSY(state) {
    state.busy = true;
  },

  SET_NOT_BUSY(state) {
    state.busy = false;
  },

  SET_APP_MODE(state, value) {
    state.appMode = value; // [DEBUG, NORMAL, OFFLINE]
    Hub.$emit(EVENT_APP_MODE_CHANGED);
  },

  SET_API_MODE(state, value) {
    state.apiMode = value; // [Dev, Production, etc.]
    Hub.$emit(EVENT_API_MODE_CHANGED);
  },

  SET_PRODUCT(state, productKey) {
    state.product = PRODUCTS[productKey];
    Hub.$emit(EVENT_PRODUCT_CHANGED);
  },

  SET_ORG_UNIT(state, orgUnitKey) {
    state.orgUnitKey = orgUnitKey;

    if (!orgUnitKey) return;

    window.localStorage.setItem('last_company', orgUnitKey);

    Hub.$emit(EVENT_ORG_UNIT_CHANGED);
  },

  SET_SELECTED_RECORD(state, value) {
    state.selectedRecords = castArray(value);
  },

  CACHE_RECORDS(state, value = []) {
    state.cachedRecords = value;
  },

  SET_CURRENT_VIEW(state, value = '') {
    state.currentView = value;
  },

  ADD_CACHED_FILTER(state, value) {
    state.cachedFilters.push(value);
  },

  REMOVE_CACHED_FILTER(state, gridKey) {
    if (gridKey === '*') {
      state.cachedFilters = [];
      return;
    }

    state.cachedFilters = reject(state.cachedFilters, filter => {
      return filter.gridKey === gridKey;
    });
  }
};

export default new Vuex.Store({
  state,
  actions,
  getters,
  mutations,
  plugins: [createPersistedState({ storage: window.sessionStorage })],
  strict: false,
  modules: {
    lot,
    map,
    user,
    role,
    call,
    hold,
    lien,
    image,
    label,
    truck,
    driver,
    record,
    report,
    stash,
    quote,
    price,
    location,
    contact,
    license,
    records,
    payment,
    addCall,
    towLien,
    utility,
    vehicle,
    towType,
    checkout,
    customer,
    dispatch,
    employee,
    instance,
    topsCall,
    userView,
    callTools,
    orderLine,
    towStatus,
    towTicket,
    preference,
    assignCall,
    topsCompany,
    txiCustomer,
    notification,
    motorClubBilling,
    job_offers: jobOffers
  }
});
