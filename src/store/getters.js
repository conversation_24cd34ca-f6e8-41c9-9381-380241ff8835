import { get, find } from 'lodash-es';

export const __busy = state => state.busy;

export const __state = state => state;

export const __selectedRecords = state => state.selectedRecords;

export const __cachedRecords = state => state.cachedRecords;

export const __currentView = state => state.currentView;

export const __cachedFilters = state => state.cachedFilters;

export const __orgUnitName = state => {
  let org = find(state.user.OrgUnits, ['Key', state.orgUnitKey]);

  return get(org, 'Name', null);
};
