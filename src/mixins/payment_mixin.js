import {
  EVENT_OPEN_PAYMENTS,
  EVENT_CLOSE_PAYMENTS
} from '@/config';

import {  } from 'lodash-es';
let PaymentMixin = {
  data () {
    return {
      paymentsVisible: false
    };
  },

  methods: {
    $_PaymentMixin_togglePayments (value) {
      this.paymentsVisible = (typeof value === 'boolean') ? value : !this.paymentsVisible;
    },

    // Define methods for event handlers to make them easier to remove in beforeDestroy
    $_PaymentMixin_onOpenPayments () {
      this.$_PaymentMixin_togglePayments(true);
    },

    $_PaymentMixin_onClosePayments () {
      // Only call toggleActions if it exists in the current component
      if (typeof this.toggleActions === 'function') {
        this.toggleActions();
      }
    },
  },

  mounted () {
    this.$hub.$on(EVENT_OPEN_PAYMENTS, this.$_PaymentMixin_onOpenPayments);
    this.$hub.$on(EVENT_CLOSE_PAYMENTS, this.$_PaymentMixin_onClosePayments);
  },

  beforeDestroy () {
    // Clean up event listeners to prevent memory leaks and errors
    this.$hub.$off(EVENT_OPEN_PAYMENTS, this.$_PaymentMixin_onOpenPayments);
    this.$hub.$off(EVENT_CLOSE_PAYMENTS, this.$_PaymentMixin_onClosePayments);
  },
};

export default PaymentMixin;
