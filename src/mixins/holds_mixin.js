import {
  EVENT_OPEN_HOLDS,
  EVENT_CLOSE_HOLDS
} from '@/config';

import {  } from 'lodash-es';
let HoldsMixin = {
  data () {
    return {
      holdsVisible: false
    };
  },

  methods: {
    $_HoldsMixin_toggleHolds (value) {
      this.holdsVisible = (typeof value === 'boolean') ? value : !this.holdsVisible;
    },

    // Define methods for event handlers to make them easier to remove in beforeDestroy
    $_HoldsMixin_onOpenHolds () {
      this.$_HoldsMixin_toggleHolds(true);
    },

    $_HoldsMixin_onCloseHolds () {
      // Only call toggleActions if it exists in the current component
      if (typeof this.toggleActions === 'function') {
        this.toggleActions();
      }
    }
  },

  mounted () {
    this.$hub.$on(EVENT_OPEN_HOLDS, this.$_HoldsMixin_onOpenHolds);
    this.$hub.$on(EVENT_CLOSE_HOLDS, this.$_HoldsMixin_onCloseHolds);
  },

  beforeDestroy () {
    // Clean up event listeners to prevent memory leaks and errors
    this.$hub.$off(EVENT_OPEN_HOLDS, this.$_HoldsMixin_onOpenHolds);
    this.$hub.$off(EVENT_CLOSE_HOLDS, this.$_HoldsMixin_onCloseHolds);
  },
};

export default HoldsMixin;
