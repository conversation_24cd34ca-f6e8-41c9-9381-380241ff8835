.title-bar {
  display: grid;
  grid-area: 1 / 1;
  height: 4rem;
  background: var(--titlebar-bg);
  z-index: 15;

  .-left,
  .-center,
  .-right {
    grid-area: 1 / 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 1rem;
  }

  .-left {
    justify-content: flex-start;
    color: var(--accent);
    font-weight: bold;
    text-transform: uppercase;
    font-size: 1rem;
    letter-spacing: var(--font-letter-spacing);
  }

  .-center {
    justify-content: center;

    .button {
      color: var(--titlebar-fg);
      text-decoration: none;

      &:focus,
      &:active {
        background-color: var(--titlebar-fg-a2);
        box-shadow: none;
      }

      &.is-text {
        &:hover {
          background-color: var(--titlebar-fg-a2);
        }
      }
    }

    .-filters {
      position: relative;
    }
  }

  .-right {
    justify-content: flex-end;

    .button {
      color: var(--titlebar-fg);
      background-color: var(--titlebar-fg-a2);
    }

    > * {
      margin-left: 0.5rem;
    }
  }
}
