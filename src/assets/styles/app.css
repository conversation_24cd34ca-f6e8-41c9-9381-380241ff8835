@import './vendor/bulma-0.6.2.css';

@import './settings';
@import './colors';
@import './typography';

@import './defaults';
@import './forms';
@import './grid-form';
@import './grid';
@import './tables';
@import './transitions';
@import './titlebar';
@import './footerbar';
@import './dropdown';
@import './flyout-control';
@import './spotlight-control';
@import './loader-indicator';
@import './data-point';
@import './filters-glance';
@import './call-map';

@import './components/tabs';
@import './components/quick-views';
@import './components/daterangepicker';
@import './components/towpay';
@import './components/sell-vehicle';

@import './inputs/autocomplete';
@import './inputs/inputs';
@import './inputs/suggestion';
@import './inputs/customer';

@import './pages/checkout';
@import './pages/record-view';
@import './pages/records-view';
@import './pages/search-view';
@import './pages/columns';
@import './pages/sign-in';
@import './pages/search';
@import './pages/welcome';
@import './pages/prices-view';
@import './pages/payments-view';
@import './pages/call/images';
@import './pages/call/inventory';
@import './pages/call/lien';
@import './pages/call/lien-search';
@import './pages/call/notes';
@import './pages/call/miscellaneous';
@import './pages/call/payments';
@import './pages/call/pricing';
@import './pages/call/retow';
@import './pages/call/tow';
@import './pages/call/tow-settings';
@import './pages/call/dispatch';
@import './pages/call/holds';
@import './pages/call/customer';
@import './pages/call/assign';
@import './pages/call/contact';
@import './pages/call/accounting';
@import './pages/call/motor-club-billing';
@import './pages/call/mileage';

*,
*:before,
*:after {
  box-sizing: border-box;

  scrollbar-width: thin;
  scrollbar-color: hsl(0 0% 50%);
  scroll-behavior: smooth;

  @media (prefers-reduced-motion: reduce) {
    scroll-behavior: auto;
  }

  [id] {
    scroll-margin-top: 2ex;
  }
}

html,
body {
  scrollbar-width: none;
  color: var(--body-fg);
  background: var(--body-bg);
  overscroll-behavior: contain;
  overflow: hidden;
}

#standard-layout {
  scrollbar-width: none;
  display: flex;
  position: relative;
  overflow: hidden;

  > .toolbar-placeholder {
    width: var(--tool-bar-width);
  }

  > .router-view {
    scrollbar-width: none;
    flex: 1;
    width: 100vh;
    height: 100dvh;
    background: var(--body-bg);
    overflow: hidden;
  }

  > #modal-portal,
  > #date-picker-portal {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    overflow: hidden;
    visibility: hidden;
    z-index: var(--layer-modals);

    > .vue-portal-target {
      position: relative;

      width: 100%;
      height: 100%;

      > * {
        visibility: visible;
      }
    }
  }
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.requirement-status,
.is-quiet {
  color: var(--body-border);
}

.is-complete {
  color: var(--success) !important;
}

.v-space {
  height: 1rem;
}

.v-space-2 {
  height: 2rem;
}

.flex {
  display: flex;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 1rem;
}

.flex-space {
  flex: 1;
}

dialog {
  border: 2px solid var(--body-border);
  border-radius: 1rem;
  box-shadow: var(--box-shadow-100);

  &::backdrop {
    background-color: oklch(from var(--blue) l c h / 0.05);
  }
}
