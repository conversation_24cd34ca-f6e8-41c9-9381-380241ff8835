@import url('https://rsms.me/inter/inter.css');

:root {
  --font-face-text: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Helvetica, Arial, 'Noto Sans', 'Microsoft YaHei', 'Yu Gothic UI', sans-serif;
  --font-size-body: 10pt;

  @supports (font-variation-settings: normal) {
    --font-face-text: 'Inter var', sans-serif;
  }

  --font-line-length: 50em;
  --font-line-height-text: 1.5;
  --font-line-height-display: 1.1;
  --font-letter-spacing: 0.1em;

  /* --font-size-scale: 1.067;  */
  /* --font-size-scale: 1.125;  */
  /* --font-size-scale: 1.200;  */
  --font-size-scale: 1.250;
  /* --font-size-scale: 1.333;  */
  /* --font-size-scale: 1.414;  */
  /* --font-size-scale: 1.500;  */
  /* --font-size-scale: 1.618;  */

  --font-size-h5: calc(var(--font-size-body) * var(--font-size-scale));
  --font-size-h4: calc(var(--font-size-h5) * var(--font-size-scale));
  --font-size-h3: calc(var(--font-size-h4) * var(--font-size-scale));
  --font-size-h2: calc(var(--font-size-h3) * var(--font-size-scale));
  --font-size-h1: calc(var(--font-size-h2) * var(--font-size-scale));

  --font-size-lead3: calc(var(--font-size-h1) * var(--font-size-scale));
  --font-size-lead2: calc(var(--font-size-lead3) * var(--font-size-scale));
  --font-size-lead1: calc(var(--font-size-lead2) * var(--font-size-scale));

  --font-size-small1: calc(var(--font-size-body) / var(--font-size-scale));
  --font-size-small2: calc(var(--font-size-small1) / var(--font-size-scale));
  --font-size-small3: calc(var(--font-size-small2) / var(--font-size-scale));
}

*:not(.fas, .far, .fal, .fad, .fab, .el-notification__icon, .el-notification__closeBtn, .el-icon-warning, .el-icon-close, .el-icon-information) {
  font-family: var(--font-face-text) !important;
  font-feature-settings: 'calt' 1, 'liga' 1, 'case' 1, 'ss02' 1, 'ss03' 1;
}

html,
body {
  font-size: var(--font-size-body);
}

h1,
h2,
h3,
h4 {
  line-height: var(--font-line-height-display);
}

.h1,
h1 {
  font-size: var(--font-size-h1);
  font-weight: bolder;
}

.h2,
h2 {
  font-size: var(--font-size-h2);
  font-weight: bolder;
}

.h3
h3 {
  font-size: var(--font-size-h3);
  font-weight: bolder;
}

.h4,
h4 {
  font-size: var(--font-size-h4);
  font-weight: bolder;
}

.h5,
h5 {
  font-size: var(--font-size-h5);
  font-weight: bolder;
}

.is-small,
.small,
small {
  font-size: var(--font-size-small1);
  letter-spacing: var(--font-letter-spacing);
}

.is-selectable {
  user-select: all;
}

.is-small-caps {
  font-variant-caps: all-small-caps;
  font-feature-settings: "c2sc", "smcp";
}

.is-upper {
  text-transform: uppercase;
  letter-spacing: var(--font-letter-spacing);
}

.is-bold {
  font-weight: bolder;
}

.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.number-tabular {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
}

.number-proportional {
  font-variant-numeric: proportional-nums;
  font-feature-settings: "pnum";
}

mark {
  position: relative;

  background-color: transparent;

  &::before {
    --padding: -0.1rem;

    position: absolute;
    top: var(--padding);
    right: calc(var(--padding) * 2);
    bottom: var(--padding);
    left: calc(var(--padding) * 2);

    content: "";
    background-color: color-mix(in oklch, var(--pure-yellow), transparent 70%);
    mix-blend-mode: multiply;
    border-radius: 0.25rem;
  }
}
