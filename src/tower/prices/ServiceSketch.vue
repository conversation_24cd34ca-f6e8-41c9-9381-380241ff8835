<template>
  <div id="service-sketch">
    <div class="_column">
      {{ $store.getters['price.activeService'].Name || 'No description available.' }}
    </div>

    <div class="_column">
      <app-data-point label="Tow Class" format="inline">
        {{ $store.getters['price.activeService'].TowClass }}
      </app-data-point>
      <app-data-point label="Price Type" format="inline">
        {{ $store.getters['price.activeService'].PricingType || '-' }}
      </app-data-point>
      <app-data-point label="Defaults" format="inline" v-show="activeServiceDefaultsProxy.length">
        {{ activeServiceDefaultsProxy.length }}
        <button id="service-defaults-trigger" class="is-unstyled" popovertarget="service-defaults-popover">
          <i class="far fa-chevron-down"></i>
        </button>
      </app-data-point>
    </div>

    <div class="_column">
      <app-data-point label="Unit" format="inline">
        {{ $store.getters['price.activeService'].UnitType }}
      </app-data-point>
      <app-data-point label="GL#" format="inline">
        {{ $store.getters['price.activeService'].GLNum || '-' }}
        <button id="edit-glnumber-trigger" class="is-unstyled" popovertarget="edit-glnumber-popover">
          <i class="far fa-edit"></i>
        </button>
      </app-data-point>
      <app-data-point label="Lien Service" format="inline">
        {{ $store.getters['price.activeService'].LienService | affirmative }}
      </app-data-point>
      <app-data-point label="No Negative Price" format="inline">
        {{ $store.getters['price.activeService'].NoNegativePrice | affirmative }}
      </app-data-point>
    </div>

    <div class="_column">
      <app-data-point label="Taxable" format="inline">
        {{ $store.getters['price.activeService'].bTaxable | affirmative }}
      </app-data-point>
      <app-data-point label="Discountable" format="inline">
        {{ $store.getters['price.activeService'].bDiscountable | affirmative }}
      </app-data-point>
      <app-data-point label="Commissionable" format="inline">
        {{ $store.getters['price.activeService'].bCommissionable | affirmative }}
      </app-data-point>
      <app-data-point class="surchargeable" label="Surchargeable" format="inline">
        {{ $store.getters['price.activeService'].bSurchargeable | affirmative }}
      </app-data-point>
    </div>

    <dialog title="Service Defaults" id="service-defaults-popover" popover>
      <ul class="service-defaults">
        <li v-for="serviceDefault in activeServiceDefaultsProxy" :key="serviceDefault.Key">
          {{ serviceDefault.Value }}
        </li>
      </ul>
    </dialog>

    <dialog title="Edit GL Number" id="edit-glnumber-popover" popover @toggle="onToggleGlNumberPopover">
      <app-grid-form context="inline">
        <div class="columns is-multiline">
          <div class="column is-12">
            <app-text v-model="glNumberModal.value">
              GL Number
            </app-text>
          </div>
        </div>
      </app-grid-form>

      <div style="padding: 1rem 0rem">
        <app-button type="primary" @click="saveService">
          Save
        </app-button>
      </div>
    </dialog>
  </div>
</template>

<script>import { get } from 'lodash-es';


export default {
  name: 'price-sketch',

  inject: ['getServices'],

  data () {
    return {
      glNumberModal: {
        value: null
      }
    };
  },

  computed: {
    activeServiceDefaultsProxy () {
      return get(this.$store.getters['price.activeService'], 'defaults', []);
    }
  },

  watch: {
    '$store.state.price.activeServiceKey': {
      immediate: true,
      async handler () {
        this.getServiceExtensions(this.$store.state.price.activeServiceKey);
      }
    }
  },

  methods: {
    async getServiceExtensions (key) {
      let servicePromises = [];
      servicePromises.push(this.getServiceExtensionPartOne(key));
      servicePromises.push(this.getServiceExtensionPartTwo(key));
      servicePromises.push(this.getServiceDefaults(key));

      const serviceExtensions = await Promise.all(servicePromises.map(promise => promise.catch(response => response)));

      serviceExtensions.forEach(extension => {
        this.$store.state.price.services = this.$store.state.price.services.map(service => {
          if (service.Key === extension.key) {
            return extension.property === 'defaults'
              ? Object.assign(service, { defaults: extension.response })
              : Object.assign(service, extension);
          }
          return service;
        });
      });
    },

    getServiceExtensionPartOne (key) {
      return new Promise(resolve => {
        this.$store.dispatch('PRICE__getServiceDetails', {
          key: key,
          success: response => {
            resolve({ key, ...response });
          }
        });
      });
    },

    getServiceExtensionPartTwo (key) {
      return new Promise(resolve => {
        this.$store.dispatch('LOCATIONSERVICE__read', {
          key: key,
          success: response => {
            resolve({ key, ...response });
          }
        });
      });
    },

    getServiceDefaults (key) {
      return new Promise(resolve => {
        this.$store.dispatch('LOCATIONSERVICE__getDefaults', {
          key: key,
          success: response => {
            resolve({ property: 'defaults', key, response });
          }
        });
      });
    },

    onToggleGlNumberPopover (event) {
      if (event.newState === 'open') {
        this.glNumberModal.value = this.$store.getters['price.activeService'].ch10LocationGL;
      }
    },

    async saveService () {
      this.$store.dispatch('LOCATIONSERVICE__update', {
        serviceKey: this.$store.getters['price.activeService'].Key,
        description: this.$store.getters['price.activeService'].description,
        unitTypeKey: this.$store.getters['price.activeService'].pricingUnit,
        calculated: this.$store.getters['price.activeService'].quantityCalculated,
        taxable: this.$store.getters['price.activeService'].taxable,
        discountable: this.$store.getters['price.activeService'].discountable,
        commissionable: this.$store.getters['price.activeService'].commissonable,
        glNumber: this.glNumberModal.value,
        surcharge: this.$store.getters['price.activeService'].surcharge,
        displayOrderOverride: this.$store.getters['price.activeService'].orderOverride,
        billingName: this.$store.getters['price.activeService'].billingName,
        userKey: this.$store.getters['price.activeService'].userKeyNumber,
        dateLastModified: this.$store.getters['price.activeService'].dateLastModified,
        active: this.$store.getters['price.activeService'].bActive,
        success: async () => {
          document.querySelector('#edit-glnumber-popover').hidePopover();

          await this.getServices();
          this.getServiceExtensions(this.$store.state.price.activeServiceKey);
        }
      });
    }
  }
};
</script>
