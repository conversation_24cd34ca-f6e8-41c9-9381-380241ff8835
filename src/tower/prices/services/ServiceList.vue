<template>
  <ul class="filters-section">
    <li class="filter" data-filter>
      <input type="search" v-model="$store.state.price.serviceFilter" placeholder="Filter" />
    </li>
    <ServiceItem
      v-for="service in filteredServices"
      :service="service"
      :key="service.Key"
      @on-select="$emit('on-select', $event)" />

    <li class="filter" data-fade></li>

    <li class="filter" data-controls>
      <div>
        <span v-show="$store.state.price.services.length !== filteredServices.length">
          {{ $store.state.price.services.length }}&thinsp;&rarr;&thinsp;{{ filteredServices.length }}
        </span>
      </div>
      <div class="layouts">
        <div class="layout" @click="$store.state.price.serviceVariant = 'minimal'"
          :data-active="$store.state.price.serviceVariant === 'minimal'">
          <div class="segment" data-visible></div>
          <div class="segment"></div>
          <div class="segment"></div>
        </div>
        <div class="layout" @click="$store.state.price.serviceVariant = 'compact'"
          :data-active="$store.state.price.serviceVariant === 'compact'">
          <div class="segment" data-visible></div>
          <div class="segment" data-visible></div>
          <div class="segment"></div>
        </div>
        <div class="layout" @click="$store.state.price.serviceVariant = 'full'"
          :data-active="$store.state.price.serviceVariant === 'full'">
          <div class="segment" data-visible></div>
          <div class="segment" data-visible></div>
          <div class="segment" data-visible></div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
import Fuse from 'fuse.js';
import ServiceItem from './ServiceItem.vue';

let serviceFuse = null;

export default {
  name: 'service-list',

  components: {
    ServiceItem
  },

  computed: {
    filteredServices () {
      if (!!serviceFuse && !!this.$store.state.price.serviceFilter) {
        const services = serviceFuse.search(this.$store.state.price.serviceFilter).map(service => service.item);

        return services.map(({ HighlightedName, HighlightedGLNum, ...service }) => {
          service.HighlightedName = service.Name.replace(new RegExp(this.$store.state.price.serviceFilter, 'gi'), '<mark>$&</mark>');
          service.HighlightedGLNum = service.GLNum.replace(new RegExp(this.$store.state.price.serviceFilter, 'gi'), '<mark>$&</mark>');
          return service;
        });
      }

      return this.$store.state.price.services.map(({ HighlightedName, HighlightedGLNum, ...service }) => service);
    }
  },

  watch: {
    async '$store.state.price.services' () {
      // The Fuse object breaks reactivity, so we have to cause it to re-search
      // in order for the list to reflect any new data.
      serviceFuse = new Fuse(this.$store.state.price.services, {
        keys: [
          { name: 'Name', weight: 1 },
          { name: 'GLNum', weight: 0.5 }
        ],
        threshold: 0.1,
        ignoreLocation: true
      });
    }
  },

  mounted () {
    this.$emit('on-mount');
  }
};
</script>
