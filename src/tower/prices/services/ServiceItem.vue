<template>
  <li class="filter" data-service
    @click="$emit('on-select', service.Key)"
    :data-variant="$store.state.price.serviceVariant"
    :data-active="$store.getters['price.activeService'].Key === service.Key">

    <div class="name" v-html="service.HighlightedName || service.Name"></div>
    <i class="calculated far fa-calculator" v-show="service.Calculated" title="Calculated"></i>
    <div class="gl-number">GL# <span v-html="service.HighlightedGLNum || service.GLNum"></span></div>
    <div class="tags is-small">
      <div class="tow-class" v-show="service.TowClass">{{ service.TowClass }}</div>
      <div class="type" v-show="service.PricingType">{{ service.PricingType }}</div>
      <div class="unit" v-show="service.UnitType">{{ service.UnitType }}</div>
    </div>
  </li>
</template>

<script>
export default {
  name: 'service-item',

  props: {
    service: {
      type: Object,
      required: true
    }
  }
};
</script>
