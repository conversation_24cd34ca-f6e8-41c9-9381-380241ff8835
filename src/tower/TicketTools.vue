<template>
  <div id="ticket-tools">
    <app-titlebar title="Ticket Tools">
      <app-button type="white" @click="goToDataSheet"><i class="far fa-arrow-left"></i>&nbsp;Back to tickets</app-button>
    </app-titlebar>

    <section class="scene">
      <section class="paths">
        <WizardButton
          v-for="path in visiblePaths"
          :active="isActive(path)"
          :key="path.id"
          @click="setPath(path.id)">
          {{ path.label }}
        </WizardButton>
      </section>

      <app-grid-form class="form-inputs" v-if="pathId">
        <div class="columns is-multiline">
          <div class="column is-12 is-left" v-if="parameters.newPrefix.isVisible">
            <app-text v-model="parameters.newPrefix.value">
              New Prefix
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.voidReason.isVisible">
            <app-text v-model="parameters.voidReason.value">
              Reason
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.ticketNumber.isVisible">
            <app-text v-model="parameters.ticketNumber.value" maxlength="20">
              Ticket Number
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.driverKey.isVisible">
            <app-select v-model="parameters.driverKey.value" :options="drivers" keyAlias="Key" valueAlias="Value">
              Driver
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.lotKey.isVisible">
            <app-select v-model="parameters.lotKey.value" :options="lots" keyAlias="Key" valueAlias="Value">
              Lot
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.companyKey.isVisible">
            <app-select v-model="parameters.companyKey.value" :options="companies" keyAlias="Key" valueAlias="Value">
              Company
            </app-select>
          </div>
          <div class="column is-12 is-left" v-if="parameters.quantity.isVisible">
            <app-number v-model="parameters.quantity.value" min="0" max="10000">
              How many tickets?
            </app-number>
          </div>
          <div class="column is-12 is-left" v-if="parameters.startNumber.isVisible">
            <app-text v-model="parameters.startNumber.value" maxlength="20">
              Start with ticket number
            </app-text>
          </div>
          <div class="column is-12 is-left" v-if="parameters.targetPrefix.isVisible">
            <app-text v-model="parameters.targetPrefix.value">
              Prefix
            </app-text>
          </div>
          <!-- <div class="column is-4 is-bottom" v-if="parameters.quantity.isVisible">
            <app-text v-model="rangePreview" :readonly="true">
              Will Make
            </app-text>
          </div> -->
        </div>
      </app-grid-form>

      <WizardButton flavor="primary" @click="trigger" :icon="false" v-if="pathId">
        Process
      </WizardButton>
    </section>
  </div>
</template>

<script>import { filter, find, forEach, includes, get } from 'lodash-es';


import Access from '@/utils/access.js';
import { EVENT_SUCCESS } from '@/config.js';
import { mapGetters, mapActions } from 'vuex';
import WizardButton from '@/components/inputs/WizardButton.vue';

export default {
  name: 'ticket-tools',

  components: {
    WizardButton
  },

  data () {
    return {
      pathId: '',

      paths: [
        {
          id: 'open',
          label: 'Open tickets',
          isVisible: true
        },
        {
          id: 'assign-to-driver',
          label: 'Assign tickets to a driver',
          isVisible: true
        },
        {
          id: 'assign-to-lot',
          label: 'Assign tickets to a lot',
          isVisible: true
        },
        {
          id: 'assign-to-company',
          label: 'Assign tickets to a company',
          isVisible: true
        },
        {
          id: 'void',
          label: 'Void tickets',
          isVisible: true
        },
        {
          id: 'reopen',
          label: 'Reopen voided tickets',
          isVisible: true
        },
        {
          id: 'change-prefix',
          label: 'Change prefix on tickets',
          isVisible: true
        }
      ],

      parameters: {
        targetPrefix: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'void', 'reopen', 'change-prefix']
        },
        newPrefix: {
          value: '',
          isVisible: true,
          relevantPaths: ['change-prefix']
        },
        ticketNumber: {
          value: '',
          isVisible: true,
          relevantPaths: []
        },
        startNumber: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'reopen', 'change-prefix', 'void']
        },
        quantity: {
          value: '',
          isVisible: true,
          relevantPaths: ['open', 'assign-to-driver', 'assign-to-lot', 'assign-to-company', 'reopen', 'change-prefix', 'void']
        },
        driverKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-driver']
        },
        lotKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-lot']
        },
        companyKey: {
          value: '',
          isVisible: true,
          relevantPaths: ['assign-to-company']
        },
        voidReason: {
          value: '',
          isVisible: true,
          relevantPaths: ['void']
        }
      },

      drivers: [],
      lots: [],
      companies: []
    };
  },

  computed: {
    ...mapGetters([
      'TOPSCOMPANY__settings'
    ]),

    canEditPrefix () {
      return this.TOPSCOMPANY__settings.bAllowTowTicketPrefix === '1';
    },

    visiblePaths () {
      return this.pathId
        ? filter(this.paths, ['id', this.pathId])
        : filter(this.paths, ['isVisible', true]);
    },

    currentPath () {
      return find(this.paths, ['id', this.pathId]) || null;
    },

    rangePreview () {
      let prefix = this.parameters.targetPrefix.value;
      let startNumber = Number(this.parameters.startNumber.value);
      let quantity = Number(this.parameters.quantity.value);
      let endNumber = '';

      if (!startNumber && !quantity) return 'None';
      if (startNumber && quantity < 1) return 'None';
      if (startNumber && quantity === 1) return `${prefix}${startNumber}`;

      endNumber = startNumber + quantity - 1;

      return `${prefix}${startNumber}–${prefix}${endNumber}`;
    }
  },

  mounted () {
    forEach(this.paths, path => {
      if (path.id === 'open') {
        path.isVisible = Access.has('tickets.add');
      }

      if (path.id === 'change-prefix') {
        path.isVisible = this.canEditPrefix;
      }
    });

    this.TOPSCOMPANY__getDrivers({
      callback: response => {
        this.drivers = response;
      }
    });

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.lots = response;
      }
    });

    this.CALL__getSubterminals({
      callback: response => {
        this.companies = response;
      }
    });
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getLots',
      'CALL__getSubterminals',
      'TOWTICKET__create',
      'TOWTICKET__changePrefix',
      'TOWTICKET__assignDriver',
      'TOWTICKET__assignLot',
      'TOWTICKET__assignCompany',
      'TOWTICKET__void',
      'TOWTICKET__unvoid'
    ]),

    goToDataSheet () {
      this.$router.push({ name: 'Tickets' });
    },

    setPath (pathId) {
      this.pathId = this.pathId === pathId
        ? null
        : pathId;

      forEach(this.parameters, (parameter, attribute) => {
        // Reset all values on path reset
        if (!pathId) {
          parameter.value = '';
        }

        if (attribute === 'targetPrefix') {
          parameter.isVisible = this.canEditPrefix && includes(parameter.relevantPaths, pathId);
        } else {
          parameter.isVisible = includes(parameter.relevantPaths, pathId);
        }
      });

      return;
    },

    isActive ({ id }) {
      return this.pathId === id;
    },

    trigger () {
      switch (this.currentPath.id) {
        case 'open':
          this.create();
          break;
        case 'assign-to-driver':
          this.assignDriver();
          break;
        case 'assign-to-lot':
          this.assignLot();
          break;
        case 'assign-to-company':
          this.assignCompany();
          break;
        case 'void':
          this.void();
          break;
        case 'reopen':
          this.unvoid();
          break;
        case 'change-prefix':
          this.changePrefix();
          break;
      }
    },

    create () {
      this.TOWTICKET__create({
        startNumber: this.parameters.startNumber.value,
        prefix: this.parameters.targetPrefix.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    changePrefix () {
      this.TOWTICKET__changePrefix({
        newPrefix: this.parameters.newPrefix.value,
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignDriver () {
      this.TOWTICKET__assignDriver({
        driverKey: this.parameters.driverKey.value,
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignLot () {
      this.TOWTICKET__assignLot({
        lotKey: this.parameters.lotKey.value,
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    assignCompany () {
      this.TOWTICKET__assignCompany({
        companyKey: this.parameters.companyKey.value,
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    void () {
      this.TOWTICKET__void({
        voidReason: this.parameters.voidReason.value,
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    },

    unvoid () {
      this.TOWTICKET__unvoid({
        targetPrefix: this.parameters.targetPrefix.value,
        startNumber: this.parameters.startNumber.value,
        quantity: this.parameters.quantity.value,
        success: response => {
          this.$hub.$emit(EVENT_SUCCESS, get(response.RESULT, 'Tickets updated.'));
        }});
    }
  }
};
</script>

<style scoped>
#ticket-tools {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 4rem 1fr;

  height: 100vh;

  .scene {
    grid-area: 2 / 1;
    justify-self: center;
    align-self: center;

    width: 55ch;
    max-width: 90%;

    .paths {
      margin-bottom: 1rem;
    }

    .form-inputs {
      margin-bottom: 1rem;
      border: 1px solid var(--input-border);
      border-bottom: 0;
      border-radius: var(--border-radius-100);
    }
  }
}
</style>
