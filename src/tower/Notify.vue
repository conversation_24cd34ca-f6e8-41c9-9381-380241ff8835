<template>
  <div class="notify">
    <app-titlebar title="Notify"></app-titlebar>

    <section class="driver-tools">
      <span class="select">
        <select v-model="driverGroup" @change="selectDriverGroup">
          <option value="">Select Driver Group...</option>
          <option value="all">All</option>
          <option value="none">None</option>
          <option v-for="(item, index) in driverStatusesOptions" :value="item.value" :key="index">{{ item.label }}</option>
          <option v-for="(item, index) in driverTypesOptions" :value="item.value" :key="index">{{ item.label }}</option>
        </select>
      </span>
    </section> <!-- /driver-tools -->

    <section :class="driverSectionClasses">
      <transition-group name="smooth-list" tag="ul" :css="false">
        <li v-for="item in driversProxy" @click="selectDriver(item)" :key="item.key" :class="driverClasses(item)">
          <div>
            <strong>Driver: {{ item.label }}</strong>
            <div>{{ item.value }}</div>
          </div>
          <transition name="zoom-in-out">
            <span v-if="isDriverSelected(item)"><i class="fal fa-check check"></i></span>
          </transition>
        </li>
      </transition-group>
    </section> <!-- /drivers -->

    <section class="truck-tools" v-if="trucksListVisible">
      <span class="select">
        <select v-model="truckGroup" @change="selectTruckGroup">
          <option value="">Select Truck Group...</option>
          <option value="all">All</option>
          <option value="none">None</option>
          <option v-for="(item, index) in truckStatus" :value="item.value" :key="index">{{ item.label }}</option>
        </select>
      </span>
    </section> <!-- /truck-tools -->

    <section class="trucks" v-if="trucksListVisible">
      <transition-group name="smooth-list" tag="ul" :css="false">
        <li v-for="item in trucksProxy" @click="selectTruck(item)" :key="item.key" :class="truckClasses(item)">
          <div>
            <strong>Truck: {{ item.label }}</strong>
            <div>{{ item.value }}</div>
          </div>
          <transition name="zoom-in-out">
            <span v-if="isTruckSelected(item)"><i class="fal fa-check check"></i></span>
          </transition>
        </li>
      </transition-group>
    </section> <!-- /trucks -->

    <section class="message">
      <div class="message__compose">
        <app-text v-model="driverRecipients" style="margin-bottom: 1rem;">
          <strong>Drivers</strong>
        </app-text>

        <app-text v-model="truckRecipients" style="margin-bottom: 1rem;">
          <strong>Trucks</strong>
        </app-text>

        <strong>Message</strong>
        <textarea v-model="message" :maxlength="maxlength" class="input" style="height: 15rem; margin-bottom: 1rem;"></textarea>

        <div class="field has-addons">
          <p class="control width-auto"><button class="button is-small is-static">Maximum length</button></p>
          <p class="control width-auto"><input v-model="maxlength" type="number" class="input is-small"/></p>
        </div>
      </div>
    </section> <!-- /message -->

    <app-footerbar>
      <app-button @click="cancel" type="default">Cancel</app-button>
      <app-button @click="sendAndDispatch" type="primary" :disabled="!canSend">Send</app-button>
    </app-footerbar>
  </div> <!-- /notify -->
</template>

<script>import is from 'is_js';
import { mapGetters, mapActions } from 'vuex';
import { VALUE_ID } from '@/config.js';
import {
  get,
  map,
  join,
  find,
  uniq,
  split,
  forEach,
  isEmpty,
  toNumber,
  castArray,
  findIndex
} from 'lodash-es';

export default {
  name: 'notify',

  data () {
    return {
      trucks: [],
      drivers: [],
      message: '',
      template: '',
      truckGroup: '',
      maxlength: 320,
      driverGroup: '',
      driverTypes: [],
      truckStatuses: [],
      driverStatuses: [],
      templateFields: [],
      selectedTrucks: [],
      truckRecipients: '',
      selectedDrivers: [],
      driverRecipients: '',
      assigned: {
        driverKey: '',
        truckKey: ''
      }
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings']),

    callKey () {
      return get(this.$route.query, 'callKey', '');
    },

    dispatchKey () {
      return get(this.$route.query, 'dispatchKey', '');
    },

    dispatchDriverKey () {
      return get(this.$route.query, 'dispatchDriverKey', '');
    },

    dispatchTruckKey () {
      return get(this.$route.query, 'dispatchTruckKey', '');
    },

    dispatchEmployeeKey () {
      return get(this.$route.query, 'dispatchEmployeeKey', '');
    },

    reasonProxy () {
      return get(this.$route.query, 'reason', '');
    },

    returnTo () {
      return get(this.$route.query, 'returnTo', '');
    },

    driversProxy () {
      return map(this.drivers, item => {
        return {
          key: item.Key,
          value: item.PagerEmail,
          label: item.EmployeeName,
          typeKey: item.EmployeeTypeKey,
          statusKey: item.DriverStatusKey
        };
      });
    },

    trucksProxy () {
      return map(this.trucks, item => {
        return {
          key: item.Key,
          value: item.TrackingUnitData,
          label: item.TruckNumber,
          statusKey: item.TruckStatusKey
        };
      });
    },

    driverStatusesOptions () {
      return map(this.driverStatuses, item => {
        return { value: `status:${item.Key}`, label: `Status: ${item.Value}` };
      });
    },

    driverTypesOptions () {
      return map(this.driverTypes, item => {
        return { value: `type:${item.Key}`, label: `Type: ${item.Value}` };
      });
    },

    truckStatus () {
      return map(this.truckStatuses, item => {
        return { value: `status:${item.Key}`, label: `Status: ${item.Value}` };
      });
    },

    shouldSetToDispatched () {
      return !!this.dispatchKey && this.reasonProxy !== 'unassigned';
    },

    trucksListVisible () {
      return Number(this.TOPSCOMPANY__settings.bPageTrucks) === 1;
    },

    driverSectionClasses () {
      return {
        'drivers': true,
        'is-expanded': !this.trucksListVisible
      };
    },

    canSend () {
      if (isEmpty(this.driverRecipients) &&
        isEmpty(this.truckRecipients)) return false;

      return !isEmpty(this.message);
    }
  },

  watch: {
    selectedDrivers () {
      let recipients = map(this.selectedDrivers, item => {
        return item.value;
      });

      this.driverRecipients = join(recipients, ', ');
    },

    selectedTrucks () {
      let recipients = map(this.selectedTrucks, item => {
        return item.value;
      });

      this.truckRecipients = join(recipients, ', ');
    }
  },

  methods: {
    ...mapActions([
      'NOTIFICATION__send',
      'DISPATCH__setDispatched',
      'NOTIFICATION__getTemplate',
      'NOTIFICATION__getCallText',
      'TOPSCOMPANY__getEmployeeTypes',
      'TOPSCOMPANY__getTruckStatuses',
      'TOPSCOMPANY__getDriverStatuses',
      'NOTIFICATION__getTemplateFields',
      'NOTIFICATION__getNotifiableTrucks',
      'NOTIFICATION__getNotifiableEmployees',
      'NOTIFICATION__getDispatchableDriverTruck'
    ]),

    getAssignedDispatchUnit () {
      this.assigned = {
        driverKey: Number(this.dispatchDriverKey),
        truckKey: Number(this.dispatchTruckKey)
      };

      if (!!this.assigned.driverKey && !!this.assigned.truckKey) {
        this.preselectAssignedDispatchUnit();
        return;
      }

      if (this.dispatchKey) {
        this.NOTIFICATION__getDispatchableDriverTruck({
          dispatchKey: this.dispatchKey,
          callback: response => {
            this.assigned = {
              driverKey: response.EmployeeKey,
              truckKey: response.TruckKey
            };

            this.preselectAssignedDispatchUnit();
          }
        });
      }
    },

    preselectAssignedDispatchUnit () {
      if (!!this.assigned.driverKey && this.selectedDrivers.length === 0) {
        forEach(this.driversProxy, driver => {
          if (driver.key === this.assigned.driverKey) {
            this.selectDriver(driver);
          }
        });
      }

      if (!!this.assigned.truckKey && this.selectedTrucks.length === 0) {
        forEach(this.trucksProxy, truck => {
          if (truck.key === this.assigned.truckKey) {
            this.selectTruck(truck);
          }
        });
      }
    },

    constructMessage () {
      switch (this.reasonProxy) {
        case 'unassigned':
          this.message = `Call #${this.callKey} was unassigned.`;
          break;

        default:
          if (!this.callKey) return;

          // this.NOTIFICATION__getTemplateFields({
          //   callback: response => {
          //     this.templateFields = response;
          //   }
          // });

          // this.NOTIFICATION__getTemplate({
          //   callback: response => {
          //     this.template = response.Value;
          //   }
          // });

          this.NOTIFICATION__getCallText({
            callKey: this.callKey,
            callback: response => {
              this.message = response.Value;
            }
          });
          break;
      }
    },

    getDrivers () {
      this.NOTIFICATION__getNotifiableEmployees({
        callback: response => {
          this.drivers = response;
          this.preselectAssignedDispatchUnit();
        }
      });
    },

    getTrucks () {
      this.NOTIFICATION__getNotifiableTrucks({
        callback: response => {
          this.trucks = response;
          this.preselectAssignedDispatchUnit();
        }
      });
    },

    getDriverStatuses () {
      this.TOPSCOMPANY__getDriverStatuses({
        callback: response => {
          this.driverStatuses = response;
        }
      });
    },

    getDriverTypes () {
      this.TOPSCOMPANY__getEmployeeTypes({
        callback: response => {
          this.driverTypes = response;
        }
      });
    },

    getTruckStatuses () {
      this.TOPSCOMPANY__getTruckStatuses({
        callback: response => {
          this.truckStatuses = response;
        }
      });
    },

    selectGroupAbstract (group, sourceList, targetList) {
      let [criterion, value] = split(this.$data[group], ':');
      this.$data[group] = '';
      let items = [];

      switch (criterion) {
        case 'none':
          this.$data[targetList] = [];
          break;
        case 'all':
          items = castArray(this[sourceList]);
          break;
        case 'status':
          items = find(this[sourceList], ['statusKey', value]);
          break;
        case 'type':
          items = find(this[sourceList], ['typeKey', value]);
          break;
      }

      if (isEmpty(items)) return;

      forEach(castArray(items), item => {
        this.$data[targetList].push(item);
      });

      this.$data[targetList] = uniq(this.$data[targetList]);
    },

    selectDriverGroup () {
      this.selectGroupAbstract('driverGroup', 'driversProxy', 'selectedDrivers');
    },

    selectTruckGroup () {
      this.selectGroupAbstract('truckGroup', 'trucksProxy', 'selectedTrucks');
    },

    selectDriver (driver) {
      if (this.isDriverSelected(driver)) {
        let pruneDriver = findIndex(this.selectedDrivers, driver);
        this.selectedDrivers.splice(pruneDriver, 1);
      } else {
        this.selectedDrivers.push(driver);
      }
    },

    selectTruck (truck) {
      if (this.isTruckSelected(truck)) {
        let pruneTruck = findIndex(this.selectedTrucks, truck);
        this.selectedTrucks.splice(pruneTruck, 1);
      } else {
        this.selectedTrucks.push(truck);
      }
    },

    send () {
      return new Promise((resolve, reject) => {
        resolve(this.NOTIFICATION__send({
          data: {
            'DispatchKey': this.dispatchKey,
            'PageSubject': '',
            'CallKey': this.callKey,
            'PageText': this.message,
            'MaxNumCharacters': this.maxlength,
            'EmailAddresses': this.driverRecipients,
            'TruckTrackingUnits': this.truckRecipients
          }
        }));
      });
    },

    sendAndDispatch () {
      this.send().then(() => {
        if (this.shouldSetToDispatched) {
          this.DISPATCH__setDispatched({
            key: this.dispatchKey,
            callback: response => {
              this.returnToRoute();
            }
          });
        } else {
          this.returnToRoute();
        }
      });
    },

    cancel () {
      this.returnToRoute();
    },

    returnToRoute () {
      if (this.returnTo) {
        this.$router.push({ name: this.returnTo });
      } else {
        this.$router.go(-1);
      }
    },

    isDriverSelected (driver) {
      return is.inArray(driver, this.selectedDrivers);
    },

    isTruckSelected (truck) {
      return is.inArray(truck, this.selectedTrucks);
    },

    driverClasses (driver) {
      return {
        'item': true,
        'off-duty': toNumber(driver.statusKey) === VALUE_ID.driverStatus.offDuty
      };
    },

    truckClasses (truck) {
      return {
        'item': true,
        'out-of-service': toNumber(truck.statusKey) === VALUE_ID.truckStatus.outOfService
      };
    }
  },

  mounted () {
    this.getAssignedDispatchUnit();
    this.getTrucks();
    this.getDrivers();
    this.getDriverStatuses();
    this.getDriverTypes();
    this.getTruckStatuses();
    this.constructMessage();
  }
};
</script>

<style scoped>
.notify {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: (2rem) (2rem) 1fr (2rem) 1fr (2rem);
  grid-template-areas:
    "titlebar    titlebar  titlebar"
    "drivertools message   message"
    "drivers     message   message"
    "trucktools  message   message"
    "trucks      message   message"
    "footerbar   footerbar footerbar";
  height: 100vh;

  .title-bar { grid-area: titlebar; }

  .driver-tools,
  .truck-tools {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
  }

  .driver-tools {
    grid-area: drivertools;
  }

  .truck-tools {
    grid-area: trucktools;
    border-top: 1px solid var(--body-border);
  }

  .drivers,
  .trucks {
    /* --- */
    /* Inlined from previous %rich-list */
    overflow-y: scroll;
    color: var(--body-fg);
    background: var(--body-bg);

    &::-webkit-scrollbar {
      display: none;
    }

    > li {
      padding: 0.33rem 0.5rem;
    }

    .item {
      font-size: var(--font-size-small1);

      strong {
        font-size: 1rem;
      }
    }

    .column {
      padding: 0;
    }
    /* --- */

    .item {
      display: flex;
      justify-content: space-between;
      padding: .5rem 1rem;
    }

    .off-duty,
    .out-of-service {
      * {
        color: var(--metadata-bg);
      }
    }

    .check {
      font-size: 1.5rem;
      color: var(--success);
    }
  }

  .drivers {
    grid-area: drivers;

    &.is-expanded {
      grid-row: 3 / 6;
    }
  }

  .trucks {
    grid-area: trucks;
  }

  .message {
    grid-area: message;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 2rem;
    background: var(--body-border);

    .message__compose {
      width: 100%;
    }

    .message__title {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
  }

  .footer-bar {
    grid-area: footerbar;
  }
}
</style>
