<template>
  <div id="payments-view">
    <section id="customers-section">
      <app-grid-form class="customer-control" context="inline">
        <div class="columns">
          <div class="column is-bottom">
            <app-customer v-model.number="$store.state.payment.selectedCustomerKey" :show-ppi-control="false" :show-type="true">
              Customer</app-customer>
          </div>
        </div>
      </app-grid-form>
      <app-button class="local-settings-button" @click="showLocalSettings">
        <i class="far fa-sliders-simple"></i></app-button>
    </section>

    <section id="payments-section">
      <payments-grid
        title="Payments"
        key="payments-grid"
        :grid="paymentsSettings"
        :data="paymentsData"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :show-loader="showLoader"
        :active-payment-key="$store.state.payment.selectedPaymentKey"
        @refresh="refresh"
        @edit="openPaymentModal($event)"
        @select="selectPayment($event)"
        @overpayment="addOverpayment($event)"
        @addWithMethod="addWithMethod($event)"
        @exportData="exportData"
        @save="save">
      </payments-grid>
    </section>

    <section id="calls-section">
      <div class="_liner" >
        <calls-grid
          title="Calls"
          key="calls-grid"
          :grid="callsSettings"
          :data="callsData"
          :selectedPayment="selectedPayment"
          :multiselect="true"
          :refreshedAt="refreshedAt"
          :config="callsConfig"
          :show-loader="showLoader"
          @refresh="refresh"
          @openRecord="openCallRecord"
          @exportData="exportData"
          @save="save"
          @distributePaymentBalance="distributePaymentBalance"
          @fillPendingApplicationBalance="fillPendingApplicationBalance"
          @clearPendingApplicationBalance="clearPendingApplicationBalance"
          @changePendingPayment="changePendingPayment">
        </calls-grid>

        <div id="application-panel">
          <app-button type="primary" @click="applyPayment" :disabled="!(isSelectedPaymentActive && sumOfPendingApplications)">
            Apply {{ sumOfPendingApplications | usd }}</app-button>
          <div></div>
          <app-data-point label="Payments Balance">
            {{ sumOfPaymentsBalance | usd }}</app-data-point>
          <app-data-point label="Calls Balance">
            {{ sumOfCallsBalance | usd }}</app-data-point>
        </div>
      </div>
    </section>

    <app-modal :title="paymentTitle"
      :show="paymentModal.isVisible"
      :pad="false"
      @close="closePaymentModal">
      <template v-if="paymentModal.componentIsActive">
        <Payment
          :record-key="paymentModal.key"
          :customer-key="$store.state.payment.selectedCustomerKey"
          :is-nested="true"
          @close="closePaymentModal"
          @created="onPaymentAdded" />
      </template>
    </app-modal>

    <TowPay
      :is-tow-payment="towPayModal.isTowPayment"
      :customer-key="$store.state.payment.selectedCustomerKey"
      v-if="towPayModal.isVisible"
      @close="closeTowPayModal"
      @created="onPaymentAdded" />

    <QuickViews
      :view-key="viewConfig.key"
      :view-uuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData" />

    <dialog id="local-settings" ref="localSettingsModal" @click.capture="onClickOutsideLocalSettings">
      <header class="is-small is-upper is-bold">
        Settings</header>

      <label>
        <input type="checkbox" v-model="$store.state.payment.showDeletedPayments" switch>
        <div>Show deleted payments.</div>
      </label>
      <label>
        <input type="checkbox" v-model="$store.state.payment.filterCallsByCustomer" switch>
        <div>Filter calls by customer.</div>
      </label>
      <label>
        <input type="checkbox" v-model="$store.state.payment.rejectZeroBalances" switch>
        <div>Hide zero balances.</div>
      </label>
      <label>
        <input type="checkbox" v-model="$store.state.payment.rejectUnconfirmed" switch>
        <div>Hide unconfirmed calls.</div>
      </label>
      <label>
        <input type="checkbox" v-model="$store.state.payment.selectNewlyAddedPayment" switch>
        <div>Select newly added payment.</div>
      </label>
      <div class="v-space-2"></div>

      <app-button type="primary" @click="hideLocalSettings">
        Close
      </app-button>
    </dialog>
  </div>
</template>

<script>import { get, set } from 'lodash-es';


import { mapActions } from 'vuex';
import Payment from '@/tower/Payment.vue';
import TowPay from '@/components/features/TowPay.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import PaymentsGrid from '@/components/features/PaymentsGrid.vue';
import CallsGrid from '@/components/features/PaymentsCallsGrid.vue';
import { EVENT_SUCCESS, GRID_KEY, VALUE_ID } from '@/config';

export default {
  name: 'payments-view',

  extends: RecordsView,

  components: {
    TowPay,
    Payment,
    CallsGrid,
    PaymentsGrid
  },

  data () {
    return {
      viewConfig: {
        uuid: 'payments-view',
        noun: 'Payments',
        requireFilters: false,
        shouldImmediatelyLoadData: true,
        recordKeyName: 'lPaymentKey',
        readRouteName: 'Payment',
        addRouteName: 'AddPayment',

        dataAdditional: {
          Customer: null,
          ShowInactive: this.$store.state.payment.showDeletedPayments
        }
      },

      callsConfig: {
        uuid: 'calls-grid',
        noun: 'Payments',
        requireFilters: false,
        shouldImmediatelyLoadData: false,
        recordKeyName: 'lKey',
        readRouteName: 'Call',
        addRouteName: 'AddCall',

        dataAdditional: {
          Customer: null
        }
      },

      paymentModal: {
        key: null,
        isVisible: false,
        componentIsActive: false
      },

      towPayModal: {
        key: null,
        isVisible: false,
        componentIsActive: false,
        isTowPayment: true
      }
    };
  },

  computed: {
    selectedCustomer () {
      if (!this.$store.state.payment.customers) return {};
      if (!this.$store.state.payment.selectedCustomerKey) return {};

      return this.$store.state.payment.customers.find(customer => customer.Key === this.$store.state.payment.selectedCustomerKey);
    },

    selectedPayment () {
      if (!this.paymentsData) return {};
      if (!this.$store.state.payment.selectedPaymentKey) return {};

      return this.paymentsData.find(payment => Number(payment.lPaymentKey) === Number(this.$store.state.payment.selectedPaymentKey));
    },

    isSelectedPaymentActive () {
      return this.selectedPayment && 'bActive' in this.selectedPayment ? this.selectedPayment.bActive : false;
    },

    paymentTitle () {
      return this.selectedPayment && 'lPaymentKey' in this.selectedPayment ? `Payment #${this.selectedPayment.lPaymentKey}` : 'Payment';
    },

    paymentsSettings () {
      let paymentsGrid = this.RECORDS__settings.Grids.find(grid => grid.Key === GRID_KEY.payment.payments) || {};

      if (!('Filters' in paymentsGrid)) { return paymentsGrid; }

      paymentsGrid.Filters = paymentsGrid.Filters.filter(filter => filter.FieldID !== 'tcUnappliedAmount');

      if (this.$store.state.payment.rejectZeroBalances) {
        paymentsGrid.Filters.push({
          And: true,
          Or: false,
          Not: false,
          OpenParen: false,
          FieldID: 'tcUnappliedAmount',
          FieldName: 'PAYtcUnappliedAmount',
          Operator: '>',
          Value: 0,
          DisplayValue: 0,
          CloseParen: false
        });
      }

      if (paymentsGrid.Filters.length > 0) {
        paymentsGrid.Filters[0].And = false;
      }

      return paymentsGrid;
    },

    paymentsData: {
      get () {
        return get(this.viewData, `Grids[${GRID_KEY.payment.payments}]`, []);
      },
      set (value) {
        set(this.viewData, `Grids[${GRID_KEY.payment.payments}]`, value);
      }
    },

    callsSettings () {
      let callsGrid = this.RECORDS__settings.Grids.find(grid => grid.Key === GRID_KEY.payment.calls) || {};

      if (!('Filters' in callsGrid)) { return callsGrid; }

      if (this.$store.state.payment.rejectUnconfirmed) {
        callsGrid.Filters = callsGrid.Filters.filter(filter => filter.FieldID !== 'bConfirmed');

        callsGrid.Filters.push({
          And: callsGrid.Filters.length > 0,
          Or: false,
          Not: false,
          OpenParen: false,
          FieldID: 'bConfirmed',
          FieldName: 'CASE WHEN CALdAccConfirm IS NULL THEN 0 ELSE 1 END',
          Operator: 'Is',
          Value: 'True',
          DisplayValue: 'True',
          CloseParen: false
        });
      }

      if (this.$store.state.payment.filterCallsByCustomer) {
        callsGrid.Filters = callsGrid.Filters.filter(filter => filter.FieldID !== 'sCustomer');

        if (get(this.selectedCustomer, 'Key', '')) {
          callsGrid.Filters.push({
            And: callsGrid.Filters.length > 0,
            Or: false,
            Not: false,
            OpenParen: false,
            FieldID: 'sCustomer',
            FieldName: 'CALCUSlCustomerKey',
            Operator: '=',
            Value: get(this.selectedCustomer, 'Key', ''),
            DisplayValue: get(this.selectedCustomer, 'Value', ''),
            CloseParen: false
          });
        }
      }

      if (this.$store.state.payment.rejectZeroBalances) {
        callsGrid.Filters = callsGrid.Filters.filter(filter => filter.FieldID !== 'tcBalance');

        callsGrid.Filters.push({
          And: callsGrid.Filters.length > 0,
          Or: false,
          Not: false,
          OpenParen: false,
          FieldID: 'tcBalance',
          FieldName: 'CALtcBalance',
          Operator: '>',
          Value: 0,
          DisplayValue: 0,
          CloseParen: false
        });
      }

      if (callsGrid.Filters.length > 0) {
        callsGrid.Filters[0].And = false;
      }

      return callsGrid;
    },

    callsData: {
      get () {
        return get(this.viewData, `Grids[${GRID_KEY.payment.calls}]`, []);
      },
      set (value) {
        set(this.viewData, `Grids[${GRID_KEY.payment.calls}]`, value);
      }
    },

    sumOfCallsBalance () {
      if (!this.callsData || !Array.isArray(this.callsData)) {
        return 0;
      }
      return this.callsData.reduce((total, call) => total + Number(call.realTimeBalance), 0);
    },

    sumOfPaymentsBalance () {
      if (!this.paymentsData || !Array.isArray(this.paymentsData)) {
        return 0;
      }
      return this.paymentsData.reduce((total, payment) => total + Number(payment.realTimeBalance), 0);
    },

    pendingApplications () {
      if (!this.callsData || !Array.isArray(this.callsData)) {
        return [];
      }
      return this.callsData.filter(call => call.pendingApplicationBalance > 0);
    },

    sumOfPendingApplications () {
      return this.pendingApplications.reduce((total, call) => total + call.pendingApplicationBalance, 0);
    }
  },

  watch: {
    '$store.state.payment.showDeletedPayments' () {
      this.viewConfig.dataAdditional.ShowInactive = this.$store.state.payment.showDeletedPayments;

      this.loadData();
    },

    '$store.state.payment.selectedCustomerKey' () {
      this.viewConfig.dataAdditional.Customer = this.$store.state.payment.selectedCustomerKey;
      this.callsConfig.dataAdditional.Customer = this.$store.state.payment.selectedCustomerKey;

      this.loadData();
    },

    '$store.state.payment.rejectUnconfirmed' () {
      this.loadData();
    },

    '$store.state.payment.filterCallsByCustomer' () {
      this.loadData();
    },

    '$store.state.payment.rejectZeroBalances' () {
      this.loadData();
    },

    sumOfPendingApplications () {
      if (this.selectedPayment && this.paymentsData && Array.isArray(this.paymentsData)) {
        this.paymentsData = this.paymentsData.map(payment => {
          if (payment.lPaymentKey === this.selectedPayment.lPaymentKey) {
            payment.realTimeBalance = Number(this.selectedPayment.tcUnappliedAmount) - this.sumOfPendingApplications;
          }
          return payment;
        });
      }
    }
  },

  methods: {
    ...mapActions([
      'PAYMENT__applyToCall',
      'PAYMENT__getCustomers',
      'PAYMENT__createOverpaymentInvoice'
    ]),

    showLocalSettings () {
      this.$refs.localSettingsModal.showModal();
    },

    hideLocalSettings () {
      this.$refs.localSettingsModal.close();
    },

    onClickOutsideLocalSettings (event) {
      const dialog = this.$refs.localSettingsModal;
      const rect = dialog.getBoundingClientRect();

      if (
        event.clientX < rect.left ||
        event.clientX > rect.right ||
        event.clientY < rect.top ||
        event.clientY > rect.bottom
      ) {
        this.hideLocalSettings();
      }
    },

    getCustomers () {
      return new Promise(resolve => {
        this.PAYMENT__getCustomers({
          success: response => resolve(response)
        });
      });
    },

    clearSelectedPayment () {
      this.$store.state.payment.selectedPaymentKey = '';

      this.resetApplication();
    },

    selectPayment ({ lPaymentKey }) {
      this.$store.state.payment.selectedPaymentKey = Number(lPaymentKey);

      this.resetApplication();
    },

    resetApplication () {
      if (this.paymentsData && Array.isArray(this.paymentsData)) {
        this.paymentsData = this.paymentsData.map(payment => {
          payment.realTimeBalance = payment.tcUnappliedAmount;
          return payment;
        });
      }

      if (this.callsData && Array.isArray(this.callsData)) {
        this.callsData = this.callsData.map(call => {
          call.pendingApplicationBalance = 0;
          call.realTimeBalance = Number(call.tcBalance);
          call.isShort = false;
          return call;
        });
      }
    },

    addOverpayment (payment) {
      this.PAYMENT__createOverpaymentInvoice({
        key: payment.lPaymentKey,
        amount: payment.tcUnappliedAmount,
        success: () => {
          this.loadData();
        }
      });
    },

    applyPayment () {
      const calls = this.pendingApplications.map(call => {
        return {
          CallKey: call.lKey,
          Amount: call.pendingApplicationBalance,
          ShortPay: call.isShort
        };
      });

      this.PAYMENT__applyToCall({
        key: this.selectedPayment.lPaymentKey,
        call: calls,
        success: () => {
          this.$hub.$emit(EVENT_SUCCESS, 'Payment applied.');

          this.resetApplication();
          this.loadData();
        }
      });
    },

    openPaymentModal ({ lPaymentKey }) {
      this.paymentModal.key = lPaymentKey;
      this.paymentModal.isVisible = true;
      this.paymentModal.componentIsActive = true;
    },

    openTowPayModal ({ lPaymentKey }) {
      this.towPayModal.key = lPaymentKey;
      this.towPayModal.isVisible = true;
      this.towPayModal.componentIsActive = true;
    },

    closePaymentModal () {
      this.paymentModal.key = null;
      this.paymentModal.isVisible = false;

      this.loadData();

      setTimeout(() => {
        this.paymentModal.componentIsActive = false;
      }, 500);
    },

    closeTowPayModal () {
      this.towPayModal.isVisible = false;
      this.loadData();
    },

    afterLoadData () {
      if (this.callsData && Array.isArray(this.callsData)) {
        this.callsData = this.callsData.map(call => {
          call.pendingApplicationBalance = 0;
          call.realTimeBalance = Number(call.tcBalance);
          call.isShort = false;
          return call;
        });
      }

      if (this.paymentsData && Array.isArray(this.paymentsData)) {
        this.paymentsData = this.paymentsData.map(payment => {
          payment.realTimeBalance = payment.tcUnappliedAmount;
          return payment;
        });
      }
    },

    distributePaymentBalance () {
      if (!this.callsData || !Array.isArray(this.callsData) || !this.selectedPayment) {
        return;
      }

      let realTimeBalance = this.selectedPayment.realTimeBalance;
      let index = 0;

      while (realTimeBalance > 0 && index < this.callsData.length) {
        let call = this.callsData[index];
        index++;

        // If the call has a balance, apply the payment to it
        if (call.tcBalance > 0) {
          const maxApplicableBalance = Math.min(Number(call.tcBalance), Number(realTimeBalance));

          call.pendingApplicationBalance = maxApplicableBalance;
          call.realTimeBalance = Number(call.tcBalance) - Number(call.pendingApplicationBalance);

          this.changePendingPayment(call);

          realTimeBalance -= maxApplicableBalance;
        } else {
          // If the call has no balance, move on to the next call
          continue;
        }
      }
    },

    fillPendingApplicationBalance (record) {
      record.pendingApplicationBalance = Math.min(Number(record.tcBalance), Number(this.selectedPayment.realTimeBalance));
      record.realTimeBalance = Number(record.tcBalance) - Number(record.pendingApplicationBalance);

      this.changePendingPayment(record);
    },

    clearPendingApplicationBalance (record) {
      record.pendingApplicationBalance = 0;
      record.isShort = false;
      record.realTimeBalance = Number(record.tcBalance);

      this.changePendingPayment(record);
    },

    changePendingPayment (record) {
      if (!this.callsData || !Array.isArray(this.callsData)) {
        return;
      }

      this.callsData = this.callsData.map(call => {
        if (call.lCallKey === record.lKey) {
          call.pendingApplicationBalance = record.pendingApplicationBalance;
          call.isShort = record.isShort;
          call.realTimeBalance = record.realTimeBalance;
        }
        return call;
      });
    },

    addWithMethod (method) {
      switch (method) {
        case 'towpay':
          this.clearSelectedPayment();
          this.openTowPayModal({ lPaymentKey: null });
          break;

        case 'manual':
        default:
          this.clearSelectedPayment();
          this.openPaymentModal({ lPaymentKey: null });
          break;
      }
    },

    onPaymentAdded (payment) {
      const paymentKey = payment.lPaymentKey || payment.key;

      if (this.$store.state.payment.selectNewlyAddedPayment) {
        this.$store.state.payment.selectedPaymentKey = paymentKey;
      }
    },

    openCallRecord (record) {
      let key = this.callsConfig.readRouteKey || this.callsConfig.recordKeyName;

      this.$router.push({
        name: this.callsConfig.readRouteName,
        params: { key: record[key] }
      });
    },

    getSubcompanies () {
      return new Promise(resolve => {
        this.$store.dispatch('TOPSCOMPANY__getSubterminals', {
          success: response => resolve(response)
        });
      });
    },

    getSubcompanyDetails (subterminalKey) {
      return new Promise(resolve => {
        this.$store.dispatch('TOPSCALL__getSubcompanyDetails', {
          subterminalKey,
          success: response => resolve(response)
        });
      });
    },

    async setSubcompanyDetails (subcompanyKey) {
      const selectedSubcompany = this.$store.state.payment.subcompanies.find(subcompany => subcompany.Key === subcompanyKey);
      const detail = await this.getSubcompanyDetails(subcompanyKey);
      this.$store.state.payment.selectedSubcompany = { ...selectedSubcompany, ...detail };

      const processingTypeKey = get(this.$store.state.payment, 'selectedSubcompany.lCreditCardProcessingTypeKey', null);
      this.$store.state.payment.isTowPayAvailable = processingTypeKey === VALUE_ID.creditCardProcessingType.towpay;
    },

    async evaluateTowPayAvailability () {
      this.$store.state.payment.subcompanies = await this.getSubcompanies();

      if (this.$store.state.payment.subcompanies.length > 0) {
        this.setSubcompanyDetails(this.$store.state.payment.subcompanies[0].Key);
      }
    }
  },

  async mounted () {
    this.evaluateTowPayAvailability();

    if (this.$store.state.payment.selectedCustomerKey) {
      this.viewConfig.dataAdditional.Customer = this.$store.state.payment.selectedCustomerKey;
      this.callsConfig.dataAdditional.Customer = this.$store.state.payment.selectedCustomerKey;

      this.viewConfig.shouldImmediatelyLoadData = true;
      this.callsConfig.shouldImmediatelyLoadData = true;
    } else {
      // document.querySelector('.customer__search-input input').click();
    }

    this.$store.state.payment.customers = await this.getCustomers();
  }
};
</script>
