<template>
  <div id="record-view" data-page="call" :data-search-mode="searchMode">
    <app-titlebar :title="'Call · ' + callKey" v-if="!searchMode">
      <spotlight-control
        :noun="viewConfig.searchNoun"
        @setFilters="setFilters"
        @resetFilters="setFilters">
      </spotlight-control>
    </app-titlebar>

    <div class="loader" v-if="loaderVisible"></div>

    <section class="content-section">
      <div class="_liner" v-show="!loaderVisible">
        <main class="sections">
          <section class="pinned-sections" :data-pinned="sectionsArePinned">
            <div class="_liner">
              <form-section
                v-for="(section, name) in pinnedSections"
                :key="name"
                :id="name"
                :title="section.title"
                :fixed="true">
                <component
                  :is="section.componentName"
                  :call="call"
                  :searchMode="searchMode"
                  @addSpeedBump="addSpeedBump">
                </component>
              </form-section>
            </div>
          </section>

          <section class="floating-sections" :data-pinned="sectionsArePinned">
            <form-section
              v-for="(section, name) in floatingSections"
              :key="name"
              :id="name"
              :title="section.title"
              :fixed="true">
              <component
                :is="section.componentName"
                :call="call"
                :searchMode="searchMode"
                :price-repository="priceRepository"
                @addSpeedBump="addSpeedBump"
                @setBalance="value => { liveBalance = Number(value) }">
              </component>
            </form-section>

            <app-modified-metadata
              class="modified-metadata"
              style="margin-top: 1rem"
              v-if="!isNewRecord"
              :record="call"
              :config="viewConfig"
              modifiedAtAlias="dLastModified"
              modifiedByAlias="lUserKey_LastModified"
              recordKeyAlias="lCallKey">
            </app-modified-metadata>
          </section>
        </main>

        <aside class="mini-map" v-if="!searchMode">
          <div class="_liner">

            <CallSketch :call="call"
              :searchMode="searchMode"
              :priceRepository="priceRepository"
              :liveBalance="liveBalance" />

            <ul class="_links">
              <li class="_link"
                v-for="(section, name) in sectionsProxy"
                @click="lookAt(name)"
                :key="name"
                :data-pinned="sectionsArePinned && ['tow', 'vehicle'].includes(name)">
                {{ section.title }}
              </li>
            </ul>

          </div>
        </aside>
      </div>
    </section>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button @click="saveAndClose()" type="primary" :disabled="!canSave">Save</app-button>
        <app-button @click="save()" :disabled="!canSave">Save &amp; Stay</app-button>
        <app-button @click="duplicate" :disabled="!canDuplicate">Duplicate</app-button>
        <app-button @click="toggleActions(true)" :disabled="!canDoAction">Actions</app-button>
        <app-button @click="cancel" :disabled="!canClose">Close</app-button>
      </template>

      <record-reel
        :record-key="callKey"
        :key-alias="viewConfig.recordKeyName"
        :exitSpeedBumps="exitSpeedBumps"
        @jump="jumpTo"
        @resetSpeedBumps="resetSpeedBumps">
      </record-reel>

      <app-button type="success" size="normal" @click="addCall" v-if="canAddCall" style="border-radius: 10rem;">
        <i class="far fa-plus"></i>&nbsp;Add
      </app-button>
    </app-footerbar>

    <actions
      :show="actionsComponent.visible"
      :call-key="callKey"
      :subterminal-key="call.lSubterminalKey"
      :mileage-required="call.bMileageRequired"
      :mutations="actionsComponent.mutations"
      :dates-visible="actionsComponent.datesVisible"
      @close="toggleActions(true)"
      @notify="notify">
    </actions>

    <DMVCommunications v-if="showDMV" :callKey="callKey"  @close="onHideDMVModal" />
  </div>
</template>

<script>
import Call from './Call.vue';
import RecordReel from '@/components/features/RecordReel.vue';

export default {
  name: 'call-reel',

  extends: Call,

  components: {
    RecordReel
  },

  methods: {
    jumpTo (key) {
      this.$router.replace({
        name: 'CallReel',
        params: { key: key }
      });

      this.readCall();
    }
  }
};
</script>
