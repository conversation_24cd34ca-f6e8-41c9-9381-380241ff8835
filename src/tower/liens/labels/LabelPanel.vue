<template>
  <div id="label-panel">
    <ActionSelect
      v-model="$store.state.lien.selectedLabelLayoutKey"
      :options="$store.getters['lien.activeLabelLayouts']"
      valueAlias="Value"
      keyAlias="Key">
      Layout
    </ActionSelect>

    <section class="label-profile flex flex-center gap-1 small is-quiet" v-if="$store.state.lien.selectedLabelProfile">
      <div title="Number of lines"><i class="far fa-line-height"></i> {{ $store.state.lien.selectedLabelProfile.tMaxLinesPerLabel }}</div>
      <div title="Number of characters per line"><i class="far fa-a"></i> {{ $store.state.lien.selectedLabelProfile.tMaxCharsPerLabel }}</div>
      <div title="Orientation">
        <template v-if="$store.state.lien.selectedLabelProfile.bPortrait">
          <i class="far fa-rectangle-vertical"></i>
          Portrait
        </template>
        <template v-else>
          <i class="far fa-rectangle-vertical fa-rotate-90"></i>
          Landscape
        </template>
      </div>
    </section>

    <div class="v-space"></div>

    <Expandable open>
      <template slot="summary">Add <i class="far fa-plus"></i></template>

      <ActionNumber v-model="$store.state.lien.assistAddLabelCount" min="1" max="9999" step="1" />

      <ActionButton @click="addLabel" variant="blue">
        Add {{ $store.state.lien.assistAddLabelCount }}
      </ActionButton>
    </Expandable>

    <Expandable>
      <template slot="summary">Copy <i class="far fa-copy"></i></template>

      <ActionNumber v-model="$store.state.lien.assistCopyLabelCount" min="1" max="9999" step="1" />

      <ActionButton @click="copyLabels" variant="blue" :disabled="!$store.state.lien.selectedLabelKeys.length">
        Copy {{ $store.state.lien.assistCopyLabelCount }}x
      </ActionButton>
    </Expandable>

    <Expandable>
      <template slot="summary">
        <span>Print <span v-show="$store.state.lien.selectedLabelKeys.length">{{ $store.state.lien.selectedLabelKeys.length }} selected</span></span>
        <i class="far fa-print"></i>
      </template>

      <ActionNumber v-model="$store.state.lien.labelStartPosition" min="1" max="9999" step="1">
        Start position
      </ActionNumber>

      <ActionButton @click="printLabels" variant="blue" :disabled="!$store.state.lien.labels.length">
        Print
      </ActionButton>
    </Expandable>

    <Expandable>
      <template slot="summary">
        <span>Export <span v-show="$store.state.lien.selectedLabelKeys.length">{{ $store.state.lien.selectedLabelKeys.length }} selected</span></span>
        <i class="far fa-file-export"></i>
      </template>

      <ActionButton @click="exportLabelsToCSV" :disabled="!$store.state.lien.labels.length">
        Export to CSV
      </ActionButton>
      <ActionButton @click="printLabels" :disabled="!$store.state.lien.labels.length">
        Export to PDF
      </ActionButton>
    </Expandable>

    <div class="v-space"></div>

    <ActionButton @click="selectNone" :disabled="!$store.state.lien.selectedLabelKeys.length">
      Clear selected
    </ActionButton>

    <AppTip v-if="$store.state.lien.hasUnprintedLabels">
      <i class="far fa-exclamation-triangle pure-orange" slot="icon"></i>
      <p>Careful, unprinted&nbsp;labels.</p>
    </AppTip>

    <ActionButton @click="deleteLabels" :disabled="!$store.state.lien.labels.length">
      Delete all
    </ActionButton>
  </div>
</template>

<script>import { uniqueId } from 'lodash-es';


import Expandable from '../inputs/Expandable.vue';
import ActionSelect from '../inputs/Select.vue';
import ActionButton from '../inputs/Button.vue';
import ActionNumber from '../inputs/Number.vue';

export default {
  name: 'label-panel',

  components: {
    Expandable,
    ActionSelect,
    ActionButton,
    ActionNumber
  },

  inject: ['addLabel'],

  watch: {
    '$store.state.lien.selectedLabelLayoutKey': {
      immediate: true,
      async handler (value) {
        this.$store.state.lien.selectedLabelProfile = await this.fetchLabelProfile(value);
      }
    },

    '$store.state.lien.labels': {
      immediate: true,
      handler (value) {
        this.$store.state.lien.hasUnprintedLabels = this.$store.state.lien.labels.length > 0;
      }
    }
  },

  methods: {
    fetchLabelLayouts () {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('LABEL__getLayouts', {
          success: response => {
            resolve(response);
          },
          fail: error => {
            reject(error);
          }
        });
      });
    },

    fetchLabelProfile (layout) {
      if (!layout) return;

      return new Promise((resolve, reject) => {
        this.$store.dispatch('LABEL__read', {
          layout: layout,
          success: response => {
            resolve(response);
          },
          fail: error => {
            reject(error);
          }
        });
      });
    },

    copyLabels () {
      const count = this.$store.state.lien.assistCopyLabelCount;

      for (let i = 0; i < count; i++) {
        this.$store.state.lien.labels.push(...this.$store.getters['lien.selectedOrAllLabels'].map(label => {
          const { key, ...rest } = label;
          const newLabel = { key: uniqueId(), ...rest };
          return newLabel;
        }));
      }
    },

    deleteLabels () {
      this.$store.state.lien.labels = [];
    },

    printLabels () {
      let labels = this.$store.getters['lien.selectedOrAllLabels'].map(label => {
        const { key, ...rest } = label;
        return rest;
      });

      this.$store.dispatch('LABEL__create', {
        labels: labels,
        layout: this.$store.state.lien.selectedLabelLayoutKey,
        startPosition: this.$store.state.lien.labelStartPosition,
        success: response => {
          if ('LabelsURL' in response) {
            window.open(`${import.meta.env.VITE_TXI_API}?${response.LabelsURL}`, '_blank');
            this.$store.state.lien.hasUnprintedLabels = false;
          }
        }
      });
    },

    exportLabelsToCSV () {
      const csvContent = 'data:text/csv;charset=utf-8,' + this.$store.getters['lien.selectedOrAllLabels'].map(label => {
        const { key, ...rest } = label;
        return Object.values(rest).join(',');
      }).join('\n');
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', 'labels.csv');
      document.body.appendChild(link);
      link.click();
    },

    selectNone () {
      this.$store.state.lien.selectedLabelKeys = [];
    }
  },

  async mounted () {
    this.$store.state.lien.labelLayouts = await this.fetchLabelLayouts();
    this.$store.state.lien.selectedLabelLayoutKey = this.$store.getters['lien.activeLabelLayouts'][0].Key;
  }
};
</script>

<style scoped>
#label-panel {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  padding: 1rem;
  overflow-y: scroll;

  .label-profile {
    padding: 0 1rem;
  }
}
</style>
