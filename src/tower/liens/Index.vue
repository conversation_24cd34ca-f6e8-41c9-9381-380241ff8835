<template>
  <div id="lien-view">
    <section class="_tabs">
      <tab-group v-model="$store.state.lien.selectedTab" @change="onTabChange">
        <tab-item value="processes">Processes</tab-item>
        <tab-item value="labels">Labels</tab-item>
        <tab-item value="search">Search</tab-item>
      </tab-group>
    </section>

    <template v-if="$store.state.lien.selectedTab === 'processes'">
      <ProcessesPanel
        :security-access="securityAccess"
        :selected-record-keys="selectedRecordKeys"
        @on-step-complete="reloadStepsView"
        @on-step-hold="reloadStepsView"
        @on-process-task-fail="onProcessTaskError" />

      <section id="process-results">
        <transition name="fade" mode="out-in">
          <grid
            title=""
            key="grid"
            class="grid -step-calls"
            :grid="stepsGridSettings"
            :data="stepsGridData"
            :refreshedAt="refreshedAt"
            :config="viewConfig"
            :multiselect="true"
            :show-loader="showLoader"
            @refresh="refresh"
            @openRecord="openRecord"
            @exportData="exportData"
            @save="save">
          </grid>
        </transition>
      </section>
    </template>

    <template v-if="$store.state.lien.selectedTab === 'labels'">
      <LabelPanel />
      <LabelResults />
    </template>

    <template v-if="$store.state.lien.selectedTab === 'search'">
      <SearchPanel
        :security-access="securityAccess"
        :selected-record-keys="selectedRecordKeys"
        @on-show-ready-calls-change="onReadyCallsChange"
        @on-batch-step-activate="reloadSearchView"
        @on-batch-step-skip="reloadStepsView"
        @on-batch-lien-start="reloadSearchView"
        @on-batch-lien-terminate="reloadSearchView" />

      <section id="search-results">
        <transition name="fade" mode="out-in">
          <grid
            title=""
            key="grid"
            class="grid -search"
            :grid="searchGridSettings"
            :data="searchGridData"
            :refreshedAt="refreshedAt"
            :config="viewConfig"
            :multiselect="true"
            :show-loader="showLoader"
            @refresh="refresh"
            @openRecord="openRecord"
            @exportData="exportData"
            @save="save">
          </grid>
        </transition>
      </section>
    </template>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="refresh">
    </quick-views>

  </div>
</template>

<script>import { find, clone, castArray, get, reject, map, forOwn, has, set, forEach, uniqueId } from 'lodash-es';


import datefns from 'date-fns';
import Access from '@/utils/access.js';
import Grid from '@/components/features/Grid.vue';
import { mapGetters, mapActions } from 'vuex';
import { GRID_KEY } from '@/config.js';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import ProcessesPanel from './ProcessesPanel.vue';
import SearchPanel from './search/SearchPanel.vue';
import LabelPanel from './labels/LabelPanel.vue';
import LabelResults from './labels/LabelResults.vue';

export default {
  name: 'lien-view',

  extends: RecordsView,

  components: {
    Grid,
    ProcessesPanel,
    SearchPanel,
    LabelPanel,
    LabelResults
  },

  provide () {
    return {
      addLabel: this.addLabel
    };
  },

  data () {
    return {
      viewConfig: {
        uuid: 'lien-batch-screen',
        noun: 'LienBatch',
        dataAdditional: {},
        readRouteName: 'Call',
        requireData: false,
        requireFilters: false,
        recordKeyName: 'lCallKey',
        shouldImmediatelyLoadData: false,
        shouldImmediatelySelectAllRecords: false
      },

      setDate: datefns.format(new Date(), 'MM/DD/YYYY'),
      processedCalls: {},
      lettersSentDate: datefns.format(new Date(), 'MM/DD/YYYY'),
      labelFormat: '',
      startLabel: 1,
      certificationNumber: '',
      stepsGridKey: GRID_KEY.lienStepCalls,
      searchGridKey: GRID_KEY.lienSearchCalls
    };
  },

  computed: {
    ...mapGetters(['__busy']),

    canViewReadyCalls () {
      return Access.has('liens.isReadyCallsVisible') && !this.anyRecordsAreSelected;
    },

    canEdit () {
      return Access.has('liens.editBatch');
    },

    canProcessTasks () {
      return this.canEdit &&
        this.anyRecordsAreSelected &&
        this.activeStepTasks.length > 0;
    },

    canCompleteStep () {
      return this.canEdit && this.anyRecordsAreSelected;
    },

    canSkipStep () {
      if (!this.$store.getters['lien.selectedStep']) {
        return false;
      }

      return this.canEdit &&
        this.anyRecordsAreSelected &&
        !this.$store.getters['lien.selectedStep'];
    },

    canHoldStep () {
      return this.canEdit && this.anyRecordsAreSelected;
    },

    canBatchActivateStep () {
      return this.canEdit && this.anyRecordsAreSelected && !this.$store.state.lien.isReadyCallsVisible;
    },

    canBatchSkipStep () {
      return this.canEdit && this.anyRecordsAreSelected && !this.$store.state.lien.isReadyCallsVisible;
    },

    canBatchStartLien () {
      return this.canEdit && this.anyRecordsAreSelected;
    },

    canBatchTerminateLien () {
      return this.canEdit && this.anyRecordsAreSelected && !this.$store.state.lien.isReadyCallsVisible;
    },

    securityAccess () {
      return {
        canProcessTasks: this.canProcessTasks,
        canCompleteStep: this.canCompleteStep,
        canSkipStep: this.canSkipStep,
        canHoldStep: this.canHoldStep,
        canBatchActivateStep: this.canBatchActivateStep,
        canBatchSkipStep: this.canBatchSkipStep,
        canBatchStartLien: this.canBatchStartLien,
        canBatchTerminateLien: this.canBatchTerminateLien,
        canViewReadyCalls: this.canViewReadyCalls
      };
    },

    stepsGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.stepsGridKey]);

      return settings || {};
    },

    viewSettingsWithStepsGrid () {
      let settings = clone(this.RECORDS__settings);

      settings.Grids = castArray(this.stepsGridSettings);
      settings.StepKey = this.$store.state.lien.selectedStepKey;

      return settings || {};
    },

    stepsGridData () {
      return get(this.viewData, `Grids[${this.stepsGridKey}]`, []);
    },

    searchGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.searchGridKey]);

      return settings || {};
    },

    viewSettingsWithSearchGrid () {
      let settings = clone(this.RECORDS__settings);

      settings.Grids = castArray(this.searchGridSettings);
      settings.isReadyCallsVisible = this.$store.state.lien.isReadyCallsVisible;

      return settings || {};
    },

    searchGridData () {
      return get(this.viewData, `Grids[${this.searchGridKey}]`, []);
    },

    activeStepTasks () {
      let tasks = get(this.$store.getters['lien.selectedStep'], 'Tasks', []);

      return reject(tasks, 'bSkipBatch');
    },

    selectedRecordKeys () {
      return map(this.__selectedRecords, record => {
        return record[this.viewConfig.recordKeyName];
      });
    },

    callsSuccessfullyProcessed () {
      let calls = {};

      forOwn(this.processedCalls.Calls, (call, key) => {
        if (!has(call, 'Errors')) set(calls, key, call);
      });

      return calls;
    }
  },

  watch: {
    '$store.state.lien.selectedStepKey': {
      immediate: true,
      handler: function () {
        this.__selectRecords([]);
        if (this.$store.state.lien.selectedStepKey) {
          this.loadData(this.viewSettingsWithStepsGrid);
        }
      }
    }
  },

  methods: {
    ...mapActions([
      'LIENBATCH__start',
      'LIENBATCH__skipStep',
      'LIENBATCH__terminate',
      'LIENBATCH__activateStep'
    ]),

    onTabChange (tab) {
      this.__selectRecords([]);

      if (tab === 'search') {
        this.loadData(this.viewSettingsWithSearchGrid);
      } else {
        this.$store.state.lien.isReadyCallsVisible = false;
      }
    },

    // Override parent method
    refresh () {
      this.loadData(this.viewSettingsWithStepsGrid);
    },

    async onReadyCallsChange (isReadyCallsVisible) {
      if (!isReadyCallsVisible && this.searchGridSettings.Filters.length === 0) {
        this.$router.push({
          name: 'GridSearch',
          params: { key: this.searchGridKey },
          query: { noun: this.viewConfig.noun }
        });
      } else {
        await this.$awaitNextTicks(2);
        this.loadData(this.viewSettingsWithSearchGrid);
      }
    },

    onProcessTaskError (calls) {
      this.pushErrorsIntoGrid(calls);
    },

    pushErrorsIntoGrid (calls = null) {
      if (!Array.isArray(calls)) {
        calls = [calls];
      }

      forEach(calls, call => {
        const foundCall = find(this.stepsGridData, { [this.viewConfig.recordKeyName]: call.Key });

        if (foundCall) {
          this.$set(foundCall, 'Errors', call.Errors);
        }
      });
    },

    reloadStepsView () {
      this.loadData(this.viewSettingsWithStepsGrid);
      this.__selectRecords([]);
    },

    reloadSearchView () {
      this.loadData(this.viewSettingsWithSearchGrid);
      this.__selectRecords([]);
    },

    // Override parent method
    exportData ({ format, gridKey }) {
      let actionName = '';
      let targetGrid = gridKey === this.stepsGridKey
        ? this.viewSettingsWithStepsGrid.Grids
        : this.viewSettingsWithSearchGrid.Grids;

      switch (format) {
        case 'CSVFile':
          actionName = 'RECORDS__exportCSV';
          break;

        case 'PDF':
          actionName = 'RECORDS__exportPDF';
          break;
      }

      this[actionName]({
        noun: this.viewConfig.noun,
        viewKey: this.viewKey,
        gridKey: gridKey,
        name: this.viewSettings.Title,
        description: this.viewSettings.Description,
        grids: targetGrid,
        stepKey: this.$store.state.lien.selectedStepKey
      });
    },

    addLabel (label = null) {
      const count = this.$store.state.lien.assistAddLabelCount;
      const maxLines = this.$store.state.lien.selectedLabelProfile.tMaxLinesPerLabel;

      if (label) {
        label.key = uniqueId();
        this.$store.state.lien.labels.push(label);
      } else {
        for (let i = 0; i < count; i++) {
          const newLabel = { key: uniqueId() };

          for (let lineNumber = 1; lineNumber <= maxLines; lineNumber++) {
            newLabel[lineNumber] = '';
          }

          this.$store.state.lien.labels.push(newLabel);
        }
      }
    },
  },

  mounted () {
    this.$store.state.addCall.shouldStayOnSave = false;

    setTimeout(() => {
      this.loadData(this.viewSettingsWithSearchGrid);
    }, 1000);
  },

  beforeRouteLeave (to, from, next) {
    // Check if there are unprinted labels
    if (this.$store.state.lien.hasUnprintedLabels) {
      this.$confirm('Pump the brakes. You have unprinted labels.', 'Unprinted labels', {
        confirmButtonText: 'Stay and print',
        cancelButtonText: 'Leave anyway',
        type: 'warning'
      }).then(() => {
        // User clicked "Stay and print"
        next(false); // Cancel navigation
      }).catch(() => {
        // User clicked "Leave anyway"
        next(); // Allow navigation
      });
    } else {
      // No unprinted labels, allow navigation
      next();
    }
  },
};
</script>

<style scoped>
#lien-view {
  position: relative;

  display: grid;
  grid-template-columns: 300px 1fr;
  grid-template-rows: var(--titlebar-height) 1fr;
  grid-template-areas:
    "tabbar calls"
    "toolbar calls";

  height: 100%;

  ._tabs {
    grid-area: tabbar;

    display: grid;
    place-content: center;
  }

  > #processes-panel,
  > #label-panel,
  > #search-panel {
    grid-area: toolbar;
  }

  > #process-results,
  > #label-results,
  > #search-results {
    grid-area: calls;
  }
}
</style>
