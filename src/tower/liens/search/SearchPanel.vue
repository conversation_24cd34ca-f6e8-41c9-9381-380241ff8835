<template>
  <div id="search-panel">
    <ReadyCalls
      :disabled="!securityAccess.canViewReadyCalls"
      @on-show-ready-calls-change="$emit('on-show-ready-calls-change', $event)" />

    <div class="v-space"></div>

    <ActivateStep
      :disabled="!securityAccess.canBatchActivateStep"
      :selected-record-keys="selectedRecordKeys"
      @on-batch-step-activate="$emit('on-batch-step-activate')" />

    <SkipStep
      :disabled="!securityAccess.canBatchSkipStep"
      :selected-record-keys="selectedRecordKeys"
      @on-batch-step-skip="$emit('on-batch-step-skip')" />

    <div class="v-space"></div>

    <StartLien
      :disabled="!securityAccess.canBatchStartLien"
      :selected-record-keys="selectedRecordKeys"
      @on-batch-lien-start="$emit('on-batch-lien-start')" />

    <TerminateLien
      :disabled="!securityAccess.canBatchTerminateLien"
      :selected-record-keys="selectedRecordKeys"
      @on-batch-lien-terminate="$emit('on-batch-lien-terminate')" />
  </div>
</template>

<script>
import ReadyCalls from './actions/ReadyCalls.vue';
import ActivateStep from './actions/ActivateStep.vue';
import SkipStep from './actions/SkipStep.vue';
import StartLien from './actions/StartLien.vue';
import TerminateLien from './actions/TerminateLien.vue';

export default {
  name: 'search-panel',

  components: {
    ReadyCalls,
    ActivateStep,
    SkipStep,
    StartLien,
    TerminateLien
  },

  props: {
    securityAccess: {
      type: Object,
      required: true,
      default: () => {}
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  }
};
</script>

<style scoped>
#search-panel {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  padding: 1rem;
  border-right: 1px solid var(--body-border);
}
</style>
