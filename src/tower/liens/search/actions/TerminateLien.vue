<template>
  <Expandable>
    <template slot="summary">Terminate Lien</template>

    <ActionCheckbox v-model="removePricingItems">
      Remove Pricing Items
    </ActionCheckbox>

    <ActionButton @click="onClick" :disabled="disabled">
      Terminate
    </ActionButton>
  </Expandable>
</template>

<script>
import Expandable from '../../inputs/Expandable.vue';
import ActionCheckbox from '../../inputs/Checkbox.vue';
import ActionButton from '../../inputs/Button.vue';

export default {
  name: 'terminate-lien',

  components: {
    Expandable,
    ActionCheckbox,
    ActionButton
  },

  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      removePricingItems: null
    };
  },

  methods: {
    onClick () {
      this.$store.dispatch('LIENBATCH__terminate', {
        removePricingItems: this.removePricingItems,
        callKeys: this.selectedRecordKeys,
        callback: () => {
          this.$emit('on-batch-lien-terminate');
        }
      });
    }
  }
};
</script>
