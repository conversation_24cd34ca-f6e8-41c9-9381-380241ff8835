<template>
  <tab-group
    class="ready-calls-control"
    v-model="$store.state.lien.isReadyCallsVisible"
    @change="onChange"
    :disabled="disabled">
    <tab-item :value="true">Ready calls</tab-item>
    <tab-item :value="false">Filtered calls</tab-item>
  </tab-group>
</template>

<script>
import ActionCheckbox from '../../inputs/Checkbox.vue';

export default {
  name: 'ready-calls',

  components: {
    ActionCheckbox
  },

  props: {
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  methods: {
    onChange () {
      this.$emit('on-show-ready-calls-change', this.$store.state.lien.isReadyCallsVisible);
    }
  }
};
</script>

<style scoped>
.ready-calls-control {
  margin: 0 auto;
}
</style>
