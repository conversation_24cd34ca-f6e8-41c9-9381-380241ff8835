<template>
  <div class="task-item">
    <header class="header">
      <div class="name">{{ task.vc50TaskAlias }}</div>
      <div class="description is-small is-quiet">{{ task.vc255Description }}.</div>

      <tab-group v-model="selectedTab">
        <tab-item value="process">Prereqs</tab-item>
        <tab-item value="result" :disabled="!isProcessClicked">Results</tab-item>
      </tab-group>
    </header>

    <section class="process-section" v-if="selectedTab === 'process'">
      <AppTip v-if="task.tIterations > 1">
        <i class="fas fa-circle-info pure-aqua" slot="icon"></i>
        <p>{{ task.tIterations }} copies should be printed</p>
      </AppTip>
      <AppTip v-if="hasLettersToSend">
        <i class="fas fa-circle-info pure-aqua" slot="icon"></i>
        <p>Marked sent at {{ lettersSentDate }}</p>
      </AppTip>
      <AppTip v-if="hasLabelsToPrint">
        <i class="fas fa-circle-info pure-aqua" slot="icon"></i>
        <p>{{ labelFormat }} starting at position {{ startLabel }}</p>
      </AppTip>
      <AppTip v-if="hasDateToSet">
        <i class="fas fa-circle-info pure-aqua" slot="icon"></i>
        <p>Date set to {{ setDate }}</p>
      </AppTip>
      <AppTip v-if="usesCertificationNumber">
        <i class="fas fa-circle-info pure-aqua" slot="icon"></i>
        <p>Using certification number {{ certificationNumber }}</p>
      </AppTip>

      <!-- <ActionButton @click="processTask" :disabled="!canProcessTask">
        Process this task
      </ActionButton> -->
    </section>

    <section class="result-section" v-if="selectedTab === 'result'">
      <AppTip v-if="callsWithoutErrors.length">
        <i class="fas fa-circle-check pure-green" slot="icon"></i>
        <p>{{ callsWithoutErrors.length }} calls were processed.</p>
      </AppTip>

      <template v-for="call in callsWithErrors">
        <AppTip v-for="error in call.Errors" :key="error.Message">
          <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
          <p><strong>Call {{ call.Key }}</strong> :: {{ error.Message }}</p>
        </AppTip>
      </template>

      <section class="document-section" v-if="documentsProxy.length">
        <AppTip>
          <i class="fas fa-exclamation-triangle pure-orange" slot="icon"></i>
          <p class="small">The browser may prevent documents from automatically opening. They are listed here to open manually as&nbsp;needed.</p>
        </AppTip>

        <div class="v-space"></div>

        <ul class="document-list">
          <li class="document" v-for="document in documentsProxy" :key="document.Type">
            <a :href="document.URL" target="_blank">{{ document.Type }}</a>
          </li>
        </ul>
      </section>
    </section>
  </div>
</template>

<script>import { get, forEach } from 'lodash-es';


import { VALUE_ID } from '@/config';
import ActionButton from '@/tower/liens/inputs/Button.vue';

export default {
  name: 'task-item',

  components: {
    ActionButton
  },

  props: {
    task: {
      type: Object,
      required: true,
      default: () => ({})
    },
    lettersSentDate: {
      type: String,
      default: null
    },
    labelFormat: {
      type: String,
      default: null
    },
    startLabel: {
      type: Number,
      default: null
    },
    setDate: {
      type: String,
      default: null
    },
    certificationNumber: {
      type: String,
      default: null
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  inject: ['addLabel'],

  data () {
    return {
      selectedTab: 'process', // process | result
      isProcessClicked: false,
      processResponse: {}
    };
  },

  computed: {
    hasLettersToSend () {
      return this.task.lLienTaskTypeKey === VALUE_ID.lienTaskType.letter;
    },

    hasLabelsToPrint () {
      // Manually disabled in case we need it back
      // return this.task.lLienTaskTypeKey === VALUE_ID.lienTaskType.label;
      return false;
    },

    hasDateToSet () {
      return this.task.lLienTaskTypeKey === VALUE_ID.lienTaskType.setDate;
    },

    usesCertificationNumber () {
      return this.task.bUsesCertificationNum;
    },

    canProcessTask () {
      if (this.hasLettersToSend && !this.lettersSentDate) return false;
      if (this.hasLabelsToPrint && !this.labelFormat) return false;
      if (this.hasDateToSet && !this.setDate) return false;
      if (this.usesCertificationNumber && !this.certificationNumber) return false;

      return true;
    },

    documentsProxy () {
      return get(this.processResponse, 'Documents', []);
    },

    callsProxy () {
      const callsObject = get(this.processResponse, 'Calls', {});
      return Object.entries(callsObject).map(([key, value]) => ({ ...value, Key: key }));
    },

    callsWithoutErrors () {
      return this.callsProxy.filter(call => (call.Errors || []).length === 0);
    },

    callsWithErrors () {
      return this.callsProxy.filter(call => (call.Errors || []).length > 0);
    }
  },

  methods: {
    resetState () {
      this.selectedTab = 'process';
      this.isProcessClicked = false;
      this.processResponse = {};
    },

    async processTask () {
      if (!this.selectedRecordKeys.length) return;
      if (!this.canProcessTask) return;

      this.isProcessClicked = true;

      // Mock response
      // this.selectedTab = 'result';
      // await this.handleResponse(this.mockSuccessResponse());
      // await this.handleResponse(this.mockFailResponse());

      this.$store.dispatch('LIENBATCH__processTask', {
        stepKey: this.task.lLienStepKey,
        order: this.task.tOrder,
        lettersSentDate: this.lettersSentDate,
        labelFormat: this.labelFormat,
        setDate: this.setDate,
        certificationNumber: this.certificationNumber,
        callKeys: this.selectedRecordKeys,
        returnRawResponse: true,
        always: async response => {
          this.selectedTab = 'result';
          await this.handleResponse(response);
        }
      });

      return true;
    },

    /**
     * This handles both success and fail because it can be
     * both at once.
     */
    handleResponse (response = null) {
      return new Promise((resolve, reject) => {
        const dataVariant1 = get(response, 'data.Data', null);
        const dataVariant2 = get(response, 'Data', null);
        this.processResponse = dataVariant1 || dataVariant2;

        // Handle documents
        forEach(this.documentsProxy, document => {
          window.open(document.URL, '_blank');
        });

        // Handle labels
        this.callsProxy.forEach(call => {
          if ('Labels' in call) {
            call.Labels.forEach(label => {
              this.addLabel(label);
            });
          }
        });

        // Handle successes
        this.$emit('on-process-task-success', {
          calls: this.callsWithoutErrors,
          documents: this.documentsProxy
        });

        // Handle fails
        if (this.callsWithErrors.length > 0) {
          this.$emit('on-process-task-fail', this.callsWithErrors);
        }

        resolve(true);
      });
    },

    mockSuccessResponse () {
      return {
        Result: 'SUCCESS',
        Data: {
          Documents: [
            {
              Name: null,
              Type: 'PDF',
              URL: 'https://www.towxchange.net/tempreportsdata/66e82aebfc569d9cf9b14bf70a7630460acfb04a205f819524bb95c4c96b0564',
              Copies: 1
            }
          ],
          Calls: {
            1152: {
              DoNotProcess: false,
              Labels: [
                {
                  1: 'Good    [5009]',
                  2: '510 Crewdson',
                  3: 'Chattanooga, TN 37405'
                },
                {
                  1: 'Bad1    [5009]',
                  2: '510 Crewdson',
                  3: 'Chattanooga, TN 37402'
                },
                {
                  1: 'Bad2    [5009]',
                  2: '510',
                  3: 'Chattanooga, TN 37405'
                },
                {
                  1: 'Bad3    [5009]',
                  2: '510 Crewdson',
                  3: 'apt',
                  4: 'Chattavegas, TN 37405'
                },
                {
                  1: 'Bad4    [5009]',
                  2: '510 Credson',
                  3: 'Chattanooga, TN 37405'
                },
                {
                  1: 'Robert Ownerman    [5009]',
                  2: '499 EL CERRO DR',
                  3: 'SUNLAND PARK, NM 88806'
                }
              ]
            }
          }
        }
      };
    },

    mockFailResponse () {
      return {
        Calls: {
          3129: {
            DoNotProcess: true,
            Errors: [
              {
                Message: 'Error from API Server: Could Not Retrieve Lien Task Information for the Lien Step',
                Context: '<?xml version="1.0" encoding="UTF-8"?>n<!DOCTYPE towXResponse>n<towXResponse>n  <Errors>n    <Error>n      <Message>Could Not Retrieve Lien Task Information for the Lien Step</Message>n      <Context>244</Context>n      <DebugInfo>1917</DebugInfo>n      <ValidationError>false</ValidationError>n    </Error>n  </Errors>n</towXResponse>',
                DebugInfo: '',
                ValidationError: false
              }
            ]
          },
          3091: {
            DoNotProcess: true,
            Errors: [
              {
                Message: 'Error from API Server: Could Not Retrieve Lien Task Information for the Lien Step',
                Context: '<?xml version="1.0" encoding="UTF-8"?>n<!DOCTYPE towXResponse>n<towXResponse>n  <Errors>n    <Error>n      <Message>Could Not Retrieve Lien Task Information for the Lien Step</Message>n      <Context>244</Context>n      <DebugInfo>1917</DebugInfo>n      <ValidationError>false</ValidationError>n    </Error>n  </Errors>n</towXResponse>',
                DebugInfo: '',
                ValidationError: false
              }
            ]
          }
        }
      };
    }
  }
};
</script>

<style scoped>
.task-item {
  padding: 0.5rem;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.75rem;

  background-color: white;
}

.header {
  display: grid;
  grid-template-columns: 1fr max-content;
  grid-template-rows: min-content min-content;
  grid-template-areas:
    "name tab"
    "description tab";

  .name {
    grid-area: name;
  }

  .description {
    grid-area: description;
  }

  .tab-group {
    grid-area: tab;

    align-self: start;
  }
}

.-icon {
  margin-top: 3px;
  margin-right: .5em;
}

.-alias {
  grid-column: 2;
  max-width: 55ch;
}

.-description {
  grid-column: 2;
  opacity: .6;
  max-width: 55ch;
}

.process-section,
.result-section {
  display: flex;
  flex-direction: column;
  gap: 0.5em;

  margin-top: 1rem;
}

.document-list {
  list-style-type: none;
  padding: 0;

  .document a {
    display: grid;
    place-content: center;

    width: 4rem;
    aspect-ratio: 8.5 / 11;
    font-weight: bold;
    background: radial-gradient(at top left, white, hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.05));
    border: 0.1rem solid hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.2);
    border-top-right-radius: 1rem;

    &:hover {
      color: var(--pure-blue);
      background: radial-gradient(at top left, white, hsla(var(--pure-blue-h), var(--pure-blue-s), var(--pure-blue-l), 0.1));
    }
  }
}
</style>
