<template>
  <div id="processes-panel">

    <AppTip id="steps-placeholder" v-if="$store.getters['lien.activeSteps'].length === 0">
      Start by selecting a&nbsp;process.
    </AppTip>

    <ActionSelect
      v-model="$store.state.lien.selectedProcessKey"
      :options="$store.getters['lien.activeProcesses']"
      valueAlias="Value"
      keyAlias="Key">
      Process
    </ActionSelect>

    <div class="v-space"></div>

    <StepList
      :security-access="securityAccess"
      :selected-record-keys="selectedRecordKeys"
      @on-step-select="$emit('on-select-step')"
      @on-step-complete="$emit('on-step-complete')"
      @on-step-hold="$emit('on-step-hold')"
      @on-process-task-fail="$emit('on-process-task-fail', $event)" />

  </div>
</template>

<script>
import StepList from './steps/StepList.vue';
import ActionSelect from './inputs/Select.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';

export default {
  name: 'processes-panel',

  extends: RecordsView,

  components: {
    StepList,
    ActionSelect
  },

  props: {
    securityAccess: {
      type: Object,
      required: true,
      default: () => {}
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  methods: {
    fetchProcesses () {
      return new Promise((resolve, reject) => {
        this.$store.dispatch('LIEN__getProcesses', {
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async getProcesses () {
      this.$store.state.lien.processes = await this.fetchProcesses();

      if (this.$store.getters['lien.activeProcesses'].length) {
        this.startProcessKey = this.$store.getters['lien.activeProcesses'][0].Key;
      }
    }
  },

  async mounted () {
    this.getProcesses();
  }
};
</script>

<style scoped>
#processes-panel {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  padding: 1rem;
  border-right: 1px solid var(--body-border);
  overflow-y: scroll;
}
</style>
