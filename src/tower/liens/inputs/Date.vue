<template>
  <label :data-required="required">
    <div><slot></slot></div>
    <input
      type="datetime-local"
      v-model="$value"
      @change="$emit('change', $event)"
      :required="required"
      :disabled="disabled">
  </label>
</template>

<script>
export default {
  name: 'date-input',

  props: {
    value: {},
    required: {
      type: Boolean,
      required: false,
      default: false
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  computed: {
    $value: {
      get () {
        if (!this.value) return '';
        
        // Convert "MM/DD/YYYY HH:mm:ss" format to datetime-local format (YYYY-MM-DDTHH:mm:ss)
        try {
          const date = new Date(this.value);
          if (isNaN(date.getTime())) return '';
          
          // Format to YYYY-MM-DDTHH:mm:ss for datetime-local input using local time
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          
          return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
        } catch (e) {
          return '';
        }
      },
      set (value) {
        if (!value) {
          this.$emit('input', '');
          return;
        }
        
        // Convert datetime-local format (YYYY-MM-DDTHH:mm:ss) back to MM/DD/YYYY HH:mm:ss
        try {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            this.$emit('input', '');
            return;
          }
          
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const year = date.getFullYear();
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          
          this.$emit('input', `${month}/${day}/${year} ${hours}:${minutes}:${seconds}`);
        } catch (e) {
          this.$emit('input', '');
        }
      }
    }
  }
};
</script>

<style scoped>
label {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;

  padding: 0.25rem 0.5rem;
  width: 100%;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.5rem;

  &[data-required] {
    background-color: color-mix(in oklch, var(--pure-yellow) 10%, transparent);
  }
}

div {
  white-space: nowrap;
}

input {
  font-size: 1rem;
  color: var(--pure-blue) !important;
  background: transparent !important;
  border: 0;
}
</style>
