<template>
  <details :open="open">
    <summary @click="onClick">
      <div class="_layout">
        <slot name="summary"></slot>
      </div>
    </summary>

    <div class="details-content">
      <slot></slot>
    </div>
  </details>
</template>

<script>
export default {
  props: {
    open: {
      type: Boolean,
      default: false
    },
    observeExternalOpenState: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    onClick (event) {
      if (this.observeExternalOpenState) {
        event.preventDefault();
        this.$emit('click');
      }
    }
  }
};
</script>

<style scoped>
* {
  box-sizing: border-box;
}

details {
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: 0.2rem;
  background-color: color-mix(in oklch, var(--blue), transparent 90%);
  border-radius: 0.7rem;
  cursor: default;
  overflow: hidden;

  &[open] {
    summary {
      color: var(--pure-blue);
    }

    .details-content {
      max-height: 100dvh;
      opacity: 1;
    }
  }

  summary {
    display: block;
    cursor: pointer;

    &::-webkit-details-marker,
    &::marker {
      display: none;
      content: "";
    }
  }
}

summary {
  ._layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  padding: 0.25rem 0.5rem;
  color: var(--body-fg);
  background-color: white;
  border-radius: 0.5rem;
}

.details-content {
  container: details-content / inline-size;

  display: grid;
  grid-template-rows: auto;
  gap: 0.5rem;

  padding: 0.5rem;
  margin-top: 0.5rem;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s ease-out, opacity 0.4s ease-out;

  > * {
    width: 100%;
  }
}
</style>
