<template>
  <label :data-required="required">
    <div><slot></slot></div>
    <select
      v-model="$value"
      @change="$emit('change', $event)"
      :required="required"
      :disabled="disabled">

      <option
        v-for="(option, index) in options"
        :value="option[keyAlias]"
        :key="index">
        {{ option[valueAlias] }}
      </option>

    </select>
    <div class="select-indicator"></div>
  </label>
</template>

<script>
export default {
  name: 'select-input',

  props: {
    value: {},
    options: { type: Array, required: true },
    keyAlias: { type: [String, Number], required: false, default: 'value' },
    valueAlias: { type: [String, Number], required: false, default: 'description' },
    required: {
      type: Boolean,
      required: false,
      default: false
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  }
};
</script>

<style scoped>
label {
  position: relative;

  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0.5rem;

  padding: 0.25rem 0.5rem;
  width: 100%;
  border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1);
  border-radius: 0.5rem;

  &[data-required] {
    background-color: color-mix(in oklch, var(--pure-yellow) 10%, transparent);
  }
}

div {
  white-space: nowrap;
}

select {
  font-size: 1rem;
  color: var(--pure-blue) !important;
  background: transparent !important;
  border: 0;
  appearance: none;
}

.select-indicator {
  position: absolute;
  inset-inline-end: 0.5rem;
  inset-block-start: 50%;

  inline-size: 0.5rem;
  block-size: 0.5rem;
  border-inline-end: 1px solid var(--pure-blue);
  border-block-end: 1px solid var(--pure-blue);
  transform: translateY(-60%) rotate(45deg);
  pointer-events: none;
}
</style>
