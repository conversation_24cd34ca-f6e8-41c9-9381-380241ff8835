<template>
  <ActionButton @click="onClick">
    Hold step&hellip; {{ hoursToHold }}
  </ActionButton>
</template>

<script>
import ActionButton from '../../inputs/Button.vue';

export default {
  name: 'step-hold',

  components: {
    ActionButton
  },

  props: {
    step: {
      type: Object,
      required: false,
      default: () => ({})
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  data () {
    return {
      hoursToHold: null
    };
  },

  methods: {
    async onClick () {
      this.$prompt('How many hours would you like to hold this step?', 'Hold', {
        confirmButtonText: 'Hold step',
        cancelButtonText: 'Cancel'
      }).then((input) => {
        this.hoursToHold = Number(input.value);
        this.holdStep();
      }).catch(() => {
        // Nothing
      });
    },

    holdStep () {
      if (!this.selectedRecordKeys.length) return;
      if (!this.hoursToHold) return;

      this.$store.dispatch('LIENBATCH__holdStep', {
        stepKey: this.step.lLienStepKey,
        callKeys: this.selectedRecordKeys,
        hours: this.hoursToHold,
        callback: () => {
          this.$emit('on-step-hold');
        }
      });
    }
  }
};
</script>
