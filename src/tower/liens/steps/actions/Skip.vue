<template>
  <ActionButton @click="onClick">
    Skip step&hellip;
  </ActionButton>
</template>

<script>
import ActionButton from '../../inputs/Button.vue';

export default {
  name: 'step-skip',

  components: {
    ActionButton
  },

  props: {
    step: {
      type: Object,
      required: false,
      default: () => ({})
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  methods: {
    async onClick () {
      this.$confirm(`Skipping is fun, but are you sure it's appropriate here?`, 'Skip', {
        confirmButtonText: 'Skip step',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        this.skipStep();
      }).catch(() => {
        // Nothing
      });
    },

    skipStep () {
      if (!this.selectedRecordKeys.length) return;

      this.$store.dispatch('LIENBATCH__skipStep', {
        stepKey: this.step.lLienStepKey,
        callKeys: this.selectedR<PERSON>ord<PERSON><PERSON><PERSON>,
        callback: () => {
          this.$emit('on-step-hold');
        }
      });
    }
  }
};
</script>
