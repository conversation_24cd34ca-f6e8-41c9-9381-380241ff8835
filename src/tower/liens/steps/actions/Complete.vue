<template>
  <ActionButton @click="onClick">
    Complete step&hellip;
  </ActionButton>
</template>

<script>
import ActionButton from '../../inputs/Button.vue';

export default {
  name: 'step-complete',

  components: {
    ActionButton
  },

  props: {
    step: {
      type: Object,
      required: false,
      default: () => ({})
    },
    selectedRecordKeys: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  methods: {
    async onClick() {
      const question = /[.?!]$/.test(this.step.vc255CompletionQuestion.trim()) ?
        this.step.vc255CompletionQuestion :
        this.step.vc255CompletionQuestion + '.';

      const confirmText = question +
        ' This will affect ' +
        this.selectedRecordKeys.length +
        ' ' +
        (this.selectedRecordKeys.length === 1 ? 'call' : 'calls') +
        '.';

      this.$confirm(confirmText, 'Confirm', {
        confirmButtonText: 'Complete step',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        this.completeStep();
      }).catch(() => {
        // Nothing
      });
    },

    completeStep () {
      if (!this.selectedRecordKeys.length) return;

      this.$store.dispatch('LIENBATCH__completeStep', {
        stepKey: this.step.lLienStepKey,
        callKeys: this.selectedRecordKeys,
        callback: () => {
          this.$emit('on-step-complete');
        }
      });
    }
  }
};
</script>
