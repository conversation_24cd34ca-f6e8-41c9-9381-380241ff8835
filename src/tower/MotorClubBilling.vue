<template>
  <div id="motor-club-billing-view">
    <section id="tabs">
      <tab-group v-model="$store.state.motorClubBilling.selectedTab">
        <tab-item value="calls">Calls</tab-item>
        <tab-item value="invoices">Invoices</tab-item>
      </tab-group>
    </section>

    <section id="tools">
      <template v-if="$store.state.motorClubBilling.selectedTab === 'calls'">
        <app-tip>
          Select from the list of calls that are ready to be invoiced.
        </app-tip>

        <div class="v-space"></div>

        <WizardButton flavor="primary" :icon="false" @click="invoiceCalls" :disabled="!anyRecordsAreSelected || isInvoicingCalls">
          Invoice selected calls
        </WizardButton>
      </template>

      <template v-if="$store.state.motorClubBilling.selectedTab === 'invoices'">
        <div class="_label is-upper is-small">Filter</div>

        <div class="v-space"></div>

        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === ''">
          <div>All</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="">
        </label>
        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === 'paid'">
          <div>Paid</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="paid">
        </label>
        <label class="_radio-control" :data-active="$store.state.motorClubBilling.invoiceFilter.pay === 'unpaid'">
          <div>Unpaid</div>
          <input type="radio" v-model="$store.state.motorClubBilling.invoiceFilter.pay" name="filter_invoices" value="unpaid">
        </label>

        <div class="v-space"></div>

        <span class="select">
          <select v-model="$store.state.motorClubBilling.invoiceFilter.customer">
            <option value="">Customer...</option>
            <option v-for="customer in $store.state.motorClubBilling.customers" :value="customer.Value" :key="customer.Key">
              {{ customer.Value }}
            </option>
          </select>
        </span>
      </template>
    </section>

    <section id="data-sheet">
      <grid
        v-if="$store.state.motorClubBilling.selectedTab === 'calls'"
        title=""
        key="grid"
        class="grid"
        :grid="callsSettings"
        :data="callsProxy"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :multiselect="true"
        :show-loader="showLoader"
        :invoice-preview-control="true"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @viewInvoice="viewInvoice"
        @save="save">
        <template slot="context-tools"></template>
      </grid>

      <grid
        v-if="$store.state.motorClubBilling.selectedTab === 'invoices'"
        title=""
        key="grid"
        class="grid"
        :grid="invoicesSettings"
        :data="invoicesProxy"
        :refreshedAt="refreshedAt"
        :config="viewConfig"
        :multiselect="false"
        :show-loader="showLoader"
        :invoice-review-control="true"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @viewInvoice="viewInvoice"
        @save="save">
        <template slot="context-tools"></template>
      </grid>
    </section>

    <invoice-preview
      :callKey="invoicePreview.callKey"
      :invoiceKey="invoicePreview.key"
      v-if="invoicePreview.isModalVisible"
      @close="invoicePreview.isModalVisible = false">
    </invoice-preview>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>import { get, find, filter, reject, castArray } from 'lodash-es';


import { mapActions } from 'vuex';
import { GRID_KEY } from '@/config';
import Access from '@/utils/access';
import Grid from '@/components/features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import InvoicePreview from '@/components/features/InvoicePreview.vue';
import WizardButton from '@/components/inputs/WizardButton.vue';

  export default {
    name: 'motor-club-billing-view',

    extends: RecordsView,

    components: {
      Grid,
      InvoicePreview,
      WizardButton
    },

    data () {
      return {
        viewConfig: {
          uuid: 'motor-club-billing-screen',
          noun: 'MCBilling',
          readRouteName: 'CallReel',
          recordKeyName: 'lCallKey'
        },

        isInvoicingCalls: false,
        invoicePreview: {
          key: null,
          callKey: null,
          isModalVisible: false
        },

        gridKey: {
          calls: GRID_KEY.motorClubCallsToInvoice,
          invoices: GRID_KEY.motorClubInvoices
        }
      };
    },

    computed: {
      canEditThings () {
        return Access.has('motorClubBilling.read');
      },

      callsProxy () {
        return get(this.viewData, `Grids[${this.gridKey.calls}]`, []);
      },

      callsSettings () {
        let settings = find(this.RECORDS__settings.Grids, ['Key', this.gridKey.calls]);

        return settings || {};
      },

      invoicesProxy () {
        let invoices = get(this.viewData, `Grids[${this.gridKey.invoices}]`, []);

        if (this.$store.state.motorClubBilling.invoiceFilter.pay === 'paid') {
          invoices = filter(invoices, ['tcBalance', '0.00']);
        }

        if (this.$store.state.motorClubBilling.invoiceFilter.pay === 'unpaid') {
          invoices = reject(invoices, ['tcBalance', '0.00']);
        }

        if (this.$store.state.motorClubBilling.invoiceFilter.customer) {
          invoices = filter(invoices, ['sCustomer', this.$store.state.motorClubBilling.invoiceFilter.customer]);
        }

        return invoices;
      },

      invoicesSettings () {
        let settings = find(this.RECORDS__settings.Grids, ['Key', this.gridKey.invoices]);

        return settings || {};
      }
    },

    methods: {
      ...mapActions([
        'MCBILLING__sendInvoice',
        'MCBILLING__getCustomers'
      ]),

      async invoiceCalls () {
        let promises = [];

        this.isInvoicingCalls = true;

        this.__selectedRecords.forEach(record => {
          promises.push(new Promise((resolve, reject) => {
            this.MCBILLING__sendInvoice({
              key: record.lCallKey,
              success: response => resolve(response),
              fail: response => reject(response)
            });
          }));
        });

        const responses = await Promise.all(promises.map(promise => promise.catch(response => response)));

        this.isInvoicingCalls = false;

        responses.forEach(response => {
          if ('CallKey' in response) {
            let targetCall = find(this.callsProxy, [this.viewConfig.recordKeyName, response.CallKey.toString()]);

            if (targetCall) {
              targetCall.isRequested = true;
            }
          } else {
            let targetCall = find(this.callsProxy, [this.viewConfig.recordKeyName, response.Context.toString()]);

            if (targetCall) {
              targetCall.Errors = castArray({ Message: response.Message });
            }
          }
        });

        this.__selectRecords([]);
      },

      viewInvoice (record) {
        this.invoicePreview.callKey = get(record, 'lCallKey', null);
        this.invoicePreview.key = get(record, 'lKey', null);

        this.invoicePreview.isModalVisible = !!this.invoicePreview.callKey || !!this.invoicePreview.key;
      }
    },

    mounted () {
      this.$store.state.addCall.shouldStayOnSave = false;

      this.MCBILLING__getCustomers({
        success: response => {
          this.$store.state.motorClubBilling.customers = response;
        }
      });
    }
  };
</script>

<style scoped>
#motor-club-billing-view {
  display: grid;

  grid-template-columns: 300px 1fr;
  grid-template-rows: var(--titlebar-height) 1fr;
  grid-template-areas:
    "tabs data-sheet"
    "tools data-sheet";

  height: 100%;

  #tabs {
    grid-area: tabs;

    display: grid;
    place-content: center;

    background: var(--body-border);
  }

  #tools {
    grid-area: tools;

    padding: 1rem;
    background: var(--body-border);

    ._label {
      font-weight: bold;
      opacity: 0.8;
      mix-blend-mode: color-burn;
    }

    ._radio-control {
      display: flex;
      justify-content: space-between;
      align-items: center;

      padding: 0.5rem 1rem;
      border-radius: 0.25rem;

      &[data-active] {
        color: var(--body-fg);
        background-color: white;
        box-shadow: var(--box-shadow-50);
      }

      input {
        width: auto;
      }
    }
  }

  #data-sheet {
    grid-area: data-sheet;
  }
}
</style>
