<template>
<div id="record-view" data-page="customer" :data-search-mode="searchMode">
  <app-titlebar title="Customer" v-if="!searchMode"></app-titlebar>

  <div class="content-section">
    <app-grid-form>
      <section class="_section-group">
        <form-section title="Customer" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-6">
              <app-text id="customer.vc30Name" v-model="record.vc30Name" @change="propertyChanged('vc30Name')" @focus="shouldLookupDuplicate = true" maxlength="30" :disabled="!canEditEssentials" :required="!searchMode">
                Customer Name
              </app-text>
            </div>
            <div class="column is-6">
              <app-text id="customer.ch6ShortCode" v-model="record.ch6ShortCode" v-upper="record.ch6ShortCode" @change="propertyChanged('ch6ShortCode')" @focus="shouldLookupDuplicate = true" maxlength="6" :disabled="!canEditEssentials" :required="!searchMode">
                Short Code
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-shortcode v-model="record.lCustomerTypeKey" :options="filteredCustomerTypes" @change="propertyChanged('lCustomerTypeKey')" :disabled="!canEditEssentials" :required="!searchMode" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Type
              </app-shortcode>
            </div>
            <div class="column is-6 is-bottom">
              <app-shortcode id="customer.lCustomerStatusTypeKey" v-model="record.lCustomerStatusTypeKey" :options="statuses" @change="propertyChanged('lCustomerStatusTypeKey')" :disabled="!canEditEssentials" :required="!searchMode" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Status
              </app-shortcode>
            </div>
          </div>
        </form-section>

        <form-section title="Accounting" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-6" v-if="canViewTaxAndDiscount">
              <app-checkbox id="customer.bTaxExempt" v-model="record.bTaxExempt" @change="propertyChanged('bTaxExempt')" :disabled="!canEdit">
                Tax Exempt
              </app-checkbox>
            </div>
            <div class="column is-6" v-if="canViewTaxAndDiscount">
              <app-text id="customer.vc20TaxCertNum" v-model="record.vc20TaxCertNum" maxlength="20" :disabled="!canEdit">
                Tax Certification Number
              </app-text>
            </div>
            <div class="column is-6 is-left" v-if="canViewTaxAndDiscount">
              <app-number id="customer.fDiscountPct" v-model="record.fDiscountPct" :disabled="!canEdit" :required="!searchMode">
                Discount
              </app-number>
            </div>
            <div class="column is-6" v-if="canViewTaxAndDiscount">
              <app-shortcode id="customer.lTermsTypeKey" v-model="record.lTermsTypeKey" :options="terms" :disabled="!canEditTerms" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Default Terms
              </app-shortcode>
            </div>
            <div class="column is-3 is-left">
              <app-number id="customer.pCreditLimit" v-model="record.pCreditLimit" min="0" :disabled="!canEditCreditLimit">
                Credit Limit (Dollars)
              </app-number>
            </div>
            <div class="column is-3">
              <app-checkbox id="customer.bPaymentNecessary" v-model="record.bPaymentNecessary" :disabled="!canEdit">
                Payment Necessary
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox id="customer.bPrintInvoices" v-model="record.bPrintInvoices" :disabled="!canEdit">
                Print Invoices
              </app-checkbox>
            </div>
            <div class="column is-3">
              <app-checkbox id="customer.bPrintStatements" v-model="record.bPrintStatements" :disabled="!canEdit">
                Print Statements
              </app-checkbox>
            </div>
            <div class="column is-12 is-left">
              <app-number id="customer.lFreeStorageMinutesOverride" v-model="record.lFreeStorageMinutesOverride" min="0" :disabled="!canEdit">
                Free Storage Minutes (Overrides Etc. Screen)
              </app-number>
            </div>
            <div class="column is-6 is-left">
              <app-shortcode id="customer.lMotorClubKey" v-model="record.lMotorClubKey" :options="motorclubs" :disabled="!canEdit || !isMotorClubCustomer" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Motor Club
              </app-shortcode>
            </div>
            <div class="column is-6">
              <app-text id="customer.vc30AccountID" v-model="record.vc30AccountID" maxlength="30" :disabled="!canEdit">
                Account Number
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-shortcode id="customer.lSalespersonKey" v-model="record.lSalespersonKey" :options="salespersons" :disabled="!canEdit" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Salesperson
              </app-shortcode>
            </div>
            <div class="column is-6 is-bottom">
              <app-text v-model="createdByPair" :disabled="true">
                Created By
              </app-text>
            </div>
          </div>
        </form-section>

        <form-section title="Physical Location" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-12">
              <app-group>
                <app-text v-model="record.vc30Address1_Physical" id="customer.vc30Address1_Physical" maxlength="30" :disabled="!canEdit">
                  Address 1
                </app-text>
                <app-button @click="copyFromBilling" tabindex="-1" :disabled="!canEdit" style="width: auto">Copy From Billing</app-button>
              </app-group>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.vc30Address2_Physical" id="customer.vc30Address2_Physical" maxlength="30" :disabled="!canEdit">
                Address 2
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc30City_Physical" id="customer.vc30City_Physical" maxlength="30" :disabled="!canEdit">
                City
              </app-text>
            </div>
            <div class="column is-3">
              <app-select-state v-model="record.ch2StateKey_Physical" id="customer.ch2StateKey_Physical" :disabled="!canEdit">
                State
              </app-select-state>
            </div>
            <div class="column is-3">
              <app-text v-model="record.vc10Zip_Physical" id="customer.vc10Zip_Physical" maxlength="10" :disabled="!canEdit">
                Zip
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc30ContactName_Physical" id="customer.vc30ContactName_Physical" maxlength="30" :disabled="!canEdit">
                Contact Name
              </app-text>
            </div>
            <div class="column is-6">
              <app-text v-model="record.vc50Email_Physical" id="customer.vc50Email_Physical" :disabled="!canEdit">
                Email
              </app-text>
            </div>
            <div class="column is-4 is-left is-bottom">
              <app-phone v-model="record.vc20Phone1_Physical" id="customer.vc20Phone1_Physical" maxlength="20" :disabled="!canEdit">
                Phone 1
              </app-phone>
            </div>
            <div class="column is-4 is-bottom">
              <app-phone v-model="record.vc20Phone2_Physical" id="customer.vc20Phone2_Physical" maxlength="20" :disabled="!canEdit">
                Phone 2
              </app-phone>
            </div>
            <div class="column is-4 is-bottom">
              <app-phone v-model="record.vc20Fax_Physical" id="customer.vc20Fax_Physical" maxlength="20" :disabled="!canEdit">
                Fax
              </app-phone>
            </div>
          </div>
        </form-section>

        <form-section title="Billing Location" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-12">
              <app-group>
                <app-text v-model="record.vc30Attention_Billing" id="customer.vc30Attention_Billing" maxlength="30" :disabled="!canEdit">
                  Attention
                </app-text>
                <app-button @click="copyFromPhysical" tabindex="-1" :disabled="!canEdit" style="width: auto">
                  Copy From Physical
                </app-button>
              </app-group>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.vc30Address1_Billing" id="customer.vc30Address1_Billing" maxlength="30" :disabled="!canEdit" :required="billingLocationIsRequired">
                Address 1
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.vc30Address2_Billing" id="customer.vc30Address2_Billing" maxlength="30" :disabled="!canEdit">
                Address 2
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc30City_Billing" id="customer.vc30City_Billing" maxlength="30" :disabled="!canEdit" :required="billingLocationIsRequired">
                City
              </app-text>
            </div>
            <div class="column is-3">
              <app-select-state v-model="record.ch2StateKey_Billing" id="customer.ch2StateKey_Billing" :disabled="!canEdit" :required="billingLocationIsRequired">
                State
              </app-select-state>
            </div>
            <div class="column is-3">
              <app-text v-model="record.vc10Zip_Billing" id="customer.vc10Zip_Billing" maxlength="10" :disabled="!canEdit" :required="billingLocationIsRequired">
                Zip
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc30ContactName_Billing" id="customer.vc30ContactName_Billing" maxlength="30" :disabled="!canEdit">
                Contact Name
              </app-text>
            </div>
            <div class="column is-6">
              <app-text v-model="record.vc50Email_Billing" id="customer.vc50Email_Billing" :disabled="!canEdit">
                Email
              </app-text>
            </div>
            <div class="column is-4 is-left is-bottom">
              <app-phone v-model="record.vc20Phone1_Billing" id="customer.vc20Phone1_Billing" :maxlength="20" :disabled="!canEdit">
                Phone 1
              </app-phone>
            </div>
            <div class="column is-4 is-bottom">
              <app-phone v-model="record.vc20Phone2_Billing" id="customer.vc20Phone2_Billing" :maxlength="20" :disabled="!canEdit">
                Phone 2
              </app-phone>
            </div>
            <div class="column is-4 is-bottom">
              <app-phone v-model="record.vc20Fax_Billing" id="customer.vc20Fax_Billing" :maxlength="20" :disabled="!canEdit">
                Fax
              </app-phone>
            </div>
          </div>
        </form-section>

        <form-section title="PPI" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-6">
              <app-shortcode id="customer.lPPIBillingCustomerKey" v-model="record.lPPIBillingCustomerKey" :options="customers" :disabled="!canEdit" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Billing Customer
              </app-shortcode>
            </div>
            <div class="column is-6">
              <app-text v-model="record.vc50ParentName" id="customer.vc50ParentName" maxlength="50" :disabled="!canEdit">
                Parent Name
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="record.dEffective" id="customer.dEffective" :disabled="!canEdit" :formatter="!searchMode">
                Effective
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-date-time v-model="record.dRenewal" id="customer.dRenewal" :disabled="!canEdit" :formatter="!searchMode">
                Renewal
              </app-date-time>
            </div>
            <div class="authorities-section column is-12">
              <ul class="_list" >
                <li class="_item is-uppercase is-bold is-quiet is-small" data-shape="title">
                  <div>Authorized Callers</div>
                </li>
                <li class="_item" data-shape="header">
                  <div>Name</div>
                  <div>Phone</div>
                  <button class="button-x" @click="addAuthority" tabindex="-1" :disabled="!canEditPPIAuthority">
                    <i class="far fa-plus"></i>
                  </button>
                </li>
                <li class="_item" v-for="(authority, index) in record.PPIAuthorities" :key="index">
                  <input type="text" v-model="authority.vc50Name" maxlength="50" :disabled="!canEditPPIAuthority" />
                  <input type="text" v-model="authority.vc20Phone" :maxlength="20" :disabled="!canEditPPIAuthority" />
                  <button class="button-x" @click="removeAuthority(index)" tabindex="-1" :disabled="!canEditPPIAuthority">
                    <i class="far fa-xmark"></i>
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </form-section>
      </section>

      <section class="_section-group">
        <form-section title="New Call Defaults" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-6">
              <app-text v-model="record.vc30CallerName_NewCallDefault" id="customer.vc30CallerName_NewCallDefault" maxlength="30" :disabled="!canEdit">
                Caller Name
              </app-text>
            </div>
            <div class="column is-6">
              <app-phone v-model="record.vc20PhoneNum_NewCallDefault" id="customer.vc20PhoneNum_NewCallDefault" :maxlength="20" :disabled="!canEdit">
                Phone Number
              </app-phone>
            </div>
            <div class="column is-8 is-left">
              <label>Location</label>
              <div class="field has-addons">
                <p class="control">
                  <input v-model="record.vc100Location_NewCallDefault" type="text" class="input" id="customer.vc100Location_NewCallDefault" maxlength="100" :disabled="!canEdit"/>
                </p>
                <p class="control width-auto" v-if="record.vc100Location_NewCallDefault">
                  <button class="button" @click="geocodeLocation" tabindex="-1" :disabled="!canEdit"><i class="fal fa-location"></i></button>
                </p>
              </div>
            </div>
            <div class="column is-2">
              <app-text v-model="record.gcLocationLatitude_NewCallDefault" id="customer.gcLocationLatitude_NewCallDefault" :disabled="!canEdit">
                Latitude
              </app-text>
            </div>
            <div class="column is-2">
              <app-text v-model="record.gcLocationLongitude_NewCallDefault" id="customer.gcLocationLongitude_NewCallDefault" :disabled="!canEdit">
                Longitude
              </app-text>
            </div>
            <div class="column is-8 is-left">
              <label>Destination</label>
              <div class="field has-addons">
                <p class="control width-auto">
                  <app-shortcode :showLabel="false" :options="lots" v-model="destinationLot" placeholder="Lots..." keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext" @change="formatDestinationLot" />
                </p>
                <p class="control">
                  <input v-model="record.vc100Destination_NewCallDefault" type="text" class="input" id="customer.vc100Destination_NewCallDefault" maxlength="100" placeholder="Address" :disabled="!canEdit"/>
                </p>
                <p class="control width-auto" v-if="record.vc100Destination_NewCallDefault">
                  <button class="button" @click="geocodeDestination" tabindex="-1" :disabled="!canEdit"><i class="fal fa-location"></i></button>
                </p>
              </div>
            </div>
            <div class="column is-2">
              <app-text v-model="record.gcDestinationLatitude_NewCallDefault" id="customer.gcDestinationLatitude_NewCallDefault" :disabled="!canEdit">
                Latitude
              </app-text>
            </div>
            <div class="column is-2">
              <app-text v-model="record.gcDestinationLongitude_NewCallDefault" id="customer.gcDestinationLongitude_NewCallDefault" :disabled="!canEdit">
                Longitude
              </app-text>
            </div>
            <div class="column is-8 is-left">
              <InputTowType v-model="record.lTowTypeKey_NewCallDefault" :context="searchContext" :disabled="!canEdit" />
            </div>
            <div class="column is-4">
              <app-number v-model="record.tPriority_NewCallDefault" id="customer.tPriority_NewCallDefault" min="1" max="9" :disabled="!canEdit">
                Priority
              </app-number>
            </div>
            <div class="column is-8 is-left">
              <app-shortcode id="customer.lReasonTypeKey_NewCallDefault" v-model="record.lReasonTypeKey_NewCallDefault" :options="reasons" :disabled="!canEdit" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Reason
              </app-shortcode>
            </div>
            <div class="column is-4">
              <app-number v-model="record.lETA_NewCallDefault" id="customer.lETA_NewCallDefault" :disabled="!canEdit">
                ETA
              </app-number>
            </div>
            <div class="column is-8 is-left">
              <app-shortcode id="customer.lTruckTypeKey_NewCallDefault" v-model="record.lTruckTypeKey_NewCallDefault" :options="truckTypes" :disabled="!canEdit" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :context="searchContext">
                Truck Type Required
              </app-shortcode>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bNoCharge" id="customer.bNoCharge" :disabled="!canEdit">
                No Charge
              </app-checkbox>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.vc50EquipmentRequired_NewCallDefault" id="customer.vc50EquipmentRequired_NewCallDefault" maxlength="50" :disabled="!canEdit">
                Equipment Required
              </app-text>
            </div>
            <div class="column is-4 is-left">
              <app-checkbox v-model="record.bPortalToPortal_NewCallDefault" id="customer.bPortalToPortal_NewCallDefault" :disabled="!canEdit">
                Portal to Portal
              </app-checkbox>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bVINRequired" id="customer.bVINRequired" :disabled="!canEdit">
                VIN Required
              </app-checkbox>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bRORequired" id="customer.bRORequired" :disabled="!canEdit">
                R.O. Required
              </app-checkbox>
            </div>
            <div class="column is-4 is-left">
              <app-checkbox v-model="record.bMileageRequired" id="customer.bMileageRequired" :disabled="!canEdit">
                Mileage Required
              </app-checkbox>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bPORequired" id="customer.bPORequired" :disabled="!canEdit">
                P.O. Required
              </app-checkbox>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bOdometerRequired" id="customer.bOdometerRequired" :disabled="!canEdit">
                Odometer Required
              </app-checkbox>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.tcDefaultCommAmt" id="customer.tcDefaultCommAmt" :disabled="!canEdit">
                Commissionable Amount
              </app-text>
            </div>
            <div class="column is-6">
              <app-checkbox v-model="tcDefaultSecondCommAmtProxy" id="customer.tcDefaultSecondCommAmtProxy" :disabled="!canEdit"  >
                Apply 2nd Commission
              </app-checkbox>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.vc100UserDefined1_NewCallDefault" id="customer.vc100UserDefined1_NewCallDefault" maxlength="100" :disabled="!canEdit">
                {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef1 }}
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc50UserDefined2_NewCallDefault" id="customer.vc50UserDefined2_NewCallDefault" maxlength="50" :disabled="!canEdit">
                {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef2 }}
              </app-text>
            </div>
            <div class="column is-6">
              <app-text v-model="record.vc50UserDefined3_NewCallDefault" id="customer.vc50UserDefined3_NewCallDefault" maxlength="50" :disabled="!canEdit">
                {{ TOPSCOMPANY__settings.vc15Label_Call_UserDef3 }}
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="record.vc255Notes_CallTaker" id="customer.vc255Notes_CallTaker" maxlength="255" :disabled="!canEdit">
                Call Taker Notes
              </app-textarea>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="record.vc255Notes_Driver" id="customer.vc255Notes_Driver" maxlength="255" :disabled="!canEdit">
                Driver Notes
              </app-textarea>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="record.vc255Notes_Accounting" id="customer.vc255Notes_Accounting" maxlength="255" :disabled="!canEdit">
                Accounting Notes
              </app-textarea>
            </div>
            <div class="column is-12 is-left is-bottom">
              <app-textarea v-model="record.vc255Notes_Dispatch" id="customer.vc255Notes_Dispatch" maxlength="1000" :disabled="!canEdit">
                Dispatch Notes
              </app-textarea>
            </div>
          </div>
        </form-section>

        <form-section title="Miscellaneous" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-4">
              <app-text v-model="record.vc20DealerNum" id="customer.vc20DealerNum" maxlength="20" :disabled="!canEdit">
                Dealer Number
              </app-text>
            </div>
            <div class="column is-4">
              <app-date-time v-model="record.dDealerExpiry" id="customer.dDealerExpiry" :disabled="!canEdit" :formatter="!searchMode">
                Expires
              </app-date-time>
            </div>
            <div class="column is-4">
              <app-checkbox v-model="record.bAutoCalcMileages" :disabled="!canEdit">
                Auto Calculate Mileages
              </app-checkbox>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.vc50UserDefined1" id="customer.vc50UserDefined1" maxlength="50" :disabled="!canEdit">
                {{ TOPSCOMPANY__settings.vc15Label_Customer_UserDef1 }}
              </app-text>
            </div>
            <div class="column is-6">
              <app-text v-model="record.vc50UserDefined2" id="customer.vc50UserDefined2" maxlength="50" :disabled="!canEdit">
                {{ TOPSCOMPANY__settings.vc15Label_Customer_UserDef2 }}
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="record.vc255Notes" id="customer.vc255Notes" maxlength="255" :disabled="!canEdit">
                Notes
              </app-textarea>
            </div>
            <div class="column is-12 is-left is-bottom">
              <app-textarea v-model="record.sAdditionalNotes" id="customer.sAdditionalNotes" maxlength="1000" :disabled="!canEdit">
                Additional Notes
              </app-textarea>
            </div>
          </div>
        </form-section>

        <app-modified-metadata
          v-if="!isNewRecord"
          class="modified-metadata"
          :record="record"
          :config="viewConfig"
          modifiedAtAlias="dDateLastModified"
          modifiedByAlias="lUserKey">
        </app-modified-metadata>
      </section>
    </app-grid-form>
  </div>

  <app-modal class="risky-changes-modal" :show="riskyChangesVisible" title="Review Changes" @close="riskyChangesVisible = false">
    <div class="risky-changes-modal__content columns">
      <div class="column has-icon has-text-centered">
        <i class="fal fa-exclamation-triangle is-warning"></i>
      </div>
      <div class="column has-message is-two-thirds">
        <p>The following changes may have unexpected effects:</p>
        <ul>
          <li v-for="(change, index) in riskyChanges" :key="index">{{ change }}</li>
        </ul>
      </div>
    </div>

    <app-footerbar>
      <app-button @click="riskyChangesVisible = false" type="default">Cancel</app-button>
      <app-button @click="save" type="primary">Save Anyway</app-button>
    </app-footerbar>
  </app-modal>

  <app-footerbar v-if="!searchMode">
    <template slot="left">
      <app-button type="primary" @click="saveProxy" :disabled="!canEdit">Save</app-button>
      <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canDuplicate">Duplicate</app-button>
      <app-button type="default" @click="cancel">Close</app-button>
      <app-button v-if="isDeletable" type="danger" @click="deleteRecord" :disabled="!canDelete">Delete</app-button>
      <app-button v-if="isUndeletable" type="default" @click="undeleteRecord" :disabled="!canDelete">Undelete</app-button>
    </template>

    <app-button v-if="!isNewRecord" @click="viewQuotes">
      View open quotes&hellip;
    </app-button>
    <record-reel
      v-if="!isNewRecord"
      :record-key="key"
      :key-alias="viewConfig.recordKeyName"
      @jump="jumpTo">
    </record-reel>
  </app-footerbar>
</div> <!-- /customer-view -->
</template>

<script>

import { filter, isEmpty, toString, includes, forEach, find, get, uniq, debounce } from 'lodash-es';
import is from 'is_js';
import Access from '@/utils/access.js';

import { fullDateTime } from '@/utils/filters.js';
import { mapGetters, mapActions } from 'vuex';
import RecordReel from '@/components/features/RecordReel.vue';
import RecordView from '@/components/ancestors/RecordView.vue';

import {
  VALUE_ID,
  EVENT_WARNING,
  EVENT_INFO
} from '@/config.js';

export default {
  name: 'customer-view',

  extends: RecordView,

  components: { RecordReel },

  data () {
    return {
      lots: [],
      terms: [],
      reasons: [],
      statuses: [],
      customers: [],
      motorclubs: [],
      truckTypes: [],
      salespersons: [],
      customerTypes: [],
      destinationLot: '',
      changedProperties: [],
      customerDuplicate: {},
      watchForUserChanges: false,
      riskyChangesVisible: false,
      shouldLookupDuplicate: false,
      billingCustomerCallCount: null,

      viewConfig: {
        noun: 'Customer',
        recordKeyName: 'lCustomerKey',
        returnRouteName: 'Customers',
        readRouteName: 'Customer',
        addRouteName: 'AddCustomer'
      },

      record: {
        lCustomerKey: '',
        vc30Name: '',
        ch6ShortCode: '',
        lCustomerHierarchyType: '',
        lCustomerTypeKey: '',
        lCustomerStatusTypeKey: '',
        lMotorClubKey: '',
        fDiscountPct: '',
        bPORequired: false,
        bVINRequired: false,
        bOdometerRequired: false,
        bRORequired: false,
        bMileageRequired: false,
        vc30AccountID: '',
        vc100Location_NewCallDefault: '',
        vc100Destination_NewCallDefault: '',
        lTowTypeKey_NewCallDefault: '',
        lReasonTypeKey_NewCallDefault: '',
        lTruckTypeKey_NewCallDefault: '',
        vc50EquipmentRequired_NewCallDefault: '',
        tPriority_NewCallDefault: '',
        vc30CallerName_NewCallDefault: '',
        vc20PhoneNum_NewCallDefault: '',
        vc30Address1_Physical: '',
        vc30Address2_Physical: '',
        vc30City_Physical: '',
        ch2StateKey_Physical: '',
        vc10Zip_Physical: '',
        vc30ContactName_Physical: '',
        vc30Attention_Billing: '',
        vc30Address1_Billing: '',
        vc30Address2_Billing: '',
        vc30City_Billing: '',
        ch2StateKey_Billing: '',
        vc10Zip_Billing: '',
        vc30ContactName_Billing: '',
        vc20Phone1_Physical: '',
        vc20Phone2_Physical: '',
        vc20Fax_Physical: '',
        vc50Email_Physical: '',
        vc20Phone1_Billing: '',
        vc20Phone2_Billing: '',
        vc20Fax_Billing: '',
        vc50Email_Billing: '',
        vc255Notes_Driver: '',
        vc255Notes_Accounting: '',
        vc255Notes: '',
        vc255Notes_CallTaker: '',
        lTermsTypeKey: '',
        pCreditLimit: '',
        bPortalToPortal_NewCallDefault: false,
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        bTransferredtoFES: false,
        lUserKey: '',
        dDateLastModified: '',
        bActive: false,
        bPaymentNecessary: false,
        bTaxExempt: false,
        vc20TaxCertNum: '',
        vc100UserDefined1_NewCallDefault: '',
        vc50UserDefined2_NewCallDefault: '',
        vc50UserDefined3_NewCallDefault: '',
        lETA_NewCallDefault: '',
        bPrintStatements: false,
        bPrintInvoices: false,
        lSalespersonKey: '',
        vc20DealerNum: '',
        dDealerExpiry: '',
        vc255Notes_Dispatch: '',
        bNoCharge: false,
        lFreeStorageMinutesOverride: '',
        dTransferredToFES: '',
        tcDefaultCommAmt: '',
        tcDefaultSecondCommAmt: '',
        dCreated: '',
        dEffective: '',
        dRenewal: '',
        vc50ParentName: '',
        lPPIBillingCustomerKey: '',
        gcLocationLatitude_NewCallDefault: '',
        gcLocationLongitude_NewCallDefault: '',
        gcDestinationLatitude_NewCallDefault: '',
        gcDestinationLongitude_NewCallDefault: '',
        // lUserKey_CreatedBy: '',
        PPIAuthorities: []
      },

      sections: {
        customer: {
          name: 'Customer',
          landingPoint: 'customer.vc30Name',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.lCustomerStatusTypeKey'
        },
        accounting: {
          name: 'Accounting',
          landingPoint: 'customer.bTaxExempt',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.lSalespersonKey'
        },
        newCallDefaults: {
          name: 'New Call Defaults',
          landingPoint: 'customer.vc30CallerName_NewCallDefault',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.vc255Notes_Dispatch'
        },
        physicalLocation: {
          name: 'Physical Location',
          landingPoint: 'customer.vc30Address1_Physical',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.vc20Fax_Physical'
        },
        billingLocation: {
          name: 'Billing Location',
          landingPoint: 'customer.vc30Attention_Billing',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.vc20Fax_Billing'
        },
        miscellaneous: {
          name: 'Miscellaneous',
          landingPoint: 'customer.vc20DealerNum',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.vc255Notes'
        },
        ppi: {
          name: 'PPI',
          landingPoint: 'customer.lPPIBillingCustomerKey',
          jumpBackPoint: null,
          jumpForwardPoint: 'customer.dRenewal'
        }
      }
    };
  },

  computed: {
    ...mapGetters(['TOPSCOMPANY__settings']),

    searchContext () {
      return this.searchMode ? 'search' : 'active';
    },

    isRestrictedToAuction () {
      return Access.has('customers.restrictToAuction');
    },

    isAuctionCustomer () {
      return Number(this.record.lCustomerTypeKey) === VALUE_ID.customerType.auction;
    },

    isMotorClubCustomer () {
      return Number(this.record.lCustomerTypeKey) === VALUE_ID.customerType.motorClub;
    },

    canEdit () {
      if (this.isRestrictedToAuction) {
        return this.isAuctionCustomer;
      }

      return Access.has('customers.edit');
    },

    canEditEssentials () {
      return this.canEdit && Access.has('customers.editEssentials');
    },

    canEditCreditLimit () {
      return this.canEdit && Access.has('customers.editCreditLimit');
    },

    canEditTerms () {
      return this.canEdit && Access.has('customers.editTerms');
    },

    canEditPPIAuthority () {
      return this.canEdit && Access.has('customers.editPPI');
    },

    canViewTaxAndDiscount () {
      return Access.has('customers.viewTaxAndDiscount');
    },

    canDelete () {
      return this.canEdit && Access.has('customers.delete');
    },

    canDuplicate () {
      return this.canEdit;
    },

    filteredCustomerTypes () {
      if (this.isRestrictedToAuction) {
        return filter(this.customerTypes, type => {
          return Number(type.Key) === VALUE_ID.customerType.auction;
        });
      }

      return this.customerTypes;
    },

    physicalLocationExists () {
      return is.all.truthy([
        this.record.vc30Address1_Physical,
        this.record.vc30City_Physical,
        this.record.ch2StateKey_Physical,
        this.record.vc10Zip_Physical
      ]);
    },

    billingLocationExists () {
      return is.all.truthy([
        this.record.vc30Address1_Billing,
        this.record.vc30City_Billing,
        this.record.ch2StateKey_Billing,
        this.record.vc10Zip_Billing
      ]);
    },

    billingCustomerIsRequired () {
      return is.all.truthy([
        Number(this.record.lCustomerTypeKey) === VALUE_ID.customerType.ppi,
        isEmpty(toString(this.record.lPPIBillingCustomerKey))
      ]);
    },

    billingLocationIsRequired () {
      let validCustomerTypes = [
        VALUE_ID.customerType.account,
        VALUE_ID.customerType.auction,
        VALUE_ID.customerType.accountOther
      ];

      return Number(this.record.lCustomerStatusTypeKey) === VALUE_ID.customerStatus.approved &&
        includes(validCustomerTypes, Number(this.record.lCustomerTypeKey));
    },

    ppiAuthoritiesAreRequired () {
      let isRequired = false;

      forEach(this.record.PPIAuthorities, authority => {
        if (isEmpty(authority.vc50Name)) isRequired = true;
        if (isEmpty(authority.vc20Phone)) isRequired = true;
      });

      return isRequired;
    },

    taxCertificationIsRequired () {
      return Number(this.record.bTaxExempt) && isEmpty(this.record.vc20TaxCertNum);
    },

    riskyChanges () {
      let changes = [];

      if (this.isNewRecord) return changes;

      if (is.inArray('vc30Name', this.changedProperties)) {
        changes.push('Customer Name has changed.');
      }

      if (is.inArray('ch6ShortCode', this.changedProperties)) {
        changes.push('Customer Short Code has changed.');
      }

      if (is.inArray('lCustomerStatusTypeKey', this.changedProperties)) {
        changes.push('Customer Status has changed.');
      }

      if (is.inArray('lCustomerTypeKey', this.changedProperties)) {
        changes.push('Customer Type has changed.');
      }

      if (is.inArray('bTaxExempt', this.changedProperties)) {
        changes.push('Tax Exempt status has changed.');
      }

      if (!this.physicalLocationExists && this.billingLocationExists) {
        changes.push('Physical Location is incomplete even though the Billing Location exists.');
      }

      if (!this.billingLocationExists && this.physicalLocationExists) {
        changes.push('Billing Location is incomplete even though the Physical Location exists.');
      }

      if (this.billingCustomerCallCount > 0) {
        changes.push(`This Customer is the Billing Customer on ${this.billingCustomerCallCount} Call(s). Changing it to PPI will remove it from the list of available Billing Customers. It will also be updated on active (unconfirmed) Calls and will be blank on inactive (confirmed) Calls, although the old value will be recorded in Accounting Notes.`);
      }

      return changes;
    },

    nameCodePair () {
      return `${this.record.vc30Name} ${this.record.ch6ShortCode}`;
    },

    customerTypeProxy () {
      return this.record.lCustomerTypeKey;
    },

    createdByPair () {
      if (this.isNewRecord) {
        return '';
      }

      return `${this.record.sCreatedBy} at ` + fullDateTime(this.record.dCreated);
    },

    /**
     * API wants a stringnumber, but we want a boolean.
     */
    tcDefaultSecondCommAmtProxy: {
      get () {
        return Number(this.record.tcDefaultSecondCommAmt) === 1;
      },
      set (value) {
        this.record.tcDefaultSecondCommAmt = value ? '1' : '0';
      }
    }
  },

  watch: {
    nameCodePair () {
      this.testCustomerDuplicate();
    },

    customerDuplicate () {
      this.viewCustomerPrompt();
    },

    customerTypeProxy () {
      this.getBillingCustomerCalls();
    }
  },

  methods: {
    ...mapActions([
      'CALL__getReasons',
      'TOPSCOMPANY__getEmployees',
      'CALL__getTruckTypes',
      'MAP__getCoordinates',
      'TOPSCOMPANY__getLots',
      'CALL__getCustomers',
      'CUSTOMER__getTerms',
      'CUSTOMER__getTypes',
      'CUSTOMER__getStatuses',
      'CUSTOMER__getMotorClubs',
      'CUSTOMER__testDuplicate',
      'CUSTOMER__getBillingCalls',
      'TOPSCOMPANY__getLotCoordinates',
      'CUSTOMER__getBillingCustomers'
    ]),

    formatDestinationLot () {
      let lot = find(this.lots, ['Key', this.destinationLot]);

      if (lot) {
        this.record.vc100Destination_NewCallDefault = `{${lot.ShortCode}}`;
        this.getStorageLotCoordinates(lot);
      }
    },

    getStorageLotCoordinates (lot) {
      this.TOPSCOMPANY__getLotCoordinates({
        lotKey: lot.Key,
        callback: response => {
          if (is.all.falsy([response.Latitude, response.Longitude])) return false;

          this.record.gcDestinationLatitude_NewCallDefault = response.Latitude;
          this.record.gcDestinationLongitude_NewCallDefault = response.Longitude;
        }
      });
    },

    geocodeLocation () {
      if (isEmpty(this.record.vc100Location_NewCallDefault)) return;

      this.MAP__getCoordinates({
        location: this.record.vc100Location_NewCallDefault,
        callback: response => {
          if (is.all.falsy([response.Latitude, response.Longitude])) return false;

          this.$set(this.record, 'gcLocationLatitude_NewCallDefault', response.Latitude);
          this.$set(this.record, 'gcLocationLongitude_NewCallDefault', response.Longitude);
        }
      });
    },

    geocodeDestination () {
      if (isEmpty(this.record.vc100Destination_NewCallDefault)) return;

      this.MAP__getCoordinates({
        location: this.record.vc100Destination_NewCallDefault,
        callback: response => {
          if (is.all.falsy([response.Latitude, response.Longitude])) return false;

          this.$set(this.record, 'gcDestinationLatitude_NewCallDefault', response.Latitude);
          this.$set(this.record, 'gcDestinationLongitude_NewCallDefault', response.Longitude);
        }
      });
    },

    copyFromBilling () {
      this.record.vc30Address1_Physical = this.record.vc30Address1_Billing;
      this.record.vc30Address2_Physical = this.record.vc30Address2_Billing;
      this.record.vc30City_Physical = this.record.vc30City_Billing;
      this.record.ch2StateKey_Physical = this.record.ch2StateKey_Billing;
      this.record.vc10Zip_Physical = this.record.vc10Zip_Billing;
      this.record.vc30ContactName_Physical = this.record.vc30ContactName_Billing;
      this.record.vc50Email_Physical = this.record.vc50Email_Billing;
      this.record.vc20Phone1_Physical = this.record.vc20Phone1_Billing;
      this.record.vc20Phone2_Physical = this.record.vc20Phone2_Billing;
      this.record.vc20Fax_Physical = this.record.vc20Fax_Billing;
    },

    copyFromPhysical () {
      this.record.vc30Address1_Billing = this.record.vc30Address1_Physical;
      this.record.vc30Address2_Billing = this.record.vc30Address2_Physical;
      this.record.vc30City_Billing = this.record.vc30City_Physical;
      this.record.ch2StateKey_Billing = this.record.ch2StateKey_Physical;
      this.record.vc10Zip_Billing = this.record.vc10Zip_Physical;
      this.record.vc30ContactName_Billing = this.record.vc30ContactName_Physical;
      this.record.vc50Email_Billing = this.record.vc50Email_Physical;
      this.record.vc20Phone1_Billing = this.record.vc20Phone1_Physical;
      this.record.vc20Phone2_Billing = this.record.vc20Phone2_Physical;
      this.record.vc20Fax_Billing = this.record.vc20Fax_Physical;
    },

    removeAuthority (index) {
      this.record.PPIAuthorities.splice(index, 1);
    },

    addAuthority () {
      const authority = {
        lCustomerKey: this.record.lCustomerKey,
        lOrder: this.record.PPIAuthorities.length + 1,
        vc50Name: '',
        vc20Phone: ''
      };

      this.record.PPIAuthorities.push(authority);
    },

    saveProxy () {
      if (this.billingCustomerIsRequired) {
        this.$hub.$emit(EVENT_WARNING, 'A Billing Customer must be selected.');
        return;
      }

      if (this.billingLocationIsRequired && !this.billingLocationExists) {
        this.$hub.$emit(EVENT_WARNING, 'A Billing Location must be provided.');
        return;
      }

      if (this.ppiAuthoritiesAreRequired) {
        this.$hub.$emit(EVENT_WARNING, 'All PPI Authorities require valid names and phones.');
        return;
      }

      if (this.taxCertificationIsRequired) {
        this.$hub.$emit(EVENT_WARNING, 'A Tax Certification Number must be provided.');
        return;
      }

      if (this.riskyChanges.length > 0) {
        this.riskyChangesVisible = true;
        return;
      }

      this.save({
        onCreated: () => {
          this.$hub.$emit(EVENT_INFO, 'Customer added.');
        }
      });
    },

    getBillingCustomerCalls () {
      if (!this.watchForUserChanges) return;
      if (Number(this.record.lCustomerTypeKey) !== VALUE_ID.customerType.ppi) return;

      this.CUSTOMER__getBillingCalls({
        key: this.record.lCustomerTypeKey,
        callback: response => {
          this.billingCustomerCallCount = Number(response.Value);
        }
      });
    },

    testCustomerDuplicate: debounce(function () {
      if (!this.shouldLookupDuplicate) return;
      if (!this.record.vc30Name && !this.record.ch6ShortCode) return;

      this.CUSTOMER__testDuplicate({
        name: this.record.vc30Name,
        shortcode: this.record.ch6ShortCode,
        callback: response => {
          this.customerDuplicate = response;
        }
      });
    }, 1 * 1000),

    viewCustomerPrompt () {
      if (isEmpty(get(this.customerDuplicate, 'Key', ''))) return;

      let message = 'This customer already exists.';

      if (get(this.customerDuplicate, 'Active', 'false') === 'false') {
        message += ' Would you like to reactivate it or add a new one?';
      } else {
        message += ' Would you like to edit it or add a new one?';
      }

      this.$confirm(message, 'Edit existing customer?', {
        confirmButtonText: 'Edit',
        cancelButtonText: 'Add',
        type: 'info'
      }).then(() => {
        this.jumpTo(this.customerDuplicate.Key);
      }).catch(() => {
        // Do nothing
      });
    },

    propertyChanged (name) {
      if (!this.watchForUserChanges) return;

      this.changedProperties.push(name);
      this.changedProperties = uniq(this.changedProperties);
    },

    beforeGetViewData () {
      this.watchForUserChanges = false;
      this.shouldLookupDuplicate = false;
    },

    afterGetViewData () {
      // Make sure that no changes are captured as a result of the initial load
      // or flipping to a different record. We only want changes caused
      // by the user.
      setTimeout(() => {
        this.changedProperties = [];
        this.watchForUserChanges = true;
      }, 1000);
    },

    afterFillDefaultValues () {
      if (this.isNewRecord) {
        this.record.fDiscountPct = 0;
        this.record.bPrintStatements = true;
        this.record.bPrintInvoices = true;
      }
    },

    jumpTo (key) {
      this.$router.push({
        name: this.viewConfig.readRouteName,
        params: { key: key }
      });

      this.getNow();
      this.getViewData();
    },

    async viewQuotes () {
      await this.remoteUpdateSettings('Quote', [{
        And: false,
        Or: false,
        Not: false,
        OpenParen: false,
        FieldID: 'sCustomer',
        Operator: '=',
        DisplayValue: this.record.vc30Name,
        CloseParen: false
      },
      {
        And: true,
        Or: false,
        Not: false,
        OpenParen: false,
        FieldID: 'lCall',
        Operator: 'Is',
        DisplayValue: 'Empty',
        CloseParen: false
      }]);

      this.$router.push({ name: 'Quotes' });
    }
  },

  mounted () {
    this.CUSTOMER__getTypes({
      callback: response => {
        this.customerTypes = response;
      }
    });

    this.CUSTOMER__getStatuses({
      callback: response => {
        this.statuses = response;
      }
    });

    this.CUSTOMER__getTerms({
      callback: response => {
        this.terms = response;
      }
    });

    this.CUSTOMER__getMotorClubs({
      callback: response => {
        this.motorclubs = response;
      }
    });

    this.TOPSCOMPANY__getEmployees({
      callback: response => {
        this.salespersons = response;
      }
    });

    this.CALL__getReasons({
      callback: response => {
        this.reasons = response;
      }
    });

    this.CALL__getTruckTypes({
      callback: response => {
        this.truckTypes = response;
      }
    });

    this.CUSTOMER__getBillingCustomers({
      callback: response => {
        this.customers = response;
      }
    });

    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.lots = response;
      }
    });
  }
};
</script>
