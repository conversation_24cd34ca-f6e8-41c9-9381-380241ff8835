<template>
  <div id="assign-call">
    <app-titlebar :title="assignCallTitle"></app-titlebar>

    <section class="summary">
      <div class="_card">
        <app-data-point class="_trip" label="Trip">
          <i class="far fa-route-interstate"></i> {{ location.description }} &rarr; {{ destination.description }}
        </app-data-point>
        <app-data-point class="_driver" label="Driver">
          <i class="far fa-id-card"></i> <template v-if="$selectedDriverCode && $selectedTruckNumber">{{ $selectedDriverCode }} &middot; {{ $selectedTruckNumber }}</template>
        </app-data-point>
        <app-data-point class="_vehicle" label="Vehicle">
          <i class="far fa-car"></i> {{ vehicleInformation || '--' }}
        </app-data-point>
      </div>
    </section>

    <section class="map">
      <call-map
        :center="center"
        :zoom="11"
        :markers="markers"
        :call-key="$route.params.key"
        :is-retow="isRetow"
        :zoom-control="true"
        @marker-click="onMarkerClick($event)"
        @marker-drop="onMarkerDrop">
      </call-map>

      <div class="_truck-tooltip"
        ref="truckTooltip"
        :style="truckTooltipCoordinatesCss"
        @blur="toggleTruckTooltip(false)"
        tabindex="-1">
        <app-data-point label="Truck" v-if="$selectedTruckNumber">
          {{ $selectedTruckNumber }}
        </app-data-point>

        <app-data-point label="Driver">
          {{ $selectedDriverCode }}
        </app-data-point>

        <app-button @click="assign" type="primary" :disabled="!requirementsMet">
          Assign
        </app-button>
      </div>
    </section>

    <DispatchUnitsController
      :selected-driver.sync="selectedDriver"
      :selected-truck.sync="selectedTruck"
      @on-trucks-loaded="addMarkersForTrucks" />

    <app-footerbar>
      <span class="is-small" slot="left">
        Location: <span class="is-selectable">{{ location.lat }}, {{ location.lng }}</span>
      </span>

      <app-button @click="$router.go(-1)" type="default">Cancel</app-button>
      <app-button @click="assign" type="primary" :disabled="!requirementsMet">Assign</app-button>
    </app-footerbar>
  </div>
</template>

<script>import { get, has } from 'lodash-es';


import { mapActions, mapGetters } from 'vuex';
import { ASSET_PATH, VALUE_ID } from '@/config.js';
import CallMap from '@/components/features/CallMap.vue';
import DispatchUnitsController from '@/components/dispatchunits/Controller.vue';

export default {
  name: 'assign-call',

  components: {
    CallMap,
    DispatchUnitsController
  },

  data () {
    return {
      markers: [],
      towType: '',
      isRetow: false,
      selectedTruck: null,
      callStatusKey: null,
      selectedDriver: null,
      vehicleInformation: '',
      companyLocation: {
        lat: '',
        lng: ''
      },
      destination: {
        lat: '',
        lng: '',
        description: ''
      },
      location: {
        lat: '',
        lng: '',
        description: ''
      },
      center: {
        lat: 35.0519427,
        lng: -85.313166
      },

      truckTooltip: {
        visible: false,
        position: {
          x: 0,
          y: 0
        }
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    truckTooltipCoordinatesCss () {
      return {
        '--x': this.truckTooltip.position.x,
        '--y': this.truckTooltip.position.y
      };
    },

    assignCallTitle () {
      return `Assign #${this.$route.params.key}`;
    },

    requirementsMet () {
      return !!this.selectedDriver && !!this.selectedTruck;
    },

    $selectedDriverCode () {
      return get(this.selectedDriver, 'Code', '');
    },

    $selectedTruckNumber () {
      return get(this.selectedTruck, 'Number', '');
    }
  },

  methods: {
    ...mapActions([
      'CALL__handleAction',

      'TOPSCALL__assign',
      'TOPSCALL__getDataToAssign',
      'TOPSCALL__assignAdditional',
      'TOPSCALL__getDriverTruckToAssign'
    ]),

    getCallInformation () {
      this.TOPSCALL__getDataToAssign({
        callKey: this.$route.params.key,
        callback: response => {
          this.vehicleInformation = response.Vehicle;
          this.callStatusKey = Number(response.CallStatus);
          this.towType = response.TowType;
          this.isRetow = response.Retow;

          if (!!response.SubcompanyLat && !!response.SubcompanyLon) {
            this.companyLocation.lat = response.SubcompanyLat;
            this.companyLocation.lng = response.SubcompanyLon;
          }

          if (has(response, 'Destination')) {
            this.destination.description = response.Destination.Place;

            if (!!response.Destination.Lat && !!response.Destination.Lon) {
              this.destination.lat = response.Destination.Lat;
              this.destination.lng = response.Destination.Lon;

              this.markers.push({
                key: 'destination',
                type: 'destination',
                position: {
                  lat: this.destination.lat,
                  lng: this.destination.lng
                },
                draggable: false,
                clickable: false,
                icon: { url: ASSET_PATH + '/checkered-flag-marker.svg' },
                infoText: this.destination.description
              });
            }
          }

          if (has(response, 'Location')) {
            this.location.description = response.Location.Place;

            if (!!response.Location.Lat && !!response.Location.Lon) {
              this.location.lat = response.Location.Lat;
              this.location.lng = response.Location.Lon;

              this.markers.push({
                key: 'location',
                type: 'location',
                position: {
                  lat: this.location.lat,
                  lng: this.location.lng
                },
                draggable: true,
                clickable: false,
                icon: { url: ASSET_PATH + '/car-marker.svg' },
                infoText: this.location.description
              });

              this.center = {
                lat: this.location.lat,
                lng: this.location.lng
              };
            }
          }
        }
      });
    },

    getNow () {
      return new Promise(resolve => {
        this.$store.dispatch('__getNow', {
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async assign () {
      if (!this.selectedDriver) { return; }
      if (!this.selectedTruck) { return; }

      let assignVerb = '';

      switch (this.callStatusKey) {
        case VALUE_ID.callStatus.unassigned:
        case VALUE_ID.callStatus.retow:
          assignVerb = 'TOPSCALL__assign';
          break;

        case VALUE_ID.callStatus.dispatched:
        case VALUE_ID.callStatus.retowDispatch:
        default:
          assignVerb = 'TOPSCALL__assignAdditional';
          break;
      }

      this[assignVerb]({
        lastRead: (await this.getNow()).Now,
        callKey: this.$route.params.key,
        driverCode: this.$selectedDriverCode,
        truckNumber: this.$selectedTruckNumber,
        callback: response => {
          if (Number(this.TOPSCOMPANY__settings.bSendDispatchPage) === 1) {
            this.$router.replace({
              name: 'Notify',
              query: {
                callKey: this.$route.params.key,
                dispatchKey: response.DispatchKey,
                dispatchDriverKey: this.$selectedDriverCode,
                dispatchTruckKey: this.$selectedTruckNumber
              }
            });
          } else {
            this.$router.go(-1);
          }
        }
      });
    },

    async addMarkersForTrucks (trucks = []) {
      const markers = trucks.map(truck => {
        const latitude = get(truck, 'Location.Lat', null);
        const longitude = get(truck, 'Location.Lon', null);

        if (!latitude) { return null; }
        if (!longitude) { return null; }

        return {
          key: truck.Number,
          type: 'truck',
          truckNumber: truck.Number,
          position: {
            lat: Number(latitude),
            lng: Number(longitude)
          },
          draggable: false,
          clickable: true,
          icon: { url: truck.ImageURL },
          infoText: truck.Number
        };
      }).filter(marker => marker !== null);

      this.markers.push(...markers);
    },

    onMarkerClick ({ $event, marker }) {
      if (!marker.clickable) { return; }

      this.truckTooltip.position.x = $event.domEvent.pageX;
      this.truckTooltip.position.y = $event.domEvent.pageY;
      this.toggleTruckTooltip();

      if (marker.type !== 'truck') { return; }
      if (!marker.truckNumber) { return; }

      const truck = (this.$store.getters['assignCall.trucks']).find(truck => truck.Number === marker.truckNumber);
      if (truck) {
        this.selectedTruck = truck;
      }

      const driver = (this.$store.getters['assignCall.drivers']).find(driver => driver.Truck === truck.Number);
      if (driver) {
        this.selectedDriver = driver;
      }
    },

    onMarkerDrop (marker) {
      if (marker.key !== 'location') { return; }

      this.location.lat = marker.position.lat;
      this.location.lng = marker.position.lng;
    },

    toggleTruckTooltip (value = !this.truckTooltip.visible) {
      let visible = value;
      let timeline = this.$gsap.timeline({
        defaults: {
          duration: 0.2
        }
      });

      this.truckTooltip.visible = visible;

      if (visible) {
        timeline
          .fromTo(this.$refs.truckTooltip, {
            autoAlpha: 0,
            scale: 0.5
          }, {
            autoAlpha: 1,
            scale: 1,
            ease: 'back.out(2)'
          })
          .call(() => {
            this.$nextTick(() => {
              this.$refs.truckTooltip.focus();
            });
          });
      } else {
        timeline
          .to(this.$refs.truckTooltip, {
            autoAlpha: 0,
            scale: 0.5,
            ease: 'power1.in'
          });
      }
    },

    initializeTruckTooltip () {
      this.$gsap.to(this.$refs.truckTooltip, {
        autoAlpha: 0,
        scale: 0.5,
        duration: 0.2,
        ease: 'power1.in'
      });
    }
  },

  async mounted () {
    this.getCallInformation();
    this.initializeTruckTooltip();
  }
};
</script>
