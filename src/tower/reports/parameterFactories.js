import { format, subMonths } from 'date-fns';

/**
 * Factory function for text input parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createTextParameter(label, overrides = {}) {
  return {
    label,
    control: 'InputText',
    isVisible: true,
    ...overrides
  };
}

/**
 * Factory function for date input parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createDateParameter(label, overrides = {}) {
  const today = format(new Date(), 'MM/DD/YYYY');
  const defaultTime = label.toLowerCase().includes('end') ? '23:59' : '00:00';

  return {
    label,
    control: 'InputDate',
    value: `${today} ${defaultTime}`,
    isVisible: true,
    ...overrides
  };
}

/**
 * Factory function for checkbox parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createCheckboxParameter(label, overrides = {}) {
  return {
    label,
    control: 'InputCheckbox',
    isVisible: true,
    ...overrides
  };
}

/**
 * Factory function for select dropdown parameters
 * @param {string} label - The display label for the parameter
 * @param {string} action - The action to fetch options
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createSelectParameter(label, action, overrides = {}) {
  return {
    label,
    control: 'InputSelect',
    action,
    value: -1,
    addAllOption: true,
    keyAlias: 'Key',
    valueAlias: 'Value',
    shortCodeAlias: 'ShortCode',
    isVisible: true,
    ...overrides
  };
}

/**
 * Factory function for select parameters that return value instead of key
 * @param {string} label - The display label for the parameter
 * @param {string} action - The action to fetch options
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createSelectValueParameter(label, action, overrides = {}) {
  return createSelectParameter(label, action, {
    keyAlias: 'Value',
    valueAlias: 'Value',
    shortCodeAlias: 'ShortCode',
    ...overrides
  });
}

/**
 * Factory function for simple select parameters (app-select control)
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createSimpleSelectParameter(label, overrides = {}) {
  return {
    label,
    control: 'InputSelect',
    keyAlias: 'Key',
    valueAlias: 'Value',
    shortCodeAlias: 'ShortCode',
    isVisible: true,
    ...overrides
  };
}

/**
 * Factory function for month select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createMonthParameter(label = 'Calendar Month', overrides = {}) {
  return createSelectParameter(label, 'getMonths', {
    value: format(subMonths(new Date(), 1), 'M'),
    ...overrides
  });
}

/**
 * Factory function for year select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createYearParameter(label = 'Calendar Year', overrides = {}) {
  return createSelectParameter(label, 'getYears', {
    value: format(new Date(), 'YYYY'),
    ...overrides
  });
}

/**
 * Factory function for customer select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createCustomerParameter(label = 'Customer', overrides = {}) {
  return createSelectParameter(label, 'TOPSCOMPANY__getCustomers', overrides);
}

/**
 * Factory function for customer name select parameters (returns name instead of key)
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createCustomerNameParameter(label = 'Customer', overrides = {}) {
  return createSelectValueParameter(label, 'TOPSCOMPANY__getCustomers', overrides);
}

/**
 * Factory function for driver select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createDriverParameter(label = 'Driver', overrides = {}) {
  return createSelectParameter(label, 'TOPSCOMPANY__getDrivers', overrides);
}

/**
 * Factory function for truck select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createTruckParameter(label = 'Truck', overrides = {}) {
  return createSelectParameter(label, 'TOPSCOMPANY__getTrucks', overrides);
}

/**
 * Factory function for employee select parameters
 * @param {string} label - The display label for the parameter
 * @param {Object} overrides - Optional properties to override defaults
 * @returns {Object} Parameter configuration object
 */
export function createEmployeeParameter(label = 'Employee', overrides = {}) {
  return createSelectParameter(label, 'TOPSCOMPANY__getEmployees', overrides);
}
