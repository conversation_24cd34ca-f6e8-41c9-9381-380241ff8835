import { describe, it, expect, vi } from 'vitest';
import { useReportParameters } from '../useReportParameters.js';

describe('useReportParameters', () => {
  it('should initialize with empty parameters', () => {
    const { parameters, temporaryValues } = useReportParameters();

    expect(parameters.value).toEqual([]);
    expect(temporaryValues.value).toEqual({});
  });

  it('should add and retrieve parameters', () => {
    const { parameters, getParameter, setParameter } = useReportParameters();

    // Add a parameter to the array
    parameters.value.push({
      Name: 'TEST_PARAM',
      Value: 'test_value',
      Required: true,
      Control: 'app-text',
      IsVisible: true
    });

    // Test getParameter
    const param = getParameter('TEST_PARAM');
    expect(param).toBeTruthy();
    expect(param.Value).toBe('test_value');

    // Test setParameter
    setParameter('TEST_PARAM', 'new_value');
    expect(getParameter('TEST_PARAM').Value).toBe('new_value');
  });

  it('should handle parameter visibility', () => {
    const { isControlVisible } = useReportParameters();

    // Test visible parameter
    const visibleParam = {
      IsVisible: true,
      Control: 'app-text',
      Options: []
    };
    expect(isControlVisible(visibleParam)).toBe(true);

    // Test invisible parameter
    const invisibleParam = {
      IsVisible: false,
      Control: 'app-text',
      Options: []
    };
    expect(isControlVisible(invisibleParam)).toBe(false);

    // Test app-select-report with no options
    const selectReportParam = {
      IsVisible: true,
      Control: 'app-select-report',
      Options: []
    };
    expect(isControlVisible(selectReportParam)).toBe(false);

    // Test app-select-report with options
    const selectReportParamWithOptions = {
      IsVisible: true,
      Control: 'app-select-report',
      Options: [{Key: 1, Value: 'Option 1'}, {Key: 2, Value: 'Option 2'}]
    };
    expect(isControlVisible(selectReportParamWithOptions)).toBe(true);
  });

  it('should handle temporary values', () => {
    const { temporaryValues, setTemporaryValue } = useReportParameters();

    const input = { id: 'TEST_INPUT', value: 'test_value' };
    setTemporaryValue(input);

    expect(temporaryValues.value.TEST_INPUT).toBe('test_value');
  });

  it('should handle canViewReport computed property', () => {
    const { parameters, canViewReport } = useReportParameters();

    // No parameters - should return false
    expect(canViewReport.value).toBe(false);

    // Add required parameter without value
    parameters.value.push({
      Name: 'REQUIRED_PARAM',
      Value: '',
      Required: true,
      Control: 'app-text'
    });

    // Should return false due to missing required value
    expect(canViewReport.value).toBe(false);

    // Add value to required parameter
    parameters.value[0].Value = 'some_value';

    // Should return true
    expect(canViewReport.value).toBe(true);
  });



  it('should clear parameters', () => {
    const { parameters, temporaryValues, clearParameters } = useReportParameters();

    // Add some data
    parameters.value.push({ Name: 'TEST' });
    temporaryValues.value.TEST = 'value';

    // Clear
    clearParameters();

    expect(parameters.value).toEqual([]);
    expect(temporaryValues.value).toEqual({});
  });
});
