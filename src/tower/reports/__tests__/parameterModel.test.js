import { describe, it, expect } from 'vitest';
import { parameterModel } from '../parameterModel.js';

describe('parameterModel', () => {
  it('should maintain backwards compatibility with original structure', () => {
    // Test a few key parameters to ensure they have the expected structure

    // Test text parameter
    expect(parameterModel.INVOICE_NUM).toEqual({
      label: 'Invoice Number',
      control: 'InputText',
      isVisible: true
    });

    // Test date parameter
    expect(parameterModel.BEGIN_DATE_TIME).toEqual({
      label: 'Start Date',
      control: 'InputDate',
      value: expect.any(String), // Should be formatted date
      isVisible: true
    });

    // Test select parameter
    expect(parameterModel.CUSTOMER_KEY).toEqual({
      label: 'Customer',
      control: 'InputSelect',
      addAllOption: true,
      action: 'TOPSCOMPANY__getCustomers',
      value: -1,
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    });

    // Test select value parameter (returns name instead of key)
    expect(parameterModel.CUSTOMER_NAME).toEqual({
      label: 'Customer',
      control: 'InputSelect',
      addAllOption: true,
      action: 'TOPSCOMPANY__getCustomers',
      value: -1,
      keyAlias: 'Value',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    });

    // Test checkbox parameter
    expect(parameterModel.PO_REQD).toEqual({
      label: 'P.O. Number Required',
      control: 'InputCheckbox',
      isVisible: true
    });

    // Test parameter with custom options
    expect(parameterModel.BILLING_CUSTOMER).toEqual({
      label: 'Customer Source',
      control: 'InputSelect',
      addAllOption: false,
      action: 'customerSources',
      value: 0,
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      emptyOption: false,
      isVisible: true
    });

    // Test text parameter with placeholder
    expect(parameterModel.CALLS_NUMS).toEqual({
      label: 'Call Numbers',
      control: 'InputText',
      placeholder: 'e.g. 123, 456, 789',
      isVisible: true
    });

    // Test month parameter
    expect(parameterModel.MONTH).toEqual({
      label: 'Calendar Month',
      control: 'InputSelect',
      addAllOption: true,
      action: 'getMonths',
      value: expect.any(String), // Should be formatted month
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    });

    // Test year parameter
    expect(parameterModel.YEAR).toEqual({
      label: 'Calendar Year',
      control: 'InputSelect',
      addAllOption: true,
      action: 'getYears',
      value: expect.any(String), // Should be formatted year
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    });

    // Test special case parameter with custom logic
    expect(parameterModel.PREVIOUS_RUN_DATE).toEqual({
      label: 'Previous Run Date',
      control: 'InputSelect',
      parseOptions: expect.any(Function),
      keyAlias: 'Key',
      valueAlias: 'Value',
      shortCodeAlias: 'ShortCode',
      isVisible: true
    });
  });

  it('should handle PREVIOUS_RUN_DATE parseOptions correctly', () => {
    const { parseOptions } = parameterModel.PREVIOUS_RUN_DATE;

    // Test with valid comma-separated dates
    const result = parseOptions('2023-01-01,2023-01-02,2023-01-03');
    expect(result).toEqual([
      { Key: '2023-01-01', Value: '2023-01-01', ShortCode: '2023-01-01' },
      { Key: '2023-01-02', Value: '2023-01-02', ShortCode: '2023-01-02' },
      { Key: '2023-01-03', Value: '2023-01-03', ShortCode: '2023-01-03' }
    ]);

    // Test with empty string
    expect(parseOptions('')).toEqual([]);

    // Test with null
    expect(parseOptions(null)).toEqual([]);

    // Test with undefined
    expect(parseOptions(undefined)).toEqual([]);

    // Test with non-string
    expect(parseOptions(123)).toEqual([]);
  });

  it('should contain all expected parameter keys', () => {
    const expectedKeys = [
      'INVOICE_NUM', 'BEGIN_DATE_TIME', 'END_DATE_TIME', 'CUSTOMER_KEY',
      'CUSTOMER_NAME', 'BILLING_CUSTOMER', 'CUSTOMER_TYPE', 'CUSTTYPE_NAME',
      'LOT_KEY', 'SUBTERMINAL_KEY', 'CALL_NUM', 'CALLS_NUMS', 'DRIVER_KEY',
      'TRUCK_KEY', 'PO_REQD', 'RO_REQD', 'VIN_REQD', 'ODOMETER_REQD',
      'HOLD_REASON_TYPE', 'TOW_CLASS', 'COMPLETION_REVENUE_DATE', 'DRIVER_STATUS',
      'EMP_KEY', 'CERTIFICATION_NUM', 'EMPLOYEE_TYPE', 'JUST_DATE',
      'REVENUE_RECONCILIATION_DATE', 'INVOICES_NUMS', 'BARCODE_NUM',
      'BARCODES_NUMS', 'REASON_TYPE', 'MONTH', 'YEAR', 'CONFIRMED_LAST_30',
      'SERVICE_KEY', 'INCLUDE_CANCELED_CALLS', 'BAAN_NUM', 'PREVIOUS_RUN_DATE'
    ];

    expectedKeys.forEach(key => {
      expect(parameterModel).toHaveProperty(key);
    });
  });
});
