<template>
  <form id="reports-view" method="get" :action="txiApiUrl" target="_blank">
    <input v-for="(value, name) in flattenedPayload" type="hidden" :name="name" :value="value" :key="name">

    <app-titlebar title="Reports"></app-titlebar>

    <section class="report-list-panel">
      <header class="report-list-header">
        <input type="search" v-model="$store.state.report.searchTerm" placeholder="Filter reports..." class="search-input" />
        <button class="description-toggle" @click="showDescriptions = !showDescriptions" type="button" title="Toggle report descriptions">
          <i :class="showDescriptions ? 'fas fa-expand pure-blue' : 'far fa-expand'"></i>
        </button>
      </header>
      <div class="report-list-content">
        <div v-for="report in filteredReports" :key="report.Key" class="report-item"
          :class="{ active: $store.state.report.selectedReportKey === report.Key }" @click="selectReport(report.Key)" tabindex="1"
          @keydown.enter="selectReport(report.Key)" @keydown.space="selectReport(report.Key)" role="button"
          :aria-label="`Select report: ${report.Name}`">
          <div class="report-name" v-html="highlightSearchMatches(report.Name)"></div>
          <div v-if="showDescriptions" class="report-description is-small">{{ report.Description || 'No description available.' }}</div>
        </div>
      </div>
    </section>

    <section class="parameters-panel">
      <transition name="flip" mode="out-in" appear>
        <div class="parameters-liner" v-if="parameters" :key="$store.state.report.selectedReportKey">
          <app-tip v-if="!visibleParameters.length">Select a report to view.</app-tip>
          <component v-for="(parameter, index) in visibleParameters" :key="parameter.VueKey"
            :is="parameter.Control" :id="parameter.Name"
            :required="isRequiredIndicatorVisible(parameter)" v-model="parameter.Value"
            :options="parameter.Options" :keyAlias="parameter.KeyAlias" :valueAlias="parameter.ValueAlias"
            :shortCodeAlias="parameter.ShortCodeAlias" :tabindex="index + 2" :empty-option="parameter.EmptyOption"
            @change="setValue" @keyup="setValue">
            {{ parameter.Label }}
          </component>

          <footer>
            <input-button data-variant="blue" :disabled="!canViewReport" type="submit">
              View report
            </input-button>
            <button v-show="false" id="view-report-proxy"></button>
            <input-button id="email-report-trigger" popovertarget="email-report-dialog" :disabled="!canEmailReport" type="button">
              Send to email
            </input-button>
          </footer>
        </div>
      </transition>
    </section>

    <EmailReportModal v-if="selectedReport" :report="selectedReport" :parameters="parameters" />
  </form>
</template>

<script>
import { forEach, isBoolean, find, isEmpty, filter, includes, map, range, reverse, debounce } from 'lodash-es';
import { mapGetters, mapActions } from 'vuex';
import { useReportParameters } from '@/tower/reports/useReportParameters.js';
import { format, subYears, addYears } from 'date-fns';

import EmailReportModal from '@/tower/reports/EmailReportModal.vue';
import InputButton from '@/tower/liens/inputs/Button.vue';
import InputCheckbox from '@/tower/liens/inputs/Checkbox.vue';
import InputNumber from '@/tower/liens/inputs/Number.vue';
import InputSelect from '@/tower/liens/inputs/Select.vue';
import InputText from '@/tower/liens/inputs/Text.vue';
import InputDate from '@/tower/liens/inputs/Date.vue';

// Note: The process on this page is roundabout with the goal of opening
// the report in a new tab. The standard XHR request is hijacked while the data
// from that request is transformed and injected into the <form>.

export default {
  name: 'reports-view',

  components: {
    EmailReportModal,
    InputButton,
    InputCheckbox,
    InputNumber,
    InputSelect,
    InputText,
    InputDate
  },

  setup() {
    const {
      parameters,
      temporaryValues,
      visibleParameters,
      transformedParameters,
      canViewReport,
      hasParameter,
      isControlVisible,
      isRequiredIndicatorVisible,
      getParameter,
      setParameter,
      setValue,
      setTemporaryValue,
      hydrateParameters,
      clearParameters
    } = useReportParameters();

    return {
      parameters,
      temporaryValues,
      visibleParameters,
      transformedParameters,
      canViewReport,
      hasParameter,
      isControlVisible,
      isRequiredIndicatorVisible,
      getParameter,
      setParameter,
      setValue,
      setTemporaryValue,
      hydrateParameters,
      clearParameters
    };
  },

  data() {
    return {
      payload: {},
      txiApiUrl: ''
    };
  },

  computed: {
    ...mapGetters(['__state']),

    showDescriptions: {
      get() {
        return this.$store.state.report.showDescriptions;
      },
      set(value) {
        this.$store.state.report.showDescriptions = value;
      }
    },

    filteredReports() {
      if (!this.$store.state.report.searchTerm.trim()) return this.$store.state.report.reports;

      const searchLower = this.$store.state.report.searchTerm.toLowerCase();

      return this.$store.state.report.reports.filter(report => {
        const reportName = report.Name.toLowerCase();
        return reportName.includes(searchLower);
      });
    },

    flattenedPayload() {
      let parameters = {};

      forEach(this.payload, payloadSection => {
        forEach(payloadSection, (value, name) => {
          if (typeof value === 'object') {
            forEach(value, (value, name) => {
              parameters[name] = isBoolean(value) ? Number(value) : value;
            });
          } else {
            parameters[name] = isBoolean(value) ? Number(value) : value;
          }
        });
      });

      return parameters;
    },

    selectedReport() {
      let report = find(this.$store.state.report.reports, ['Key', this.$store.state.report.selectedReportKey]);

      return isEmpty(report) ? { Key: '', Description: '' } : report;
    },

    canEmailReport() {
      if (!this.canViewReport) return false;
      if (!this.selectedReport.EmailAvailable && !this.selectedReport.EmailToCustomersAvailable) return false;

      return true;
    }
  },

  watch: {
    transformedParameters() {
      this._renderRequestDebounced();
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getDrivers',
      'CALL__getReasons',
      'TOPSCOMPANY__getReports',
      'TOPSCOMPANY__getCustomers',
      'TOPSCOMPANY__getEmployees',
      'TOWTYPE__getClasses',
      'HOLD__getReasons',
      'TOPSCOMPANY__getLots',
      'CALL__getSubterminals',
      'CUSTOMER__getTypes',
      'TOPSCOMPANY__getEmployeeTypes',
      'TOPSCOMPANY__getDriverStatuses',
      'REPORT__getCreate',
      'REPORT__getParameters',
      'TOPSCOMPANY__getLocationServices',
      'PAYMENT__getPaymentTypes'
    ]),

    getReports() {
      this.TOPSCOMPANY__getReports({
        success: response => {
          this.$store.state.report.reports = response;

          if (this.$store.state.report.selectedReportKey) {
            this.getParameters();
          }
        }
      });
    },

    selectReport(reportKey) {
      this.$store.state.report.selectedReportKey = reportKey;
      this.getParameters();
    },

    clearSelection() {
      if (this.$store.state.report.selectedReportKey && !this.filteredReports.some(report => report.Key === this.$store.state.report.selectedReportKey)) {
        this.$store.state.report.selectedReportKey = '';
        this.clearParameters();
      }
    },

    highlightSearchMatches(text) {
      if (!this.$store.state.report.searchTerm.trim()) {
        return text;
      }

      const searchLower = this.$store.state.report.searchTerm.toLowerCase();
      const textLower = text.toLowerCase();

      const substringIndex = textLower.indexOf(searchLower);
      if (substringIndex === -1) {
        return text;
      }

      const beforeMatch = text.substring(0, substringIndex);
      const match = text.substring(substringIndex, substringIndex + searchLower.length);
      const afterMatch = text.substring(substringIndex + searchLower.length);

      return beforeMatch + `<mark>${match}</mark>` + afterMatch;
    },

    getParameters() {
      if (!this.$store.state.report.selectedReportKey) {
        this.clearParameters();
        return;
      }

      this.REPORT__getParameters({
        reportKey: this.$store.state.report.selectedReportKey,
        callback: response => {
          // The server will handle these.
          let filteredResponse = filter(response, parameter => {
            if (!includes(['CURRENT_USER', 'LOC_KEY'], parameter.Name)) return parameter;
          });

          this.parameters = this.hydrateParameters(filteredResponse, this);
        }
      });
    },

    renderRequest() {
      this.REPORT__getCreate({
        reportKey: this.$store.state.report.selectedReportKey,
        parameters: this.transformedParameters
      })
        .then(request => {
          this.txiApiUrl = request.url;
          this.payload = request.parameters;
        });
    },

    customerSources(props) {
      // No API method available, so hardcoding options for this parameter.
      let values = [
        { Key: 0, Value: 'Control Customer' },
        { Key: 1, Value: 'Billing Customer' },
        { Key: 2, Value: 'Sale Customer' }
      ];

      props.callback(values);
    },

    getMonths(props) {
      // No API method available, so hardcoding options for this parameter.
      let values = [
        { Key: 0, Value: 'January' },
        { Key: 1, Value: 'February' },
        { Key: 2, Value: 'March' },
        { Key: 3, Value: 'April' },
        { Key: 4, Value: 'May' },
        { Key: 5, Value: 'June' },
        { Key: 6, Value: 'July' },
        { Key: 7, Value: 'August' },
        { Key: 8, Value: 'September' },
        { Key: 9, Value: 'October' },
        { Key: 10, Value: 'November' },
        { Key: 11, Value: 'December' }
      ];

      props.callback(values);
    },

    getYears(props) {
      // No API method available, so hardcoding options for this parameter.
      let startYear = format(subYears(new Date(), 50), 'YYYY');
      let endYear = format(addYears(new Date(), 2), 'YYYY');

      let values = map(range(startYear, endYear), year => {
        return { Key: year, Value: year };
      });

      props.callback(reverse(values));
    }
  },

  created() {
    this._renderRequestDebounced = debounce(this.renderRequest, 300);
  },

  beforeDestroy() {
    if (this._renderRequestDebounced) {
      this._renderRequestDebounced.cancel();
    }
  },

  mounted() {
    this.getReports();
  }
};
</script>

<style scoped>
#reports-view {
  display: grid;
  grid-template-columns: 20rlh 1fr;
  grid-template-rows: 4rem 1fr;
  grid-template-areas:
    "titlebar titlebar"
    "listpanel parameterspanel";

  width: 100%;
  height: 100dvh;

  section {
    padding: 2rlh;
  }

  .report-list-panel {
    grid-area: listpanel;

    padding: 0.5rlh;
    background: color-mix(in oklch, var(--blue), transparent 90%);
    overflow-y: auto;
  }

  .report-list-header {
    position: sticky;

    display: flex;
    gap: 0.5rlh;
    top: 0;
    align-items: center;

    z-index: 1;
  }

  .report-list-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .search-input {
    flex: 1;
    padding: 0.5rlh;
    margin-bottom: 0.5rlh;
    background-color: color-mix(in oklch, var(--white), transparent 60%);
    backdrop-filter: blur(1rlh);
    border: 1px solid var(--white);
    border-radius: 3rlh;

    &:focus {
      outline: none;
    }

    &::placeholder {
      opacity: 0.6;
    }
  }

  .description-toggle {
    background: none;
    border: none;
    padding: 0.25rlh;
    margin-bottom: 0.5rlh;
    font-size: 1rem;
    color: var(--text-color);
    cursor: pointer;
    border-radius: 0.25rlh;
    transition: background-color 0.2s;

    &:hover {
      background-color: var(--bg-color-hover);
    }

    i {
      display: block;
    }
  }

  .report-item {
    padding: 0.5rlh 1.0rlh;
    border-radius: 0.25rlh;
    cursor: default;
    transition: all 0.2s ease;

    &:hover {
      background: var(--white);
      box-shadow: var(--box-shadow-50);
    }

    &.active {
      color: var(--pure-blue);
      background: var(--white);
    }

    .report-name {
      font-weight: 600;
    }

    .report-description {
      opacity: 0.8;
    }
  }

  .parameters-panel {
    grid-area: parameterspanel;
    place-items: center;

    background: color-mix(in oklab, var(--blue), var(--white) 95%);
    overflow-y: auto;

    &>* {
      max-width: 800px;
    }
  }

  .parameters-liner {
    display: flex;
    flex-direction: column;
    gap: 1rlh;

    inline-size: 55ch;
    padding: 1rlh;
    background: var(--white);
    border-radius: 0.5rlh;
    border: 1px solid color-mix(in oklch, var(--blue), transparent 80%);

    footer {
      margin-top: 1rlh;
    }
  }

  .title-bar {
    grid-area: titlebar;
  }

  /* UI Regress Begin */
  .parameters-panel {
    place-items: start;

    background: var(--white);
  }

  .parameters-liner {
    padding: 0;
    border: 0;
  }

  label {
    position: relative;

    display: flex;
    flex-direction: column;
    gap: 0;

    padding: 0;
    background-color: transparent;
    border: 0;

    &:deep(input),
    &:deep(textarea),
    &:deep(select) {
      padding: 0.25rlh;
      color: var(--pure-black) !important;
      border: 1px solid color-mix(in oklch, var(--blue), white 50%);
      border-radius: 0.25rlh;
    }
  }

  :deep(.select-indicator) {
    margin-top: 0.7rem;
  }

  :deep(label:has(input[type="checkbox"])) {
    flex-direction: column-reverse !important;
    align-items: flex-start !important;
    justify-content: flex-start;
    text-align: left;

  }

  &:deep(input[type="checkbox"]) {
    width: auto;
  }

  /* Add red dot after first child of required labels (non-checkbox controls) */
  :deep(label[data-required="true"]:not(:has(input[type="checkbox"]))) > :first-child::after {
    content: '';
    display: inline-block;
    width: 0.6rem;
    height: 0.6rem;
    background-color: var(--pure-red);
    border-radius: 50%;
    vertical-align: middle;
  }

  /* Add red dot after first child of required labels */
  :deep(label[data-required="true"]:has(input[type="checkbox"])) > :nth-child(2)::after {
    content: '';
    display: inline-block;
    width: 0.6rem;
    height: 0.6rem;
    background-color: var(--pure-red);
    border-radius: 50%;
    vertical-align: middle;
  }
  /* UI Regress End */
}
</style>
