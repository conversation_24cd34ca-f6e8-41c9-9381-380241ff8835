<template>
  <dialog
    title="Email Report"
    id="email-report-dialog"
    anchor="email-report-trigger"
    popover
    @toggle="onToggle">

    <input-button @click="onClose">
      <i class="fas fa-xmark"></i>
    </input-button>

    <div class="v-space"></div>

    <form @submit.prevent="handleSend" v-if="!sendResults.length">
      <InputText v-model="form.to" :disabled="form.emailCustomers">
        Email to
      </InputText>
      <InputCheckbox v-model="form.emailCustomers">
        Send to customers
      </InputCheckbox>

      <div class="v-space"></div>

      <InputText v-model="form.replyTo" :disabled="form.useCompanyEmailAddresses">
        Reply to
      </InputText>
      <InputCheckbox v-model="form.useCompanyEmailAddresses">
        Use company email addresses
      </InputCheckbox>

      <div class="v-space"></div>

      <InputText v-model="form.subject">
        Subject
      </InputText>
      <InputTextarea v-model="form.body">
        Body
      </InputTextarea>

      <div class="v-space"></div>

      <input-button
        type="submit"
        data-variant="blue"
        :disabled="!canSend">
        <template v-if="isSending"><i class="fas fa-spinner-third fa-spin"></i></template>
        <template v-else><i class="fas fa-circle-arrow-up"></i></template>
      </input-button>
    </form>

    <EmailSuccess
      v-if="sendResults.length"
      :results="sendResults" />

  </dialog>
</template>

<script>
import { get } from 'lodash-es';
import { mapActions } from 'vuex';
import InputCheckbox from '@/tower/liens/inputs/Checkbox.vue';
import InputText from '@/tower/liens/inputs/Text.vue';
import InputTextarea from '@/tower/liens/inputs/Textarea.vue';
import InputButton from '@/tower/liens/inputs/Button.vue';
import EmailSuccess from './EmailSuccess.vue';
import { EVENT_ERROR } from '@/config';

export default {
  name: 'EmailReportModal',

  components: {
    InputCheckbox,
    InputText,
    InputTextarea,
    InputButton,
    EmailSuccess
  },

  props: {
    report: { type: Object, required: true },
    parameters: { type: Array, default: () => [] }
  },

  data () {
    return {
      form: {
        to: '',
        replyTo: '',
        subject: '',
        body: '',
        emailCustomers: false,
        useCompanyEmailAddresses: false
      },

      recipientGroup: 'anyone',
      isSending: false,
      sendResults: []
    };
  },

  computed: {
    canSend () {
      if (this.isSending) return false;

      return (this.form.emailCustomers || this.form.to) &&
        this.form.replyTo &&
        this.form.subject &&
        this.form.body;
    }
  },

  methods: {
    ...mapActions([
      'GetEmailDefaults',
      'EmailReport'
    ]),

    async fetchDefaults () {
      try {
        this.$store.dispatch('REPORT__GetEmailDefaults', {
          key: this.report.Key,
          success: response => {
            this.form.to = response.To;
            this.form.replyTo = response.ReplyTo;
            this.form.subject = response.Subject;
            this.form.body = response.Body;
          }
        });
      } catch (e) {
        console.error(e);
      }
    },

    async handleSend () {
      this.isSending = true;
      this.sendResults = [];

      const reducedParameters = this.parameters.reduce((acc, prop) => {
        acc[prop.Name] = prop.Value === false ? 0 : prop.Value === true ? 1 : prop.Value;
        return acc;
      }, {});

      try {
        this.$store.dispatch('REPORT__EmailReport', {
          key: this.report.Key,
          to: this.form.emailCustomers ? '' : this.form.to,
          replyTo: this.form.useCompanyEmailAddresses ? '' : this.form.replyTo,
          subject: this.form.subject,
          body: this.form.body,
          emailCustomers: this.form.emailCustomers,
          useCompanyEmailAddresses: this.form.useCompanyEmailAddresses,
          parameters: reducedParameters,
          success: response => {
            if (Array.isArray(response)) {
              this.sendResults = response;
            } else {
              this.reset();
            }
          }
        });
      } catch (error) {
        this.$hub.$emit(EVENT_ERROR, get(error, 'response.data.message') || 'Failed to send email.');
      } finally {
        this.isSending = false;
      }
    },

    onClose () {
      document.querySelector('#email-report-dialog').hidePopover();
    },

    onToggle (event) {
      if (event.newState === 'open') {
        this.fetchDefaults();
      } else {
        this.reset();
      }
    },

    reset () {
      this.form = {
        to: '',
        replyTo: '',
        subject: '',
        body: '',
        emailCustomers: false,
        useCompanyEmailAddresses: false
      };
      this.isSending = false;
      this.sendResults = [];
    }
  }
};
</script>

<style scoped>
#email-report-dialog {
  padding: 1rem;
  width: 55ch;
  max-width: calc(100vw - 2rem);
  max-height: calc(100dvh - 2rem);
  overflow-y: auto;
}

form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
</style>
