import { ref, computed } from 'vue';
import { parameterModel } from '@/tower/reports/parameterModel.js';

export function useReportParameters() {
  const parameters = ref([]);
  const temporaryValues = ref({});

  const visibleParameters = computed(() => {
    return parameters.value.filter(parameter => isControlVisible(parameter));
  });

  const transformedParameters = computed(() => {
    const parameterObject = {};

    parameters.value.forEach(parameter => {
      parameterObject[parameter.Name] = parameter.Value;
    });

    return parameterObject;
  });

  const canViewReport = computed(() => {
    if (!parameters.value.length) return false;

    // Todo: Unable to check checkbox in Safari
    const remainers = parameters.value.filter(parameter => {
      return parameter.Required &&
        !['InputCheckbox', 'InputSelect'].includes(parameter.Control) &&
        !parameter.Value;
    });

    return !remainers.length;
  });

  function hasParameter(name) {
    return getParameter(name) !== false;
  }

  function isControlVisible(parameter) {
    if (!parameter.IsVisible) return false;
    if (parameter.Control !== 'InputSelect') return true;

    const options = parameter.Options || [];

    // For select controls, check if there is more than 1 real option
    // (excluding the "All" option that gets added automatically)
    const realOptions = options.filter(option => {
      const keyAlias = parameter.KeyAlias || 'Key';
      return String(option[keyAlias]) !== '-1';
    });

    return realOptions.length > 1;
  }

  function isRequiredIndicatorVisible(parameter) {
    // Checkbox parameters are always optional
    if (parameter.Control === 'InputCheckbox') return false;

    return isControlVisible(parameter) && parameter.Required;
  }

  function getParameter(name) {
    const target = parameters.value.find(p => p.Name === name);
    return target || false;
  }

  function setParameter(name, value) {
    const target = getParameter(name);
    if (target) {
      target.Value = value;
    }
    return target;
  }



  function ensureAllOption(parameter) {
    if (parameter.addAllOption === false) return;

    const opts = parameter.Options || [];
    const keyAlias = parameter.KeyAlias || 'Key';
    const valueAlias = parameter.ValueAlias || 'Value';
    const shortAlias = parameter.ShortCodeAlias || 'ShortCode';

    const alreadyThere = opts.some(
      o => String(o[keyAlias]) === '-1'
    );
    if (alreadyThere) return;

    const allRow = {
      [keyAlias]: -1,
      [valueAlias]: 'All',
      [shortAlias]: 'All'
    };

    parameter.Options = [allRow, ...opts];
  }

  function setValue(input) {
    setTemporaryValue(input);

    const target = parameters.value.find(p => p.Name === input.id);
    if (target) {
      target.Value = input.value;
    }
  }

  function setTemporaryValue(input) {
    temporaryValues.value[input.id] = input.value;
  }

  function hydrateParameters(serverParameters, vm) {
    return serverParameters.map(parameter => {
      // Generate unique key for Vue
      parameter.VueKey = `param_${Math.random().toString(36).substr(2, 9)}`;

      // Convert Required to boolean
      parameter.Required = ['true', '1', true, 1].includes(parameter.Required);

      // Set parameter model properties
      const model = parameterModel[parameter.Name] || {};
      parameter.Label = model.label || '';
      parameter.Control = model.control || '';
      parameter.addAllOption = model.addAllOption !== undefined ? model.addAllOption : true;
      parameter.Action = model.action || '';
      parameter.KeyAlias = model.keyAlias || '';
      parameter.ValueAlias = model.valueAlias || '';
      parameter.ShortCodeAlias = model.shortCodeAlias || '';
      parameter.EmptyOption = model.emptyOption || '';
      parameter.IsVisible = model.isVisible !== undefined ? model.isVisible : true;

      // Handle special case for PREVIOUS_RUN_DATE
      if (parameter.Name === 'PREVIOUS_RUN_DATE') {
        const parseOptions = model.parseOptions;
        if (parseOptions && typeof parseOptions === 'function') {
          const options = parseOptions(parameter.Value);
          parameter.Options = options;
          if (options.length > 0) {
            parameter.Value = options[0].Key;
          }
        } else {
          parameter.Options = [];
        }
        ensureAllOption(parameter);
        return parameter;
      }

      // Set parameter value
      const temporaryValue = temporaryValues.value[parameter.Name] || '';
      const defaultValue = model.value !== undefined ? model.value : '';

      // For date parameters, use default value if server returned empty
      if (['BEGIN_DATE_TIME', 'END_DATE_TIME'].includes(parameter.Name) && !parameter.Value) {
        parameter.Value = temporaryValue || defaultValue;
      } else {
        parameter.Value = temporaryValue || parameter.Value || defaultValue;
      }

      // Ensure configured default values are applied (especially for 0 values)
      if (model.value !== undefined && (parameter.Value === '' || parameter.Value === null || parameter.Value === undefined)) {
        parameter.Value = model.value;
      }

      // Handle checkbox values
      if (parameter.Control === 'InputCheckbox') {
        parameter.Value = ['true', '1', true, 1].includes(parameter.Value);
      }

      // Handle parameter actions (API calls)
      if (parameter.Action && vm && vm[parameter.Action]) {
        vm[parameter.Action]({
          callback: response => {
            parameter.Options = response;
            // Ensure default value is preserved after loading options
            if (model.value !== undefined && (parameter.Value === '' || parameter.Value === null || parameter.Value === undefined)) {
              parameter.Value = model.value;
            }
            ensureAllOption(parameter);
          }
        });
      }

      // Fallback for parameters that already came with Options
      ensureAllOption(parameter);
      return parameter;
    });
  }

  function clearParameters() {
    parameters.value = [];
    temporaryValues.value = {};
  }

  return {
    parameters,
    temporaryValues,
    visibleParameters,
    transformedParameters,
    canViewReport,
    hasParameter,
    isControlVisible,
    isRequiredIndicatorVisible,
    getParameter,
    setParameter,
    setValue,
    setTemporaryValue,
    hydrateParameters,
    clearParameters
  };
}
