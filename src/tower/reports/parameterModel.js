import {
  createTextParameter,
  createDateParameter,
  createCheckboxParameter,
  createSelectParameter,
  createSelectValueParameter,
  createSimpleSelectParameter,
  createMonthParameter,
  createYearParameter,
  createCustomerParameter,
  createCustomerNameParameter,
  createDriverParameter,
  createTruckParameter,
  createEmployeeParameter
} from './parameterFactories.js';

export const parameterModel = {
  INVOICE_NUM: createTextParameter('Invoice Number'),

  BEGIN_DATE_TIME: createDateParameter('Start Date'),

  END_DATE_TIME: createDateParameter('End Date'),

  CUSTOMER_KEY: createCustomerParameter(),

  CUSTOMER_NAME: createCustomerNameParameter(),

  BILLING_CUSTOMER: createSelectParameter('Customer Source', 'customerSources', {
    addAllOption: false,
    value: 0,
    emptyOption: false
  }),

  CUSTOMER_TYPE: createSelectParameter('Customer Type', 'CUSTOMER__getTypes'),

  CUSTTYPE_NAME: createSelectValueParameter('Customer Type', 'CUSTOMER__getTypes'),

  LOT_KEY: createSelectParameter('Lot', 'TOPSCOMPANY__getLots'),

  SUBTERMINAL_KEY: createSelectParameter('Company', 'CALL__getSubterminals'),

  CALL_NUM: createTextParameter('Call Number'),

  CALLS_NUMS: createTextParameter('Call Numbers', {
    placeholder: 'e.g. 123, 456, 789'
  }),

  DRIVER_KEY: createDriverParameter(),

  TRUCK_KEY: createTruckParameter(),

  PO_REQD: createCheckboxParameter('P.O. Number Required'),

  RO_REQD: createCheckboxParameter('R.O. Number Required'),

  VIN_REQD: createCheckboxParameter('VIN Required'),

  ODOMETER_REQD: createCheckboxParameter('Odometer Required'),

  HOLD_REASON_TYPE: createSelectParameter('Hold Reason', 'HOLD__getReasons'),

  TOW_CLASS: createSelectParameter('Tow Class', 'TOWTYPE__getClasses'),

  COMPLETION_REVENUE_DATE: createCheckboxParameter('Based on Completion Date (Otherwise, Revenue Date)'),

  DRIVER_STATUS: createSelectParameter('Driver Status', 'TOPSCOMPANY__getDriverStatuses'),

  EMP_KEY: createEmployeeParameter(),

  CERTIFICATION_NUM: createTextParameter('Certification Number'),

  EMPLOYEE_TYPE: createSelectParameter('Employee Type', 'TOPSCOMPANY__getEmployeeTypes'),

  JUST_DATE: createDateParameter('Start Date'),

  REVENUE_RECONCILIATION_DATE: createCheckboxParameter('Based on Revenue Date (Otherwise, Reconciliation Date)'),

  INVOICES_NUMS: createTextParameter('Invoice Numbers', {
    placeholder: 'e.g. 123, 456, 789'
  }),

  BARCODE_NUM: createTextParameter('Bar Code Number'),

  BARCODES_NUMS: createTextParameter('Bar Code Numbers', {
    placeholder: 'e.g. 123, 456, 789'
  }),

  REASON_TYPE: createSelectParameter('Reason', 'CALL__getReasons'),

  MONTH: createMonthParameter(),

  YEAR: createYearParameter(),

  CONFIRMED_LAST_30: createCheckboxParameter('Only Show Unconfirmed Calls and Calls Confirmed in Last 30 Days'),

  SERVICE_KEY: createSelectParameter('Service', 'TOPSCOMPANY__getLocationServices'),

  INCLUDE_CANCELED_CALLS: createCheckboxParameter('Include Canceled Calls'),

  BAAN_NUM: createCheckboxParameter('Group Companies Based on Accounting Number'),

  // Special case parameter that needs custom logic
  PREVIOUS_RUN_DATE: createSimpleSelectParameter('Previous Run Date', {
    addAllOption: false,
    parseOptions: (value) => {
      // Parse comma-separated dates into select options
      if (!value || typeof value !== 'string') return [];
      return value.split(',').map(date => ({
        Key: date.trim(),
        Value: date.trim(),
        ShortCode: date.trim()
      }));
    }
  }),

  PAYMENT_TYPE: createSelectParameter('Payment Type', 'PAYMENT__getPaymentTypes'),

  // @TODO
  // SELLERTYPE_KEY
  // SELLER_KEY
  // BIDDER_KEY
  // LOAD_NUMS
  // VEHICLE_YR
  // VEHICLE_MAKE
  // VEHICLE_MODEL
  // SWIPE_LICENSE
  // STATE_CODE
  // DMV_ORDER
  // TICKET_PREFIX
  // TICKET_START
  // TICKET_END
  // CONTACT_ORDER_NUM
  // GENERIC_TEXT_
  // USER_KEY
  // EMAIL_TO
};
