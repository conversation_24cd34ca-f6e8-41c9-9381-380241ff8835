<template>
  <div id="checkout">
    <app-titlebar title="Checkout"></app-titlebar>

    <section class="tools">
      <p class="control">
        <app-select
          v-model="$store.state.checkout.gridKey"
          :options="availableGrids"
          :emptyOption="false"
          placeholder="Find calls —"
          keyAlias="key"
          valueAlias="value"
          size="large"
          :autofocus="true">
        </app-select>
      </p>

      <AppTip v-if="$store.state.checkout.gridKey">
        <p class="small">{{ availableGrids.find(grid => grid.key === $store.state.checkout.gridKey).alias }}</p>
      </AppTip>
      <AppTip v-else>
        <p class="small">Make a selection to find calls.</p>
      </AppTip>

      <div class="_supplements">
        <transition name="flip" mode="out-in">
          <app-shortcode
            v-model="$store.state.checkout.driverKey"
            v-if="driversVisible"
            :options="drivers"
            keyAlias="Key"
            valueAlias="Value"
            shortCodeAlias="ShortCode"
            :key="`driver-${$store.state.checkout.gridKey}`">
            Driver
          </app-shortcode>

          <app-shortcode
            v-model="$store.state.checkout.employeeKey"
            v-if="employeesVisible"
            :options="employees"
            keyAlias="Key"
            valueAlias="Value"
            shortCodeAlias="ShortCode"
            :key="`employee-${$store.state.checkout.gridKey}`">
            Employee
          </app-shortcode>
        </transition>
      </div>

      <div class="_flexer"></div>

      <transition name="drop-up">
        <div class="_total" v-if="counter.visible">
          <div class="_label">Amount</div>
          <div class="_amount">{{ counter.amount | usd }}</div>
        </div>
      </transition>
    </section>

    <section class="records">
      <grid
        :grid="activeGridSettings"
        :data="gridData"
        :refreshedAt="refreshedAt"
        :config="$viewConfig"
        :queue="$store.state.checkout.queuedCalls"
        :show-loader="showLoader"
        @refresh="refresh"
        @openRecord="openRecord"
        @exportData="exportData"
        @save="save"
        @toggle-reconcile="toggleReconcile"
        @toggle-confirm="toggleConfirm">
      </grid>
    </section>

    <app-footerbar>
      <app-button @click="cancel" type="default" :disabled="!$store.state.checkout.queuedCalls.length">Cancel</app-button>
      <app-button @click="saveCalls" type="primary" :disabled="!$store.state.checkout.queuedCalls.length">Save</app-button>
    </app-footerbar>
  </div>
</template>

<script>import { find, clone, castArray, sumBy, replace, reject } from 'lodash-es';


import { mapActions } from 'vuex';
import { GRID_KEY, EVENT_SUCCESS } from '@/config.js';
import Grid from '@/components/features/CheckoutGrid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';

export default {
  name: 'checkout-view',

  extends: RecordsView,

  components: { Grid },

  data () {
    return {
      viewConfig: {
        uuid: 'checkout-screen',
        noun: 'Checkout',
        requireData: true,
        recordKeyName: 'lPaymentKey',
        readRouteKey: 'lCallKey',
        readRouteName: 'Call'
      },

      counter: {
        amount: 0,
        visible: false
      },

      now: '',
      drivers: [],
      employees: [],
      guardQueuedCalls: true,
      availableGrids: [
        {
          key: GRID_KEY.checkoutDriver,
          value: `Drivers' unreconciled tows and retows`,
          alias: 'Checkout calls that have unreconciled tows and retows',
          config: {
            recordKeyName: 'lCallKey',
            readRouteKey: null
          }
        },
        {
          key: GRID_KEY.checkoutEmployee,
          value: `Employees' unreconciled payments received`,
          alias: 'Checkout calls that have unreconciled payments received',
          config: {
            recordKeyName: 'lPaymentKey',
            readRouteKey: 'lCallKey'
          }
        },
        {
          key: GRID_KEY.checkoutCalls,
          value: 'All unreconciled calls',
          alias: 'Checkout calls that are in an unreconciled state',
          config: {
            recordKeyName: 'lCallKey',
            readRouteKey: null
          }
        },
        {
          key: GRID_KEY.checkoutReleases,
          value: 'All unconfirmed calls',
          alias: 'Checkout calls that are in an unconfirmed state',
          config: {
            recordKeyName: 'lCallKey',
            readRouteKey: null
          }
        }
      ]
    };
  },

  computed: {
    activeGridSettings () {
      if (!this.$store.state.checkout.gridKey) return {};

      return find(this.RECORDS__settings.Grids, ['Key', this.$store.state.checkout.gridKey]) || {};
    },

    activeViewSettings () {
      const view = clone(this.RECORDS__settings);
      view.Grids = this.activeGridSettings;

      return view;
    },

    gridData () {
      return this.viewData.length ? castArray(this.viewData) : [];
    },

    canLoadData () {
      return this.$store.state.checkout.gridKey > 0;
    },

    driverGridIsActive () {
      return this.$store.state.checkout.gridKey === GRID_KEY.checkoutDriver;
    },

    employeeGridIsActive () {
      return this.$store.state.checkout.gridKey === GRID_KEY.checkoutEmployee;
    },

    releasesGridIsActive () {
      return this.$store.state.checkout.gridKey === GRID_KEY.checkoutReleases;
    },

    driversVisible () {
      return this.driverGridIsActive;
    },

    employeesVisible () {
      return this.employeeGridIsActive || this.releasesGridIsActive;
    },

    $viewConfig () {
      const viewConfig = this.viewConfig;
      const grid = find(this.availableGrids, ['key', this.$store.state.checkout.gridKey]);

      if (grid) {
        viewConfig.recordKeyName = grid.config.recordKeyName;
        viewConfig.readRouteKey = grid.config.readRouteKey;
      }

      return viewConfig;
    }
  },

  watch: {
    '$store.state.checkout.gridKey' () {
      this.loadData();
    },

    '$store.state.checkout.driverKey' () {
      this.showLoader = true;
      this.loadData();
    },

    '$store.state.checkout.employeeKey' () {
      this.showLoader = true;
      this.loadData();
    }
  },

  methods: {
    ...mapActions([
      'TOPSCALL__confirm',
      'RECORDS__getSettings',
      'TOPSCALL__reconcileTow',
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getEmployees',
      'TOPSCALL__reconcilePayment'
    ]),

    // Override the refresh method from RecordsView to preserve queuedCalls
    refresh () {
      // Set guardQueuedCalls to true before loading data
      const wasGuarded = this.guardQueuedCalls;
      this.guardQueuedCalls = true;

      // Call the original loadData method
      this.loadData();

      // Only reset guardQueuedCalls if it wasn't already set
      if (!wasGuarded) {
        setTimeout(() => {
          this.guardQueuedCalls = false;
        }, 1000);
      }
    },

    loadData () {
      if (!this.guardQueuedCalls) {
        this.$store.state.checkout.queuedCalls = [];
      }

      if (!this.canLoadData) {
        this.viewData = {};
        this.getSum();
        return;
      }

      this.$store.dispatch('RECORDS__getData', {
        noun: this.viewConfig.noun,
        data: this.activeViewSettings,
        driverKey: this.$store.state.checkout.driverKey,
        employeeKey: this.$store.state.checkout.employeeKey,
        gridKey: this.$store.state.checkout.gridKey,
        callback: response => {
          this.showLoader = false;
          this.viewData = response;
          this.getSum();
        }
      });
    },

    getSum () {
      const sum = sumBy(this.gridData, record => {
        let value = record.tcPaymentAmount;

        value = replace(value, '$', '');
        value = replace(value, ',', '');

        return Number(value);
      });

      this.counter.visible = !!sum;

      this.$gsap.to(this.counter, {
        duration: 0.6,
        amount: sum
      });
    },

    cancel () {
      this.$store.state.checkout.queuedCalls = [];
    },

    shouldConfirmCall (call) {
      return call.task === 'confirm';
    },

    shouldReconcileTow (call) {
      return call.task === 'reconcile' && this.$store.state.checkout.gridKey === GRID_KEY.checkoutDriver;
    },

    shouldReconcilePayment (call) {
      return call.task === 'reconcile' && this.$store.state.checkout.gridKey === GRID_KEY.checkoutEmployee;
    },

    async saveCalls () {
      for (const call of this.$store.state.checkout.queuedCalls) {
        try {
          if (this.shouldConfirmCall(call)) {
            await this.confirmCall(call.key);
            this.$store.state.checkout.queuedCalls = reject(
              this.$store.state.checkout.queuedCalls,
              { paymentKey: call.paymentKey, task: 'confirm' }
            );
          } else if (this.shouldReconcileTow(call)) {
            await this.reconcileTow({
              key: call.key,
              retow: call.retow,
              bothTowAndRetow: call.retow
            });
            this.$store.state.checkout.queuedCalls = reject(
              this.$store.state.checkout.queuedCalls,
              { paymentKey: call.paymentKey, task: 'reconcile' }
            );
          } else if (this.shouldReconcilePayment(call)) {
            await this.reconcilePayment(call);
            this.$store.state.checkout.queuedCalls = reject(
              this.$store.state.checkout.queuedCalls,
              { paymentKey: call.paymentKey, task: 'reconcile' }
            );
          } else {
            await this.reconcileTow({
              key: call.key,
              retow: call.retow,
              bothTowAndRetow: call.retow
            });
            this.$store.state.checkout.queuedCalls = reject(
              this.$store.state.checkout.queuedCalls,
              { paymentKey: call.paymentKey, task: 'reconcile' }
            );
          }
        } catch (error) {
          console.error('Error processing call:', error);
        }
      }

      this.$hub.$emit(EVENT_SUCCESS, 'All actionable calls have been saved.');

      // Set guardQueuedCalls to true before refreshing to prevent queuedCalls from being cleared
      this.guardQueuedCalls = true;
      this.refresh();

      // Reset guardQueuedCalls after a delay to allow the refresh to complete
      setTimeout(() => {
        this.guardQueuedCalls = false;
      }, 1000);
    },

    async confirmCall (callKey) {
      return new Promise((resolve, reject) => {
        this.TOPSCALL__confirm({
          callKey,
          success: () => resolve(),
          fail: response => {
            this.$confirm(response.Message, 'Unable to confirm', {
              confirmButtonText: 'Continue',
              cancelButtonText: 'Stop',
              type: 'warning'
            })
              .then(resolve)
              .catch(reject);
          }
        });
      });
    },

    async reconcileTow (call) {
      return new Promise((resolve, reject) => {
        this.TOPSCALL__reconcileTow({
          callKey: call.key,
          retow: call.retow,
          bothTowAndRetow: call.bothTowAndRetow,
          success: () => resolve(),
          fail: response => {
            this.$confirm(response.Message, 'Unable to reconcile tow', {
              confirmButtonText: 'Continue',
              cancelButtonText: 'Stop',
              type: 'warning'
            })
              .then(resolve)
              .catch(reject);
          }
        });
      });
    },

    async reconcilePayment (call) {
      return new Promise((resolve, reject) => {
        this.TOPSCALL__reconcilePayment({
          callKey: call.key,
          paymentKey: call.paymentKey,
          success: () => resolve(),
          fail: response => {
            this.$confirm(response.Message, 'Unable to reconcile payment', {
              confirmButtonText: 'Continue',
              cancelButtonText: 'Stop',
              type: 'warning'
            })
              .then(resolve)
              .catch(reject);
          }
        });
      });
    },

    toggleConfirm (call) {
      const calls = castArray(call);

      // Set guardQueuedCalls to true to prevent queuedCalls from being cleared
      const wasGuarded = this.guardQueuedCalls;
      this.guardQueuedCalls = true;

      calls.forEach(call => {
        if (find(this.$store.state.checkout.queuedCalls, { paymentKey: call.paymentKey, task: 'confirm' })) {
          this.removeConfirmFromQueue(call);
        } else {
          this.addConfirmToQueue(call);
        }
      });

      // Only reset guardQueuedCalls if it wasn't already set
      if (!wasGuarded) {
        setTimeout(() => {
          this.guardQueuedCalls = false;
        }, 1000);
      }
    },

    addConfirmToQueue (call) {
      this.$store.state.checkout.queuedCalls.push(call);
      this.removeReconcileFromQueue(call);
    },

    removeConfirmFromQueue (call) {
      this.$store.state.checkout.queuedCalls = reject(
        this.$store.state.checkout.queuedCalls,
        { paymentKey: call.paymentKey, task: 'confirm' }
      );
    },

    toggleReconcile (call) {
      const calls = castArray(call);

      // Set guardQueuedCalls to true to prevent queuedCalls from being cleared
      const wasGuarded = this.guardQueuedCalls;
      this.guardQueuedCalls = true;

      calls.forEach(call => {
        if (find(this.$store.state.checkout.queuedCalls, {
          paymentKey: call.paymentKey,
          task: 'reconcile'
        })) {
          this.removeReconcileFromQueue(call);
        } else {
          this.addReconcileToQueue(call);
        }
      });

      // Only reset guardQueuedCalls if it wasn't already set
      if (!wasGuarded) {
        setTimeout(() => {
          this.guardQueuedCalls = false;
        }, 1000);
      }
    },

    addReconcileToQueue (call) {
      this.$store.state.checkout.queuedCalls.push(call);
      this.removeConfirmFromQueue(call);
    },

    removeReconcileFromQueue (call) {
      this.$store.state.checkout.queuedCalls = reject(
        this.$store.state.checkout.queuedCalls,
        { paymentKey: call.paymentKey, task: 'reconcile' }
      );
    }
  },

  mounted () {
    this.getSettings();

    setTimeout(() => {
      this.guardQueuedCalls = false;
    }, 1000);

    this.TOPSCOMPANY__getDrivers({
      callback: response => {
        const drivers = response.filter(driver => driver.Active);
        drivers.unshift({
          Key: '',
          Value: 'None',
          ShortCode: '',
          Active: true
        });
        this.drivers = drivers;
      }
    });

    this.TOPSCOMPANY__getEmployees({
      callback: response => {
        const employees = response.filter(employee => employee.Active);
        employees.unshift({
          Key: '',
          Value: 'None',
          ShortCode: '',
          Active: true
        });
        this.employees = employees;
      }
    });

    this.$store.state.addCall.shouldStayOnSave = false;
  }
};
</script>
