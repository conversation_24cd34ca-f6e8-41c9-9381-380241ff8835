import { ref, computed } from 'vue'
import axios from 'axios'

// Cache for API responses to avoid duplicate requests
const schemaCache = new Map()

export function useAdditionalServices() {
  const isLoading = ref(false)
  const error = ref(null)
  const schemas = ref([])

  const categories = computed(() => {
    const uniqueCategories = [...new Set(schemas.value.map(schema => schema.serviceCategory))]
    return uniqueCategories.sort()
  })

  const getServicesByCategory = computed(() => {
    return (category) => {
      return schemas.value
        .filter(schema => schema.serviceCategory === category)
        .sort((a, b) => a.serviceName.localeCompare(b.serviceName))
    }
  })

  const getServiceSchema = computed(() => {
    return (category, serviceName) => {
      return schemas.value.find(schema => 
        schema.serviceCategory === category && 
        schema.serviceName === serviceName
      )
    }
  })

  const fetchSchemas = async (fetchDataURL) => {
    if (!fetchDataURL) {
      error.value = 'No fetch URL provided'
      return false
    }

    // Check cache first
    if (schemaCache.has(fetchDataURL)) {
      schemas.value = schemaCache.get(fetchDataURL)
      return true
    }

    isLoading.value = true
    error.value = null

    try {
      const response = await axios.get(fetchDataURL)
      
      if (response.data && response.data.ClientViewSchemas) {
        schemas.value = response.data.ClientViewSchemas
        // Cache the result
        schemaCache.set(fetchDataURL, response.data.ClientViewSchemas)
        return true
      } else {
        error.value = 'Invalid response format'
        return false
      }
    } catch (err) {
      error.value = err.response?.data?.message || err.message || 'Failed to fetch service schemas'
      return false
    } finally {
      isLoading.value = false
    }
  }

  const reset = () => {
    schemas.value = []
    error.value = null
    isLoading.value = false
  }

  return {
    isLoading,
    error,
    schemas,
    categories,
    getServicesByCategory,
    getServiceSchema,
    fetchSchemas,
    reset
  }
}
