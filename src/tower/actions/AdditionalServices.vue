<template>
  <div class="additional-services-inline" v-if="isAdditionalServicesAction">
    <!-- Loading State -->
    <div v-if="isLoading" class="columns item">
      <div class="column _label is-one-third">
        <label>Loading Services...</label>
      </div>
      <div class="item__control column">
        <i class="fas fa-spinner-third fa-spin"></i>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="columns item">
      <div class="column _label is-one-third">
        <label>Service Error</label>
      </div>
      <div class="item__control column">
        <app-tip role="alert" aria-live="polite">
          <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
          {{ error }}
        </app-tip>
        <app-button @click="retryFetch" :disabled="isLoading">
          Retry
        </app-button>
      </div>
    </div>

    <!-- Category Selection -->
    <div v-else-if="categories.length" class="columns item">
      <div class="column _label is-one-third">
        <label>
          <span class="required-indicator"><i class="fas fa-circle-small"></i></span>
          Category
        </label>
      </div>
      <div class="item__control column">
        <app-select
          id="serviceCategory"
          v-model="selectedCategory"
          :options="categoryOptions"
          aria-label="Select service category"
          @change="onCategoryChange">
        </app-select>
      </div>
    </div>

    <!-- Service Selection -->
    <div v-if="selectedCategory && serviceOptions.length" class="columns item">
      <div class="column _label is-one-third">
        <label>
          <span class="required-indicator"><i class="fas fa-circle-small"></i></span>
          Service
        </label>
      </div>
      <div class="item__control column">
        <app-select
          id="serviceName"
          v-model="selectedService"
          :options="serviceOptions"
          aria-label="Select service type"
          @change="onServiceChange">
        </app-select>
      </div>
    </div>

    <!-- Dynamic Service Fields -->
    <template v-if="selectedService && currentServiceSchema">
      <div
        v-for="field in currentServiceSchema.fields"
        :key="field.name"
        class="columns item">
        <div class="column _label is-one-third">
          <label>
            <span v-if="field.isRequired" class="required-indicator"><i class="fas fa-circle-small"></i></span>
            {{ field.name }}
          </label>
        </div>
        <div class="item__control column">
          <component
            :is="makeFieldControl(field)"
            :id="field.name"
            v-model="fieldValues[field.name]"
            :options="transformFieldOptions(field.options)"
            :aria-label="`Enter ${field.name}`"
            @change="onFieldChange(field.name, $event)">
          </component>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { useAdditionalServices } from './useAdditionalServices.js'

export default defineComponent({
  name: 'AdditionalServicesInline',

  props: {
    action: {
      type: Object,
      required: true
    }
  },

  emits: ['field-change'],

  setup(props, { emit }) {
    const {
      isLoading,
      error,
      categories,
      getServicesByCategory,
      getServiceSchema,
      fetchSchemas,
      reset
    } = useAdditionalServices()

    const selectedCategory = ref('')
    const selectedService = ref('')
    const fieldValues = ref({})

    // Check if this is an additional services action
    const isAdditionalServicesAction = computed(() => {
      return props.action &&
        props.action.ResponseTag === 'Job' &&
        props.action.ResponseValue === 'AdditionalServices' &&
        props.action.FetchDataURL;
    })

    // Transform categories for app-select
    const categoryOptions = computed(() => {
      return categories.value.map(category => ({
        value: category,
        description: category
      }))
    })

    // Get services for selected category
    const serviceOptions = computed(() => {
      if (!selectedCategory.value) return []

      const services = getServicesByCategory.value(selectedCategory.value)
      return services.map(service => ({
        value: service.serviceName,
        description: service.serviceName
      }))
    })

    const currentServiceSchema = computed(() => {
      if (!selectedCategory.value || !selectedService.value) return null
      return getServiceSchema.value(selectedCategory.value, selectedService.value)
    })

    // Determine field control type based on field schema
    const makeFieldControl = (field) => {
      if (field.options && field.options.length > 0) {
        return 'app-select'
      }

      switch (field.type) {
        case 'string':
          return 'app-text'
        case 'integer':
          return 'app-text'
        case 'decimal':
          return 'app-text'
        default:
          return 'app-text'
      }
    }

    // Transform field options for app-select
    const transformFieldOptions = (options = []) => {
      return options.map(option => ({
        value: option.value || option,
        description: option.description || option
      }))
    }

    // Event handlers
    const onCategoryChange = () => {
      selectedService.value = ''
      fieldValues.value = {}
      emitFieldChanges()
    }

    const onServiceChange = () => {
      fieldValues.value = {}
      emitFieldChanges()
    }

    const onFieldChange = () => {
      emitFieldChanges()
    }

    // Emit field changes to parent
    const emitFieldChanges = () => {
      if (!selectedCategory.value || !selectedService.value) {
        emit('field-change', null)
        return
      }

      const payload = {
        serviceCategory: selectedCategory.value,
        serviceName: selectedService.value,
        fields: { ...fieldValues.value },
        schema: currentServiceSchema.value // Include schema for validation
      }

      emit('field-change', payload)
    }

    // Initialize when action changes
    const initializeForAction = async () => {
      if (!isAdditionalServicesAction.value) {
        reset()
        return
      }

      const success = await fetchSchemas(props.action.FetchDataURL)
      if (!success) {
        // Error already stored in error ref by composable
      }
    }

    const retryFetch = () => {
      initializeForAction()
    }

    // Watch for action changes
    watch(() => props.action, () => {
      selectedCategory.value = ''
      selectedService.value = ''
      fieldValues.value = {}
      initializeForAction()
    }, { immediate: true })

    // Initialize field values when service schema changes
    watch(currentServiceSchema, (newSchema) => {
      if (newSchema) {
        const newFieldValues = {}
        newSchema.fields.forEach(field => {
          newFieldValues[field.name] = fieldValues.value[field.name] || ''
        })
        fieldValues.value = newFieldValues
      }
    })

    return {
      isAdditionalServicesAction,
      isLoading,
      error,
      categories,
      selectedCategory,
      selectedService,
      fieldValues,
      categoryOptions,
      serviceOptions,
      currentServiceSchema,
      makeFieldControl,
      transformFieldOptions,
      onCategoryChange,
      onServiceChange,
      onFieldChange,
      retryFetch
    }
  }
})
</script>

<style scoped>
.additional-services-inline {
  /* Inherits styling from parent action item */
}

.error-text {
  color: var(--pure-red);
  font-size: 0.875rem;
  display: block;
  margin-bottom: 0.5rem;
}

.required-indicator {
  color: var(--pure-red);
  margin-right: 0.25rem;
}
</style>
