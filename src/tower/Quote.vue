<template>
  <div id="record-view" data-page="quote" :data-search-mode="searchMode">
    <app-titlebar :title="'Quote · ' + quote.lKey" v-if="!searchMode"></app-titlebar>

    <div class="loader" v-if="loaderVisible"></div>

    <section class="content-section">
      <div class="_liner" v-show="!loaderVisible">
        <main class="sections">
          <section class="pinned-sections" :data-pinned="sectionsArePinned">
            <div class="_liner">
              <form-section
                v-for="(section, name) in pinnedSections"
                :key="name"
                :id="name"
                :title="section.title"
                :fixed="true">
                <component
                  :is="section.componentName"
                  :call="call"
                  :quoteMode="true">
                </component>
              </form-section>
            </div>
          </section>

          <section class="floating-sections" :data-pinned="sectionsArePinned">
            <form-section
              v-for="(section, name) in floatingSections"
              :key="name"
              :id="name"
              :title="section.title"
              :fixed="true">
              <component
                :is="section.componentName"
                :call="section.componentName === 'quote-section' ? quote : call"
                :quoteMode="true"
                :price-repository="priceRepository"
                :focusOnReady="!quote.lKey">
              </component>
            </form-section>
          </section>
        </main>

        <aside class="mini-map">
          <div class="_liner">
            <section class="sketch">
              <template v-if="searchMode">
                <span id="call-section">
                  <app-grid-form context="inline">
                    <div class="columns is-multiline">
                      <div class="column is-12 is-left">
                        <app-text v-model="call.lCallKey">
                          Call Number
                        </app-text>
                      </div>
                      <div class="column is-12 is-left">
                        <app-select v-model="call.lCallStatusTypeKey" :options="callStatuses" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
                          Status
                        </app-select>
                      </div>
                      <div class="column is-12 is-left is-bottom">
                        <app-text v-model="call.tcPrice" id="CAL_tcPrice">
                          Price
                        </app-text>
                      </div>
                    </div>
                  </app-grid-form>
                </span>
              </template>
            </section>

            <ul class="_links">
              <li class="_link"
                v-for="(section, name) in sectionsProxy"
                @click="lookAt(name)"
                :key="name"
                :data-pinned="sectionsArePinned && ['tow', 'vehicle'].includes(name)">
                {{ section.title }}
              </li>
            </ul>
          </div>
        </aside>
      </div>
    </section>

    <app-footerbar v-if="!searchMode">
      <template slot="left">
        <app-button @click="save()" type="primary" v-if="isNewRecord" :disabled="!canSave">
          Save
        </app-button>
        <app-button @click="createCall" type="primary" v-if="!isNewRecord && !quote.lCall">
          Convert to Call
        </app-button>
        <app-button @click="viewCall" type="primary" v-if="!!quote.lCall">
          View Call #{{ quote.lCall }}
        </app-button>
        <app-button @click="duplicate" v-if="!isNewRecord">
          Duplicate
        </app-button>
        <app-button @click="cancel">
          Close
        </app-button>
      </template>

      <app-button @click="shareModal.visible = true" v-if="!isNewRecord">
        Email&hellip;
      </app-button>
      <app-button @click="preview" v-if="!isNewRecord">
        View
      </app-button>
      <app-button @click="deleteRecord" type="danger" v-if="!isNewRecord && !quote.lCall">
        Delete
      </app-button>
    </app-footerbar>

    <app-modal title="Email Quote" :show="shareModal.visible" :pad="false" @close="shareModal.visible = false">
      <app-grid-form context="inline">
        <div class="columns is-multiline">
          <div class="column is-12">
            <app-text v-model="shareModal.to">
              To
            </app-text>
          </div>
          <div class="column is-12">
            <app-text v-model="shareModal.subject">
              Title
            </app-text>
          </div>
          <div class="column is-12">
            <app-text v-model="shareModal.body">
              Message
            </app-text>
          </div>
        </div>
      </app-grid-form>

      <div style="padding: 1rem">
        <app-button type="primary" @click="share" :disabled="!canShare">
          Send
        </app-button>
      </div>
    </app-modal>
  </div>
</template>

<script>import { forEach, includes, set, get, forOwn } from 'lodash-es';


import { mapActions } from 'vuex';
import { EVENT_RESET_CALL_PRICING } from '@/config.js';
import RecordView from '@/components/ancestors/RecordView.vue';
import { callMixin } from '@/mixins/call_mixin.js';

import TowSection from '@/components/call/TowSection.vue';
import LienSection from '@/components/call/LienSection.vue';
import NotesSection from '@/components/call/NotesSection/Index.vue';
import QuoteSection from '@/components/call/QuoteSection.vue';
import PricingSection from '@/components/call/PricingSection/Index.vue';
import VehicleSection from '@/components/call/VehicleSection/VehicleSectionController.vue';
import MileageSection from '@/components/call/MileageSection.vue';
import InventorySection from '@/components/call/InventorySection.vue';
import AccountingSection from '@/components/call/AccountingSection.vue';
import SubterminalSection from '@/components/call/SubterminalSection.vue';
import MiscellaneousSection from '@/components/call/MiscellaneousSection.vue';
import SystemTagsSection from '@/components/call/SystemTagsSection/SystemTagController.vue';

import SearchTowSection from '@/components/call_search/TowSection.vue';
import SearchCallSection from '@/components/call_search/CallSection.vue';
import SearchLienSection from '@/components/call_search/LienSection.vue';
import SearchNotesSection from '@/components/call_search/NotesSection.vue';
import SearchPricingSection from '@/components/call_search/PricingSection.vue';
import SearchVehicleSection from '@/components/call_search/VehicleSection.vue';
import SearchMileageSection from '@/components/call_search/MileageSection.vue';
import SearchInventorySection from '@/components/call_search/InventorySection.vue';
import SearchAccountingSection from '@/components/call_search/AccountingSection.vue';
import SearchSubterminalSection from '@/components/call_search/SubterminalSection.vue';
import SearchMiscellaneousSection from '@/components/call_search/MiscellaneousSection.vue';

export default {
  name: 'quote-view',

  extends: RecordView,

  components: {
    TowSection,
    LienSection,
    NotesSection,
    QuoteSection,
    PricingSection,
    VehicleSection,
    MileageSection,
    InventorySection,
    AccountingSection,
    SubterminalSection,
    MiscellaneousSection,
    SystemTagsSection,

    SearchTowSection,
    SearchCallSection,
    SearchLienSection,
    SearchNotesSection,
    SearchPricingSection,
    SearchVehicleSection,
    SearchMileageSection,
    SearchInventorySection,
    SearchAccountingSection,
    SearchSubterminalSection,
    SearchMiscellaneousSection
  },

  mixins: [callMixin],

  props: {
    searchMode: { type: Boolean, default: false }
  },

  data () {
    return {
      viewConfig: {
        noun: 'Quote',
        recordKeyName: 'lKey',
        readRouteName: 'Quote',
        returnRouteName: 'Quotes',
        addRouteName: 'AddQuote',
        initializeDefaults: false
      },

      subterminals: [],
      callStatuses: [],
      loaderVisible: false,
      sectionsArePinned: false,

      shareModal: {
        visible: false,
        to: '',
        subject: '',
        body: ''
      },

      callSectionDetails: [
        'accounting',
        'lien',
        'towpricing',
        'dispatch',
        'holds'
      ],

      sections: {
        call: {
          title: 'Call',
          componentName: 'search-call-section',
          landingPoint: 'CAL_lReferenceNum',
          enabled: false,
          pinned: false
        },
        subterminal: {
          title: 'Company',
          componentName: this.searchMode ? 'search-subterminal-section' : 'subterminal-section',
          landingPoint: 'CAL_lSubterminalKey',
          enabled: false,
          pinned: false
        },
        quote: {
          title: 'Quote',
          componentName: 'quote-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        tow: {
          title: 'Tow',
          componentName: this.searchMode ? 'search-tow-section' : 'tow-section',
          landingPoint: 'CAL_vc30ContactName',
          enabled: true,
          pinned: true
        },
        system_tags: {
          title: 'System Tags',
          componentName: 'system-tags-section',
          landingPoint: '',
          enabled: !this.searchMode,
          pinned: false
        },
        towpricing: {
          title: 'Pricing',
          componentName: this.searchMode ? 'search-pricing-section' : 'pricing-section',
          landingPoint: 'pricing.AddService',
          enabled: true,
          pinned: false
        },
        vehicle: {
          title: 'Vehicle',
          componentName: this.searchMode ? 'search-vehicle-section' : 'vehicle-section',
          landingPoint: 'CAL_iYear',
          enabled: true,
          pinned: false
        },
        mileage: {
          title: 'Mileage',
          componentName: this.searchMode ? 'search-mileage-section' : 'mileage-section',
          landingPoint: '',
          enabled: true,
          pinned: false
        },
        miscellaneous: {
          title: 'Miscellaneous',
          componentName: this.searchMode ? 'search-miscellaneous-section' : 'miscellaneous-section',
          landingPoint: 'CAL_tPriority',
          enabled: false,
          pinned: false
        },
        cancel: {
          title: 'Cancel',
          componentName: this.searchMode ? 'search-cancel-section' : 'cancel-section',
          landingPoint: 'cancel.add',
          enabled: false,
          pinned: false
        },
        towdispatch: {
          title: 'Dispatches',
          componentName: this.searchMode ? 'search-dispatch-section' : 'dispatch-section',
          landingPoint: '',
          enabled: false,
          pinned: false
        },
        notes: {
          title: 'Notes',
          componentName: this.searchMode ? 'search-notes-section' : 'notes-section',
          landingPoint: 'note.add',
          enabled: false,
          pinned: false
        },
        inventory: {
          title: 'Inventory',
          componentName: this.searchMode ? 'search-inventory-section' : 'inventory-section',
          landingPoint: 'INV_lStorageLotKey',
          enabled: false,
          pinned: false
        },
        towpayment: {
          title: 'Payments',
          componentName: this.searchMode ? 'search-payment-section' : 'payment-section',
          landingPoint: 'payment.add',
          enabled: false,
          pinned: false
        },
        holds: {
          title: 'Holds',
          componentName: this.searchMode ? 'search-holds-section' : 'holds-section',
          landingPoint: 'hold.add',
          enabled: false,
          pinned: false
        },
        inspection: {
          title: 'Inspections',
          componentName: this.searchMode ? 'search-inspections-section' : 'inspections-section',
          landingPoint: 'inspection.add',
          enabled: false,
          pinned: false
        },
        contacts: {
          title: 'Contacts',
          componentName: this.searchMode ? 'search-contact-section' : 'contact-section',
          landingPoint: 'contact.add',
          enabled: false,
          pinned: false
        },
        lien: {
          title: 'Lien',
          componentName: this.searchMode ? 'search-lien-section' : 'lien-section',
          landingPoint: '',
          enabled: false,
          pinned: false
        },
        images: {
          title: 'Images',
          componentName: 'images-section',
          landingPoint: '',
          enabled: false,
          pinned: false
        },
        retow: {
          title: 'Retow',
          componentName: this.searchMode ? 'search-retow-section' : 'retow-section',
          landingPoint: 'RET_vc100Location',
          enabled: false,
          pinned: false
        },
        retowdispatch: {
          title: 'Re-dispatches',
          componentName: 'retow-dispatch-section',
          landingPoint: '',
          enabled: false,
          pinned: false
        },
        accounting: {
          title: 'Accounting',
          componentName: this.searchMode ? 'search-accounting-section' : 'accounting-section',
          landingPoint: 'CAL_vc255AccountingNotes',
          enabled: false,
          pinned: false
        }
      },

      priceRepository: {
        tow: {
          TaxRateOverride: '',
          TaxRate: '',
          DiscountPct: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        },
        sale: {
          TaxRateOverride: '',
          TaxRate: '',
          Total: '',
          TaxTotal: '',
          DiscountTotal: '',
          TaxExempt: false
        }
      },

      quote: {
        lCall: '',
        bActive: true,
        dCreated: '',
        dEmailSent: '',
        lCreatedBy: '',
        lCustomer: '',
        lDeletedBy: '',
        lEmailSentBy: '',
        lKey: '',
        vc100GivenTo: '',
        vc255EmailTo: '',
        vc1000Notes: ''
      },

      call: {
        dETA: '',
        iYear: '',
        ch5Zone: '',
        tcPrice: '',
        vc25VIN: '',
        lMakeKey: '',
        lCallKey: '',
        tPriority: this.searchMode ? 0 : 9,
        lModelKey: '',
        vc20PONum: '',
        vc20RONum: '',
        dCallTaken: '',
        dTagExpiry: '',
        fDiscountPct: 0,
        lTowTypeKey: '',
        ch2StateKey: '',
        bNoCharge: false,
        lCustomerKey: '',
        TowTicketNum: '',
        lBodyTypeKey: '',
        dAppointment: '',
        vc10Odometer: '',
        lColorTypeKey: '',
        lTruckTypeKey: '',
        vc20PoliceNum: '',
        vc30OwnerName: '',
        vc100Location: '',
        lReasonTypeKey: '',
        vc15LicenseNum: '',
        vc20OwnerPhone: '',
        vc10PoliceBeat: '',
        CallTakerNotes: '',
        lSubterminalKey: '',
        vc30ContactName: '',
        dExpirationDate: '',
        bOwnerWithVehicle: 0,
        vc50UserDefined2: '',
        vc50UserDefined3: '',
        vc100Destination: '',
        vc255DriverNotes: '',
        fTaxRate_Override: '',
        gcLocationLatitude: '',
        vc20MembershipNum: '',
        vc100UserDefined1: '',
        gcLocationLongitude: '',
        bPortalToPortal: false,
        vc255DispatchNotes: '',
        vc255DispatchNotes2: '',
        vc255DispatchNotes3: '',
        vc255DispatchNotes4: '',
        lCallStatusTypeKey: '',
        bMileageRequired: false,
        bSecondCommission: false,
        vc20ContactPhoneNum: '',
        vc255AccountingNotes: '',
        gcDestinationLatitude: '',
        vc30ExtraVehicleInfo: '',
        vc50EquipmentRequired: '',
        gcDestinationLongitude: '',
        DefaultPricingRetrieved: false,
        fMileageUnloaded: '',
        fMileageLoaded: '',
        fMileageReturn: '',

        Holds: [],
        Retow: {},
        Contacts: [],
        Inventory: {},
        Dispatches: [],
        InspectionItems: []
      }
    };
  },

  computed: {
    canSave () {
      return this.call.lCustomerKey &&
        this.call.vc100Location &&
        this.call.lTowTypeKey &&
        this.quote.vc100GivenTo;
    },

    record: {
      /**
       * This value replaces the standard 'record' variable to preserve
       * inherited RecordView functionality while providing the ability
       * to destructure the objects and preserve local reactivity.
       */
      get () {
        return {
          ...this.quote,
          jQuoteData: JSON.stringify(this.call) // The API wants this
        };
      },
      set (value) {
        this.call = value.jQuoteData;
        this.quote = value;
        delete this.quote.jQuoteData;
      }
    },

    sectionsProxy () {
      let modifiedSectionsObject = {};

      // Potentially exclude sections based on other criterion
      let hiddenSections = [];

      forEach(this.sections, (section, key) => {
        if (section.enabled && !includes(hiddenSections, key)) {
          set(modifiedSectionsObject, key, section);
        }
      });

      return modifiedSectionsObject;
    },

    pinnedSections () {
      let sections = {};

      forEach(this.sectionsProxy, (section, key) => {
        if (section.pinned) {
          set(sections, key, section);
        }
      });

      return sections;
    },

    floatingSections () {
      let sections = {};

      forEach(this.sectionsProxy, (section, key) => {
        if (!section.pinned) {
          set(sections, key, section);
        }
      });

      return sections;
    },

    orderLinesProxy () {
      return get(this.call, 'TowOrderLines', []);
    },

    canShare () {
      return this.shareModal.to &&
        this.shareModal.subject &&
        this.shareModal.body;
    },

    shouldGetDefaults () {
      return this.isNewRecord &&
        Number(this.call.lSubterminalKey) > 0 &&
        Number(this.call.lCustomerKey) > 0;
    }
  },

  watch: {
    'shareModal.visible' () {
      if (!this.shareModal.visible) return;

      this.getEmailDefaults();
    },

    'call.lCustomerKey' () {
      this.quote.lCustomer = this.call.lCustomerKey;
    },

    shouldGetDefaults () {
      if (this.shouldGetDefaults) {
        this.getDefaults();
      }
    },

    orderLinesProxy () {
      if (!this.orderLinesProxy.length) return;

      this.call.TowOrderLines.forEach(lineItem => {
        lineItem.bCalculated = false;
      });
    },

    '$route': 'initializeView'
  },

  methods: {
    ...mapActions([
      'CALL__getSubterminals',
      'TOPSCALL__getDefaultsForNew',

      'QUOTE__email',
      'QUOTE__view',
      'QUOTE__createCall',
      'QUOTE__getEmailDefaults'
    ]),

    getDefaults () {
      this.$hub.$emit(EVENT_RESET_CALL_PRICING);

      if (!this.call.lCustomerKey) return;

      this.TOPSCALL__getDefaultsForNew({
        customerKey: this.call.lCustomerKey,
        callback: response => {
          let defaults = response;

          forOwn(defaults, (value, property) => {
            this.$set(this.call, property, defaults[property]);
          });
        }
      });
    },

    setPinnedSections () {
      let titleBar = window.getComputedStyle(document.querySelector('.title-bar'));
      let footerBar = window.getComputedStyle(document.querySelector('.footer-bar'));
      let content = window.getComputedStyle(document.querySelector('.content-section'));
      let pinnedSections = window.getComputedStyle(document.querySelector('.pinned-sections ._liner'));

      let pinnedSectionsElementHeight = Number(pinnedSections.height.replace('px', ''));
      let viewareaHeight = window.visualViewport.height -
        Number(titleBar.height.replace('px', '')) -
        Number(footerBar.height.replace('px', '')) -
        Number(content.paddingTop.replace('px', '')) -
        Number(content.paddingBottom.replace('px', ''));

      this.sectionsArePinned = pinnedSectionsElementHeight < viewareaHeight;
    },

    lookAt (section) {
      let content = window.getComputedStyle(document.querySelector('.content-section'));
      let offset = Number(content.paddingTop.replace('px', ''));

      if (this.sectionsArePinned) {
        let sections = window.getComputedStyle(document.querySelector('.sections'));
        let vehicleSection = window.getComputedStyle(document.querySelector('#vehicle'));

        offset = offset +
          Number(vehicleSection.height.replace('px', '')) +
          Number(sections.gap.replace('px', ''));
      }

      this.$gsap.to('.content-section', {
        duration: 0.8,
        ease: 'power4.out',
        scrollTo: {
          y: `#${section}`,
          offsetY: offset
        }
      });
    },

    getSubterminals () {
      this.CALL__getSubterminals({
        callback: response => {
          this.subterminals = response;
          const activeSubterminals = this.subterminals.filter(subterminal => subterminal.Active);
          this.sections.subterminal.enabled = activeSubterminals.length > 1;

          if (activeSubterminals.length === 1) {
            this.call.lSubterminalKey = activeSubterminals[0].Key;
          }
        }
      });
    },

    getEmailDefaults () {
      this.QUOTE__getEmailDefaults({
        key: this.quote.lKey,
        success: response => {
          this.shareModal.to = response.To;
          this.shareModal.subject = response.Subject;
          this.shareModal.body = response.body;
        }
      });
    },

    share () {
      if (!this.canShare) return;

      this.QUOTE__email({
        key: this.quote.lKey,
        to: this.shareModal.to,
        subject: this.shareModal.subject,
        body: this.shareModal.body,
        success: () => {
          this.shareModal.visible = false;

          this.getViewData();
        }
      });
    },

    preview () {
      this.QUOTE__view({
        key: this.quote.lKey,
        success: response => {
          window.open(`${import.meta.env.VITE_TXI_API}?${response.URLParameters}`);
        }
      });
    },

    createCall () {
      this.QUOTE__createCall({
        key: this.quote.lKey,
        success: response => {
          this.$router.push({
            name: 'Call',
            params: { key: response.Key },
            query: { trajectory: 'Quotes' }
          });
        }
      });
    },

    viewCall () {
      this.$router.push({
        name: 'Call',
        params: { key: this.quote.lCall }
      });
    },

    afterGetViewData () {
      // Duplication is in process so strip out certain values.
      if (this.recordToDuplicate) {
        this.quote.lCall = null;
        this.quote.lCreatedBy = null;
        this.quote.dCreated = null;
        this.quote.vc100GivenTo = null;
        this.quote.lDeletedBy = null;
        this.quote.vc255EmailTo = null;
        this.quote.dEmailSent = null;
        this.quote.lEmailSentBy = null;

        this.call.lCallKey = null;
        this.call.dCallTaken = null;

        this.call.TowOrderLines.forEach(lineItem => {
          lineItem.lCallKey = null;
          lineItem.dLastModified = null;
        });
      }
    }
  },

  mounted () {
    this.getSubterminals();

    window.addEventListener('resize', this.setPinnedSections);
  },

  beforeDestroy () {
    window.removeEventListener('resize', this.setPinnedSections);
  }
};
</script>
