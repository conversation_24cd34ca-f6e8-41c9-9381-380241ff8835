<template>
  <div id="records-view" data-page="call-tools">
    <section class="tool-bar">
      <wizard-button class="_process" @click="goToDataSheet" flavor="default" :icon="false" data-flip-id="back-button">
        <i class="fas fa-phone"></i>&nbsp;&nbsp;&nbsp;Back to calls
      </wizard-button>

      <div class="_tools">
        <wizard-button
          v-for="tool in visibleTools"
          @click="setTool(tool)"
          :active="isActive(tool)"
          :key="tool.ToolID"
          :data-flip-id="tool.ToolID">
          {{ tool.Name }}
          <template slot="meta">{{ tool.Description }}</template>
        </wizard-button>
      </div>

      <app-grid-form class="_inputs" context="inline" v-if="inputs.length" data-flip-id="inputs">
        <div class="columns is-multiline">
          <div class="column is-12 is-left" v-for="(input, index) in inputs" :key="input.Name">
            <component
              :is="input.Control"
              :id="input.Name"
              v-model="input.Value"
              :required="input.Required"
              :options="input.Options"
              :keyAlias="input.KeyAlias"
              :valueAlias="input.ValueAlias"
              :shortCodeAlias="input.ShortCodeAlias"
              :tabindex="index"
              :empty-option="input.EmptyOption">
              {{ input.Label }}
            </component>
          </div>
        </div>
      </app-grid-form>

      <wizard-button class="_process" @click="process" flavor="primary" :icon="false" v-if="toolId" :disabled="!canProcess" data-flip-id="process-button">
        Process {{ __selectedRecords.length }} call<span v-show="__selectedRecords.length !== 1">s</span>
      </wizard-button>
    </section>

    <grid
      class="_data"
      title=""
      key="grid"
      :grid="gridSettings"
      :data="gridData"
      :refreshedAt="refreshedAt"
      :config="viewConfig"
      :multiselect="true"
      :show-loader="showLoader"
      :filters-button-visible="false"
      :columns-button-visible="false"
      @refresh="refresh"
      @openRecord="openRecord"
      @exportData="exportData"
      @save="save">
    </grid>
  </div>
</template>

<script>import { filter, isEmpty, set, forEach, uniqueId, get, includes, map, has, find } from 'lodash-es';


import { mapActions, mapGetters } from 'vuex';
import { EVENT_INFO } from '@/config.js';
import Grid from '@/components/features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import WizardButton from '@/components/inputs/WizardButton.vue';

export default {
  name: 'call-tools',

  extends: RecordsView,

  components: {
    Grid,
    WizardButton
  },

  data () {
    return {
      viewConfig: {
        uuid: 'call-tools-screen',
        noun: 'CallTools',
        recordKeyName: 'CAL_lCallKey',
        readRouteName: 'CallReel',
        requireFilters: false,
        shouldImmediatelyLoadData: false
      },

      toolId: null,
      tools: [],
      inputs: [],
      processedCalls: [],

      inputModel: {
        DRIVER_KEY: {
          label: 'Driver',
          control: 'app-select',
          action: 'TOPSCOMPANY__getDrivers',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        TRUCK_KEY: {
          label: 'Truck',
          control: 'app-select',
          action: 'TOPSCOMPANY__getTrucks',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        LOT_KEY: {
          label: 'Lot',
          control: 'app-select',
          action: 'TOPSCOMPANY__getLots',
          value: -1,
          keyAlias: 'Key',
          valueAlias: 'Value',
          shortCodeAlias: 'ShortCode',
          isVisible: true
        },
        LOAD_NUMBER: {
          label: 'Load Number',
          control: 'app-text',
          isVisible: true
        }
      }
    };
  },

  computed: {
    ...mapGetters(['RECORDS__settings']),

    anyRecordsAreSelected () {
      return this.__selectedRecords.length >= 1;
    },

    canProcess () {
      let remainers = filter(this.inputs, input => {
        return input.Required && isEmpty(input.Value);
      });

      return this.toolId &&
        remainers.length === 0 &&
        this.anyRecordsAreSelected;
    },

    visibleTools () {
      return this.toolId
        ? filter(this.tools, ['ToolID', this.toolId])
        : this.tools;
    }
  },

  mounted () {
    this.getTools();
  },

  methods: {
    ...mapActions([
      'CALL_TOOLS__getToolInputs',
      'CALL_TOOLS__process',
      'TOPSCOMPANY__getCallTools',
      'TOPSCOMPANY__getDrivers',
      'TOPSCOMPANY__getTrucks',
      'TOPSCOMPANY__getLots'
    ]),

    goToDataSheet () {
      this.$router.push({ name: 'Calls' });
    },

    getTools () {
      this.TOPSCOMPANY__getCallTools({
        success: response => {
          this.tools = response;
        }
      });
    },

    setTool (tool) {
      this.toolId = this.toolId === tool.ToolID
        ? null
        : tool.ToolID;

      if (!this.toolId) {
        this.reset();
        return;
      }

      // Ignore pre-existing filters
      let settings = {
        ToolID: this.toolId,
        ...this.RECORDS__settings
      };

      set(settings, 'Grids[0].Filters', []);

      this.loadData(settings);

      this.$nextTick(() => {
        this.getInputs(this.toolId);
      });
    },

    isActive ({ ToolID }) {
      return this.toolId === ToolID;
    },

    getInputs (toolId) {
      this.CALL_TOOLS__getToolInputs({
        toolId: toolId,
        success: response => {
          this.inputs = this.hydrateInputs(response);
        }
      });
    },

    hydrateInputs (inputs) {
      return forEach(inputs, input => {
        set(input, 'VueKey', uniqueId());
        set(input, 'Label', get(this.inputModel[input.Name], 'label', ''));
        set(input, 'Control', get(this.inputModel[input.Name], 'control', ''));
        set(input, 'Action', get(this.inputModel[input.Name], 'action', ''));
        set(input, 'KeyAlias', get(this.inputModel[input.Name], 'keyAlias', ''));
        set(input, 'ValueAlias', get(this.inputModel[input.Name], 'valueAlias', ''));
        set(input, 'ShortCodeAlias', get(this.inputModel[input.Name], 'shortCodeAlias', ''));
        set(input, 'EmptyOption', get(this.inputModel[input.Name], 'emptyOption', ''));
        set(input, 'IsVisible', get(this.inputModel[input.Name], 'isVisible', ''));
        set(input, 'Value', get(this.inputModel[input.Name], 'value', ''));
        set(input, 'Options', []);

        input.Required = includes(['true', '1', true, 1], input.Required);

        if (input.Control === 'app-checkbox') {
          input.Value = includes(['true', '1', true, 1], input.Value);
        }

        if (isEmpty(input.Action)) return;

        this[input.Action]({
          callback: response => {
            this.$set(input, 'Options', response);
          }
        });
      });
    },

    process () {
      let requestPayload = {
        toolId: this.toolId,
        callKeys: [],
        success: response => {
          this.processedCalls = response;

          this.pushErrorsIntoGrid();

          this.$hub.$emit(EVENT_INFO, 'Calls may or may not have been processed depending on their status.');
        }
      };

      requestPayload.callKeys = map(this.__selectedRecords, record => {
        if (has(record, this.viewConfig.recordKeyName)) return record[this.viewConfig.recordKeyName];
      });

      forEach(this.inputs, input => {
        set(requestPayload, input.Name, input.Value);
      });

      this.CALL_TOOLS__process(requestPayload);
    },

    pushErrorsIntoGrid () {
      forEach(this.processedCalls, call => {
        if (!has(call, 'Errors')) return;

        let remoteCall = find(this.gridData, [this.viewConfig.recordKeyName, call.CallKey]);

        this.$set(remoteCall, 'Errors', get(call, 'Errors', []));
      });
    },

    reset () {
      this.__selectRecords([]);
      this.viewData = {};
      this.processedCalls = [];
      this.inputs = [];
    }
  }
};
</script>
