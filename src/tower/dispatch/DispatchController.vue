<template>
  <div class="dispatch">
    <div class="grid-container" :class="{ 'hide-joboffers': !$store.state.dispatch.jobOffersVisible }">
      <transition name="fade" mode="out-in">
        <grid
          title="Unassigned Calls"
          key="grid"
          class="grid unassigned"
          :grid="unassignedGridSettings"
          :data="unassignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :quickAssign="true"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          :gridType="'unassigned'"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleAllAppointments()"><i :class="appointmentsIconClasses"></i>&nbsp;&nbsp;Appointments</app-button>
            <app-button type="white" @click="toggleJobOffers()" v-if="isJobOffersEnabled">
              <i :class="jobOffersIconClasses"></i>&nbsp;&nbsp;Jobs&nbsp;
              <i class="fas fa-circle-small pure-yellow" v-if="$store.getters['job_offers/newJobsAndMessages']" :title="$store.getters['job_offers/newJobsAndMessages'] + ' new jobs and messages'"></i>
            </app-button>
          </template>
        </grid>
      </transition>

      <div class="resizer" @dblclick="recenterHorizontalDivider"></div>

      <transition name="fade" mode="out-in">
        <grid
          title="Assigned Calls"
          key="grid"
          class="grid assigned"
          :grid="assignedGridSettings"
          :data="assignedGridData"
          :refreshedAt="refreshedAt"
          :config="viewConfig"
          :actions="true"
          :show-loader="showLoader"
          :findButtonVisible="false"
          :page-results="false"
          :gridType="'assigned'"
          @refresh="refresh"
          @openRecord="openRecord"
          @exportData="exportData"
          @save="save"
          @getActions="getActions">
          <template slot="context-tools">
            <app-button type="white" @click="toggleDispatchUnits">Dispatch Units</app-button>
            <app-button type="white" @click="notify">Notify</app-button>
          </template>

          <template slot="floating-tools">
            <app-button type="success" size="normal" @click="addCall" v-if="canAddCall">
              <i class="far fa-plus"></i>&nbsp;Add
            </app-button>
          </template>
        </grid>
      </transition>

      <JobOffers v-show="$store.state.dispatch.jobOffersVisible" class="grid joboffers" />
    </div>

    <actions
      :show="actionsVisible"
      :record="actionableRecord"
      :call-key="actionableRecord.lCallKey"
      :subterminal-key="actionableRecord.lSubterminalKey"
      :dispatch-key="actionableRecord.lDispatchKey"
      @close="toggleActions"
      @notify="notify">
    </actions>

    <app-modal title="Payments" @close="$_PaymentMixin_togglePayments" :show="paymentsVisible">
      <payment-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="paymentsVisible"
        @close="$_PaymentMixin_togglePayments(false)">
      </payment-section>
    </app-modal>

    <app-modal title="Holds" @close="$_HoldsMixin_toggleHolds" :show="holdsVisible">
      <holds-section
        :callKey="actionableRecord.lCallKey"
        :isVisible="holdsVisible"
        @close="$_HoldsMixin_toggleHolds(false)">
      </holds-section>
    </app-modal>

    <app-modal title="Dispatch Units" @close="toggleDispatchUnits" :show="dispatchUnitsVisible" :pad="false">
      <DispatchUnitsController />
    </app-modal>

    <quick-views
      :viewKey="viewConfig.key"
      :viewUuid="viewConfig.uuid"
      :noun="viewConfig.noun"
      @viewLoaded="loadData">
    </quick-views>
  </div>
</template>

<script>

import { get, find, isEmpty, debounce, throttle } from 'lodash-es';
import is from 'is_js';
import Access from '@/utils/access.js';
import { mapGetters } from 'vuex';
import keyCodes from '@/utils/keycodes.js';
import Actions from '@/tower/actions/ActionsController.vue';
import Grid from '@/components/features/DispatchGrid.vue';
import HoldsSection from '@/components/call/HoldsSection.vue';
import HoldsMixin from '@/mixins/holds_mixin.js';
import { GRID_KEY, COMPANY_ID } from '@/config.js';
import PaymentSection from '@/components/call/PaymentSection.vue';
import PaymentMixin from '@/mixins/payment_mixin.js';
import RecordsView from '@/components/ancestors/RecordsView.vue';
import DispatchUnitsController from '@/components/dispatchunits/Controller.vue';
import JobOffers from '@/tower/dispatch/JobOffers.vue';

export default {
  name: 'dispatch',

  extends: RecordsView,

  mixins: [
    HoldsMixin,
    PaymentMixin
  ],

  components: {
    Grid,
    Actions,
    HoldsSection,
    PaymentSection,
    DispatchUnitsController,
    JobOffers
  },

  /* TODO: Handle these terminal settings
  TRMlDispatchRefresh_Default;
  TRMbAdvancedDriverTruckFilter;
  TRMbAllowCallNumAsInvoiceNum;

  UPRvc50LastLocationName
  UPRvc100DispatchDumpDir
  UPRlMaxFindRecords
  UPRlHighlightETA
  UPRlDispatchRefresh
  UPRLOClSubterminalKey
  */

  data () {
    return {
      viewConfig: {
        key: null,
        uuid: 'dispatch-screen',
        noun: 'Dispatches',
        recordKeyName: 'lCallKey',
        readRouteName: 'Call',
        requireFilters: false,
        dataAdditional: {
          SubcompanyKey: '', // Set in mounted()
          ShowApptCallMinutes: ''
        }
      },

      refreshInterval: 0,
      refreshTimer: null,
      actionableRecord: {},
      actionsVisible: false,
      dispatchUnitsVisible: false,
      showApptCallMinutesCache: '',
      assignedCallGridKey: GRID_KEY.assigned,
      unassignedCallGridKey: GRID_KEY.unassigned,

      gridContainer: {
        endY: 0,
        startY: 0,
        maxHeight: 100,
        minHeight: 100,
        resizeTarget: {},
        isResizing: false,
        containerRectangle: null,
        animationFrameId: null
      }
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'USER__state',
      'TOPSCOMPANY__settings'
    ]),

    isJobOffersEnabled () {
      return Access.has('jobOffers.manage') && this.$store.getters['TOPSCOMPANY__hasMotorClubAccounts'];
    },

    unassignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.unassignedCallGridKey]);

      return settings || {};
    },

    unassignedGridData () {
      return get(this.viewData, `Grids[${this.unassignedCallGridKey}]`, []);
    },

    assignedGridSettings () {
      let settings = find(this.RECORDS__settings.Grids, ['Key', this.assignedCallGridKey]);

      return settings || {};
    },

    assignedGridData () {
      return get(this.viewData, `Grids[${this.assignedCallGridKey}]`, []);
    },

    appointmentsIconClasses () {
      return {
        'fas': this.$store.state.dispatch.allAppointmentsVisible,
        'fal': !this.$store.state.dispatch.allAppointmentsVisible,
        'fa-calendar': true
      };
    },

    jobOffersIconClasses () {
      return {
        'fas': this.$store.state.dispatch.jobOffersVisible,
        'fal': !this.$store.state.dispatch.jobOffersVisible,
        'fa-sidebar-flip': true
      };
    },

    canAddCall () {
      if (Number(this.__state.orgUnitKey) === COMPANY_ID.GRAND_RAPIDS_POLICE_DEPARTMENT) {
        return !Access.has('calls.duplicate');
      }

      return true;
    }
  },

  watch: {
    '$store.state.dispatch.horizontalDividerPosition' () {
      this.updateGridDividerPositions();
    },

    '$store.state.dispatch.verticalDividerPosition' () {
      this.updateGridDividerPositions();
    }
  },

  methods: {
    resetTimer () {
      this.clearRefreshTimer();

      if (this.refreshInterval > 0) {
        this.refreshTimer = window.setInterval(() => {
          this.refresh();
        }, this.refreshInterval * 1000);
      }
    },

    clearRefreshTimer () {
      if (this.refreshTimer !== null) {
        window.clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },

    beforeLoadData () {
      return new Promise((resolve, reject) => {
        this.viewConfig.dataAdditional.ShowApptCallMinutes = this.$store.state.dispatch.allAppointmentsVisible
          ? ''
          : this.showApptCallMinutesCache;

        resolve();
      });
    },

    afterLoadData () {
      this.resetTimer();

      setTimeout(() => {
        this.initializeGridResizing();
      }, 1000);
    },

    afterGetTOPSCompanySettings () {
      this.setUserCompanySettings();
    },

    setUserCompanySettings () {
      let intervalProfile = get(this.USER__state.Profile, 'lDispatchRefresh', null);
      let intervalDefault = get(this.TOPSCOMPANY__settings, 'lDispatchRefresh_Default', null);
      let interval = intervalProfile || intervalDefault;

      this.refreshInterval = is.falsy(interval) ? 0 : interval;

      this.resetTimer();

      this.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.USER__state.Profile, 'lShowApptCall', '');

      if (isEmpty(this.viewConfig.dataAdditional.ShowApptCallMinutes)) {
        this.viewConfig.dataAdditional.ShowApptCallMinutes = get(this.TOPSCOMPANY__settings, 'lShowApptCall_Default', '');
      }

      this.showApptCallMinutesCache = this.viewConfig.dataAdditional.ShowApptCallMinutes;
    },

    getActions (record) {
      this.actionableRecord = record;
      this.actionsVisible = true;
    },

    toggleActions () {
      if (is.truthy(this.actionsVisible)) this.refresh();

      this.actionsVisible = !this.actionsVisible;
    },

    addCall () {
      this.$router.push({ name: 'AddCall' });
    },

    notify (payload) {
      this.$router.replace({
        name: 'Notify',
        query: {
          callKey: get(payload, 'callKey', ''),
          dispatchKey: get(payload, 'dispatchKey', ''),
          dispatchDriverKey: get(payload, 'dispatchDriverKey', ''),
          dispatchTruckKey: get(payload, 'dispatchTruckKey', ''),
          dispatchEmployeeKey: get(payload, 'dispatchEmployeeKey', ''),
          reason: get(payload, 'reason', ''),
          returnTo: 'Dispatch'
        }
      });
    },

    toggleDispatchUnits () {
      this.dispatchUnitsVisible = !this.dispatchUnitsVisible;
    },

    handleKeyboardEvents (event) {
      if (event.altKey && event.keyCode === keyCodes.a) {  // Alt + A
        this.addCall();
      }
    },

    mouseDownHandler (event) {
      if (event.target.className === 'resizer') {
        this.gridContainer.isResizing = true;
        this.gridContainer.startY = event.y;
        this.gridContainer.containerRect = this.gridContainer.resizeTarget.getBoundingClientRect();
      }
    },

    mouseUpHandler (event) {
      this.gridContainer.isResizing = false;
      this.gridContainer.containerRect = null;
      if (this.gridContainer.animationFrameId) {
        cancelAnimationFrame(this.gridContainer.animationFrameId);
        this.gridContainer.animationFrameId = null;
      }

      // Re-enable transitions after manual resize ends
      if (this.gridContainer.resizeTarget && this.gridContainer.resizeTarget.style) {
        this.gridContainer.resizeTarget.style.transition = '';
      }
    },

    mouseMoveHandler: throttle(function (event) {
      if (!this.gridContainer.isResizing) return;

      event.preventDefault();

      const container = this.gridContainer.containerRect;
      if (!container) return;

      const newDividerPosition = ((event.y - container.top) / container.height) * 100;

      // Clamp between 10% and 90%
      const clampedPosition = Math.max(10, Math.min(90, newDividerPosition));

      if (this.gridContainer.animationFrameId) {
        cancelAnimationFrame(this.gridContainer.animationFrameId);
      }

      this.gridContainer.animationFrameId = requestAnimationFrame(() => {
        this.$store.state.dispatch.horizontalDividerPosition = `${clampedPosition}%`;
        this.gridContainer.animationFrameId = null;
      });
    }, 16), // ~60fps throttling

    initializeGridResizing () {
      if (!isEmpty(this.gridContainer.resizeTarget)) return;

      this.$set(this.gridContainer, 'resizeTarget', document.querySelector('.grid-container'));

      this.updateGridDividerPositions();
    },

    updateGridDividerPositions () {
      if (!this.gridContainer.resizeTarget) return;

      // Disable transitions during manual resize for performance
      if (this.gridContainer.isResizing) {
        this.gridContainer.resizeTarget.style.transition = 'none';
      }

      this.gridContainer.resizeTarget.style.setProperty('--horizontal-divider-pos', this.$store.state.dispatch.horizontalDividerPosition);
      this.gridContainer.resizeTarget.style.setProperty('--vertical-divider-pos', this.$store.state.dispatch.verticalDividerPosition);
    },

    toggleAllAppointments (value = !this.$store.state.dispatch.allAppointmentsVisible) {
      this.$store.state.dispatch.allAppointmentsVisible = value;
      this.viewConfig.dataAdditional.ShowApptCallMinutes = value ? '' : this.showApptCallMinutesCache;

      this.loadData();
    },

    toggleJobOffers (value = !this.$store.state.dispatch.jobOffersVisible) {
      this.$store.state.dispatch.jobOffersVisible = value;
    },

    recenterHorizontalDivider () {
      // Temporarily disable transitions during manual resize to avoid conflicts
      const container = this.gridContainer.resizeTarget;
      if (container && !this.gridContainer.isResizing) {
        container.style.transition = 'grid-template-rows 0.3s ease-out';
        this.$store.state.dispatch.horizontalDividerPosition = '50%';

        setTimeout(() => {
          if (container && !this.gridContainer.isResizing) {
            container.style.transition = '';
          }
        }, 300);
      } else {
        this.$store.state.dispatch.horizontalDividerPosition = '50%';
      }
    }
  },

  created () {
    document.addEventListener('keydown', this.handleKeyboardEvents);
    document.addEventListener('mousedown', this.mouseDownHandler);
    document.addEventListener('mouseup', this.mouseUpHandler);
    document.addEventListener('mousemove', this.mouseMoveHandler);
  },

  mounted () {
    this.viewConfig.dataAdditional.SubcompanyKey = this.__state.user.Profile.lSubterminalKey || '';
    this.$store.state.addCall.shouldStayOnSave = false;

    this.$nextTick(() => {
      const container = document.querySelector('.grid-container');
      if (container) {
        container.style.setProperty('--horizontal-divider-pos', this.$store.state.dispatch.horizontalDividerPosition);
        container.style.setProperty('--vertical-divider-pos', this.$store.state.dispatch.verticalDividerPosition);
      }
    });
  },

  destroyed () {
    this.clearRefreshTimer();

    document.removeEventListener('keydown', this.handleKeyboardEvents);
    document.removeEventListener('mousedown', this.mouseDownHandler);
    document.removeEventListener('mouseup', this.mouseUpHandler);
    document.removeEventListener('mousemove', this.mouseMoveHandler);
  }
};
</script>

<style scoped>
.grid-container {
  --horizontal-divider-pos: 50%;
  --vertical-divider-pos: calc(100% - 25rlh);

  display: grid;
  grid-template-columns: var(--vertical-divider-pos) 1fr;
  grid-template-rows: var(--horizontal-divider-pos) 0.5rlh 1fr;
  grid-template-areas:
    "unassigned joboffers"
    "resizer joboffers"
    "assigned joboffers";

  height: 100dvh;
  transition: grid-template-rows 0.3s ease-out;

  &.hide-joboffers {
    grid-template-columns: 1fr;
    grid-template-areas:
      "unassigned"
      "resizer"
      "assigned";
  }

  .grid {
    overflow: hidden;

    &.unassigned {
      grid-area: unassigned;
    }

    &.assigned {
      grid-area: assigned;
    }

    &.joboffers {
      grid-area: joboffers;

      overflow-y: auto;
    }
  }

  .resizer {
    height: 0.5rlh;
    background: var(--divider-bg);
    cursor: ns-resize;
  }
}

.grid-container:not(.hide-joboffers) :deep(._floating-tools .pill) {
  margin-right: 25rlh;
}
</style>
