import { ref, onMounted, onUnmounted } from 'vue';
import store from '@/store';

let timer = null;
let subscribers = 0;

export function useJobOffersPolling(interval = 15000) {
  const isRunning = ref(false);

  const loadJobs = async () => {
    try {
      await store.dispatch('job_offers/fetchNewOffers');
    } catch (error) {
      console.error('Failed to load job offers:', error);
      throw error;
    }
  };

  const start = () => {
    if (timer == null) {
      timer = setInterval(loadJobs, interval);
      isRunning.value = true;
    }
  };

  const stop = () => {
    subscribers--;
    if (timer != null && subscribers === 0) {
      clearInterval(timer);
      timer = null;
      isRunning.value = false;
    }
  };

  onMounted(() => {
    subscribers++;
    start();
    loadJobs(); // immediate first load
  });

  onUnmounted(stop);

  return { loadJobs, isRunning };
}
