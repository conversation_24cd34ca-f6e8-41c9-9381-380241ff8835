<template>
  <Expandable>
    <template slot="summary">
      <section class="job-summary">
        <i class="is-new fas fa-circle-small" v-if="job.New"></i>
        <span class="job-title">{{ job.Title }}</span>
        <div class="expiration" v-if="job.Expiration">
          <CountdownTimer :end-time="job.Expiration" />
        </div>
        <ul class="tags">
          <li v-for="tag in job.Info" :key="tag">{{ tag }}</li>
        </ul>
      </section>
    </template>

    <job-actions
      v-if="job.New"
      :job-key="job.Key"
      @action-success="$emit('action-success', $event)"
      @action-error="$emit('action-error', $event)" />

    <job-details :job-key="job.Key" />
  </Expandable>
</template>

<script setup>
import Expandable from '@/tower/liens/inputs/Expandable.vue';
import JobDetails from './JobDetails.vue';
import JobActions from './JobActions.vue';
import CountdownTimer from '@/components/CountdownTimer.vue';

const props = defineProps({
  job: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['action-success', 'action-error']);
</script>

<style scoped>
.job-summary {
  display: grid;
  grid-template-columns: 1rlh 2fr 1fr;
  grid-template-rows: min-content min-content;
  grid-template-areas:
    "indicators title expiration"
    "indicators tags tags";

  inline-size: 100cqi;
  padding: 0.25rlh;
}

.is-new {
  grid-area: indicators;

  margin-top: 0.25em;
  color: var(--pure-orange);
}

.job-title {
  grid-area: title;

  margin: 0;
  font-weight: 600;
  font-size: 1rem;
}

.tags {
  grid-area: tags;

  display: inline;

  margin: 0;
  font-size: var(--font-size-small1);
  opacity: 0.75;
}

.expiration {
  grid-area: expiration;
  justify-self: end;

  font-weight: 600;
  opacity: 0.75;
}
</style>
