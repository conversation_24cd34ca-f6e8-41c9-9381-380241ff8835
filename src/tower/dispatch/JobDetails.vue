<template>
  <div class="job-details">
    <app-tip v-if="errorMessage">
      <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
      {{ errorMessage }}
    </app-tip>

    <template v-if="isLoading">
      <p>Loading details...</p>
    </template>
    <template v-else-if="jobDetails">
      <div class="job-group" v-for="group in jobDetails" :key="group.Group">
        <h5 class="group-title">{{ group.Title }}</h5>
        <div class="item-list">
          <div class="job-item" v-for="item in group.Items" :key="item.Name">
            <span class="item-name">{{ item.Name }}:</span>
            <span class="item-value">{{ formatValue(item.Value) }}</span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import store from '@/store';

const props = defineProps({
  jobKey: {
    type: Number,
    required: true
  }
});

const jobDetails = ref(null);
const isLoading = ref(false);
const errorMessage = ref('');

const loadJobDetails = async () => {
  if (isLoading.value || jobDetails.value) return;

  isLoading.value = true;
  try {
    jobDetails.value = await store.dispatch('job_offers/getJobDetails', props.jobKey);
  } catch (error) {
    console.error('Failed to load job details:', error);
    errorMessage.value = 'Failed to load job details.';
  } finally {
    isLoading.value = false;
  }
};

const formatValue = (value) => {
  if (value == null) return '-';
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  return value;
};

onMounted(() => {
  loadJobDetails();
});
</script>

<style scoped>
.job-details {
  display: flex;
  flex-direction: column;
  gap: 0.35rlh;
}

.job-group {
  padding: 0.5rlh;
  background: color-mix(in oklch, transparent, var(--white) 60%);
  border-radius: 0.25rlh;
}

.group-title {
  font-size: 1rem;
  font-weight: 600;
}

.item-list {
  display: flex;
  flex-direction: column;
  gap: 0.15rlh;
}

.job-item {
  display: flex;
  font-size: var(--font-size-small1);
  gap: 0.25rlh;

  .item-name {
    font-weight: 600;
    min-width: 33%;
    opacity: 0.75;
  }

  .item-value {
    word-break: break-word;
  }
}
</style>
