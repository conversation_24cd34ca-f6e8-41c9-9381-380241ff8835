<template>
  <div class="job-actions">
    <div class="action-buttons">
      <InputButton
        v-for="(action, index) in actions"
        :key="`${action.ResponseTag}-${action.ResponseValue}-${index}`"
        :variant="getActionButtonVariant(action.ResponseValue)"
        size="small"
        @click.stop="handleAction(action)"
        :loading="processingAction === `${action.ResponseTag}-${action.ResponseValue}`">
        {{ action.Display }}
      </InputButton>
    </div>

    <dialog
      ref="actionDialog"
      class="dialog"
      popover
      @close="cancelAction">
      <div class="dialog-header">
        <h3 class="dialog-title">{{ currentAction?.Description || 'Action' }}</h3>
        <button type="button" class="dialog-close" @click="cancelAction">×</button>
      </div>

      <div class="dialog-content">
        <form
          ref="actionFormRef"
          class="action-form"
          @submit.prevent="executeAction"
          @keydown.enter="executeAction">
          <div
            v-for="item in currentAction?.AdditionalItems || []"
            :key="item.ResponseTag"
            class="form-item">

            <!-- Dropdown for options -->
            <InputSelect
              v-if="item.Options && item.Options.length > 0"
              :id="item.ResponseTag"
              v-model="formData[item.ResponseTag]"
              :options="item.Options"
              :keyAlias="'Value'"
              :valueAlias="'Description'"
              :required="item.Required === 'true'">
              {{ item.Display }}
            </InputSelect>

            <!-- Number input for Integer/Float -->
            <InputNumber
              v-else-if="['Integer', 'Float'].includes(item.ValueType)"
              :id="item.ResponseTag"
              v-model="formData[item.ResponseTag]"
              :required="item.Required === 'true'">
              {{ item.Display }}
            </InputNumber>

            <!-- Text input for String -->
            <InputText
              v-else
              :id="item.ResponseTag"
              v-model="formData[item.ResponseTag]"
              :required="item.Required === 'true'">
              {{ item.Display }}
            </InputText>
          </div>

          <app-tip v-if="errorMessage">
            <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
            {{ errorMessage }}
          </app-tip>
        </form>
      </div>

      <div class="dialog-footer">
        <InputButton @click="cancelAction">Cancel</InputButton>
        <InputButton
          variant="blue"
          @click="executeAction"
          :loading="processingAction">
          {{ currentAction?.Display || 'Execute' }}
        </InputButton>
      </div>
    </dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount } from 'vue';
import store from '@/store';
import InputNumber from '@/tower/liens/inputs/Number.vue';
import InputText from '@/tower/liens/inputs/Text.vue';
import InputSelect from '@/tower/liens/inputs/Select.vue';
import InputButton from '@/tower/liens/inputs/Button.vue';

const props = defineProps({
  jobKey: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['action-success', 'action-error']);

const processingAction = ref(null);
const currentAction = ref(null);
const actions = ref([]);
const isLoadingActions = ref(false);
const actionFormRef = ref(null);
const actionDialog = ref(null);
const errorMessage = ref(null);

const formData = reactive({});

const loadJobActions = async () => {
  if (isLoadingActions.value || actions.value.length) return;

  isLoadingActions.value = true;
  try {
    actions.value = await store.dispatch('job_offers/getJobActions', props.jobKey);
  } catch (error) {
    console.error('Failed to load job actions:', error);
  } finally {
    isLoadingActions.value = false;
  }
};

const handleAction = (action) => {
  currentAction.value = action;
  errorMessage.value = null;

  // Reset form data
  Object.keys(formData).forEach(key => delete formData[key]);

  // Initialize form data with default values
  if (action.AdditionalItems) {
    action.AdditionalItems.forEach(item => {
      formData[item.ResponseTag] = item.ResponseValue || '';
    });
  }

  // If action has additional items, show form dialog
  if (action?.AdditionalItems?.length) {
    actionDialog.value?.showPopover();
    // Focus the first form input after dialog opens
    setTimeout(() => {
      const firstInput = actionDialog.value?.querySelector('input, select, textarea');
      firstInput?.focus();
    }, 0);
  } else {
    // Execute action directly if no additional items
    executeActionDirect(action);
  }
};

const executeAction = async () => {
  if (!currentAction.value) return;

  errorMessage.value = null;

  if (currentAction.value?.AdditionalItems?.length) {
    const formEl = actionFormRef.value?.$el || actionFormRef.value;
    if (formEl && !formEl.checkValidity()) {
      formEl.reportValidity();
      return;
    }
  }

  await executeActionDirect(currentAction.value);
  if (!errorMessage.value) {
    cancelAction();
  }
};

const executeActionDirect = async (action) => {
  processingAction.value = `${action.ResponseTag}-${action.ResponseValue}`;

  try {
    const actionPayload = {
      jobKey: props.jobKey,
      responseTag: action.ResponseTag,
      responseValue: action.ResponseValue
    };

    if (action?.AdditionalItems?.length) {
      action.AdditionalItems.forEach(item => {
        actionPayload[item.ResponseTag] = formData[item.ResponseTag];
      });
    }

    const result = await store.dispatch('job_offers/handleJobAction', actionPayload);
    if (result.Result !== 'SUCCESS') throw result.data?.Error?.Message;

    emit('action-success', { action, result });

  } catch (error) {
    console.error('Unable to execute the action:', error);

    errorMessage.value = error || 'Unable to execute the action';

    emit('action-error', { action, error });
  } finally {
    processingAction.value = null;
  }
};

const cancelAction = () => {
  actionDialog.value?.hidePopover();
  currentAction.value = null;
  errorMessage.value = null;
  Object.keys(formData).forEach(key => delete formData[key]);
};

const getActionButtonVariant = (responseValue) => {
  const typeMap = {
    'Accept': 'green',
    'Reject': 'red',
    'PhoneCallRequested': 'orange',
    'default': 'default'
  };
  return typeMap[responseValue] || typeMap.default;
};

onBeforeMount(() => {
  loadJobActions();
});
</script>

<style scoped>
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rlh;

  margin-bottom: 0.4rlh;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rlh 0.75rlh;
  border-bottom: 1px solid color-mix(in oklch, var(--blue), transparent 90%);
}

.dialog-title {
  font-weight: 600;
}

.dialog-close {
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
  inline-size: 1.5rem;
  block-size: 1.5rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  opacity: 0.5;
  border-radius: 0.25rlh;
  transition: all 0.2s;

  &:hover {
    background: color-mix(in oklch, var(--blue), transparent 90%);
  }
}

.dialog-content {
  padding: 1rlh;
}

.action-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rlh;
}

.dialog-footer {
  display: flex;
  gap: 0.5rlh;
  justify-content: flex-end;

  padding: 0.5rlh 0.75rlh;
  border-top: 1px solid color-mix(in oklch, var(--blue), transparent 90%);
  background: color-mix(in oklch, var(--blue), transparent 95%);
}
</style>
