<template>
  <div class="job-offers">
    <div class="job-offers-header">
      <tab-group v-model="store.state.job_offers.selectedTab">
        <tab-item value="job-offers">Job Offers <AppBadge :count="store.getters['job_offers/newJobs'].length" variant="secondary" /></tab-item>
        <tab-item value="messages">Updates <AppBadge :count="store.state.job_offers.messages.length" variant="secondary" /></tab-item>
      </tab-group>
    </div>

    <app-tip v-if="isLoadingState" class="loading-state">
      <i class="fas fa-spinner fa-spin pure-blue" slot="icon"></i>
      <span>Loading job offers...</span>
    </app-tip>

    <app-tip v-else-if="isEmptyState" class="empty-state">
      <i class="fas fa-check-circle pure-green" slot="icon"></i>
      You're all caught up!
    </app-tip>

    <app-tip v-else-if="store.state.job_offers.error" class="error-state">
      <i class="fas fa-exclamation-triangle pure-red" slot="icon"></i>
      {{ store.state.job_offers.error }}
    </app-tip>

    <template v-else>
      <div class="jobs-list" v-if="store.state.job_offers.selectedTab === 'job-offers'">
        <JobOfferItem
          v-for="job in store.state.job_offers.jobs"
          :key="job.key"
          :job="job"
          @action-success="handleActionSuccess"
          @action-error="handleActionError" />
      </div>

      <div class="messages-list" v-if="store.state.job_offers.selectedTab === 'messages'">
        <JobMessageItem
          v-for="message in store.state.job_offers.messages"
          :message="message"
          :key="message.key" />
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import store from '@/store';
import JobOfferItem from './JobOfferItem.vue';
import JobMessageItem from './JobMessageItem.vue';
import AppBadge from '@/components/AppBadge.vue';
import { useJobOffersPolling } from './useJobOffersPolling.js';

const { pollInterval } = defineProps({
  pollInterval: {
    type: Number,
    default: 15000 // 15 seconds
  }
});

const { loadJobs } = useJobOffersPolling(pollInterval);

const isLoadingState = computed(() => {
  return store.state.job_offers.loading &&
    !store.state.job_offers.jobs.length &&
    !store.state.job_offers.messages.length;
});

const isEmptyState = computed(() => {
  return !store.state.job_offers.loading &&
    !store.state.job_offers.jobs.length &&
    !store.state.job_offers.messages.length;
});

const handleActionSuccess = ({ action, result }) => {
  loadJobs();
};

const handleActionError = ({ action, error }) => {
  // ...
};
</script>

<style scoped>
.job-offers {
  display: flex;
  flex-direction: column;
  gap: 1rlh;

  padding: 0.5rlh;
  block-size: 100dvh;
  overflow-y: auto;
}

.job-offers-header {
  position: sticky;
  top: 0;
  z-index: 10;

  display: grid;
  place-content: center;

  .tab-group {
    backdrop-filter: blur(0.5rem);
  }
}

.loading-state, .empty-state, .error-state {
  margin: 1rlh;
}

.job-offers-content {
  max-height: 600px;
  overflow-y: auto;
}

.jobs-list, .messages-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

@media (max-width: 768px) {
  .job-offers {
    padding: 16px;
  }

  .job-offers-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .job-offers-content {
    max-height: 500px;
  }
}
</style>
