<template>
  <div class="job-message-item">
    <InputButton class="acknowledge"
      @click.stop="handleAcknowledge"
      :loading="isAcknowledging">
      <i class="far fa-archive"></i>
    </InputButton>
    <div class="message-title" v-html="message.Title"></div>
    <ul class="tags">
      <li v-for="(tag, index) in formattedTags" :key="index" v-html="tag"></li>
      <li class="error-message" v-if="errorMessage" v-html="errorMessage"></li>
    </ul>
    <div class="expiration" v-if="message.Expiration">
      <CountdownTimer :end-time="message.Expiration" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect } from 'vue';
import store from '@/store';
import CountdownTimer from '@/components/CountdownTimer.vue';
import InputButton from '@/tower/liens/inputs/Button.vue';

const emit = defineEmits(['acknowledged']);

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
});

const isAcknowledging = ref(false);
const errorMessage = ref('');

const formattedTags = computed(() => {
  return props.message.Info.map(tag => tag.replace(/\n/g, '<br>'));
});

const handleAcknowledge = async () => {
  if (isAcknowledging.value) return;

  isAcknowledging.value = true;
  errorMessage.value = '';
  try {
    await store.dispatch('job_offers/acknowledgeMessage', props.message.Key);
    emit('acknowledged', props.message.Key);
  } catch (error) {
    console.error('handleAcknowledge:', error);
    errorMessage.value = 'Failed to acknowledge message.';
  } finally {
    isAcknowledging.value = false;
  }
};
</script>

<style scoped>
.job-message-item {
  display: grid;
  gap: 0 0.5rlh;
  grid-template-columns: min-content 2fr 1fr;
  grid-template-rows: min-content min-content;
  grid-template-areas:
    "acknowledge title expiration"
    "acknowledge tags tags";

  inline-size: 100%;
  padding: 0.5rlh;
  border: 0.2rem solid color-mix(in oklch, var(--blue), transparent 90%);
  border-radius: 0.7rem;
}

.acknowledge {
  grid-area: acknowledge;
  place-self: start;

  margin-top: 0.25em;
}

.message-title {
  grid-area: title;

  margin: 0;
  font-weight: 600;
  font-size: 1rem;
}

.tags {
  grid-area: tags;

  display: inline;

  margin: 0;
  font-size: var(--font-size-small1);
  opacity: 0.75;
}

.expiration {
  grid-area: expiration;
  justify-self: end;

  font-weight: 600;
  opacity: 0.75;
}

.error-message {
  color: var(--pure-red);
}
</style>
