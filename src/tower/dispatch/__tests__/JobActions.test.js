import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import JobActions from '../JobActions.vue';

// Mock the store
vi.mock('@/store', () => ({
  default: {
    dispatch: vi.fn()
  }
}));

// Mock API response data
const mockApiResponse = {
  Result: "SUCCESS",
  Data: {
    Actions: [
      {
        Display: "Accept",
        ResponseTag: "Job",
        ResponseValue: "Accept",
        Description: "Accept the Job",
        AdditionalItems: [
          {
            Display: "ETA in Minutes",
            ResponseTag: "ETA",
            Required: "true",
            ResponseValue: "",
            ValueType: "Integer",
            Options: null
          },
          {
            Display: "Remarks",
            ResponseTag: "Remarks",
            Required: "false",
            ResponseValue: "",
            ValueType: "String",
            Options: null
          }
        ]
      },
      {
        Display: "Reject",
        ResponseTag: "Job", 
        ResponseValue: "Reject",
        Description: "Reject the Job",
        AdditionalItems: [
          {
            Display: "Reason",
            ResponseTag: "RejectReasonCode",
            Required: "true",
            ResponseValue: "",
            Options: [
              {
                Value: "OutOfArea",
                Description: "Out of Area"
              },
              {
                Value: "Weather",
                Description: "Weather"
              }
            ],
            ValueType: "String"
          }
        ]
      },
      {
        Display: "Phone Call",
        ResponseTag: "Job",
        ResponseValue: "PhoneCallRequested",
        Description: "Request a Phone Call for the Job"
      }
    ]
  }
};

describe('JobActions', () => {
  let mockStore;
  
  beforeEach(async () => {
    vi.clearAllMocks();
    const storeModule = await import('@/store');
    mockStore = storeModule.default;
  });

  it('should render action buttons with correct labels', async () => {
    mockStore.dispatch.mockResolvedValue(mockApiResponse);

    const wrapper = mount(JobActions, {
      props: {
        jobKey: 'test-job-key'
      }
    });

    // Call loadJobActions
    await wrapper.vm.loadJobActions();
    await wrapper.vm.$nextTick();

    // Should render 3 action buttons
    const buttons = wrapper.findAll('.el-button');
    expect(buttons).toHaveLength(3);

    // Check button labels
    expect(buttons[0].text()).toBe('Accept');
    expect(buttons[1].text()).toBe('Reject');
    expect(buttons[2].text()).toBe('Phone Call');
  });

  it('should apply correct button types based on response value', async () => {
    mockStore.dispatch.mockResolvedValue(mockApiResponse);

    const wrapper = mount(JobActions, {
      props: {
        jobKey: 'test-job-key'
      }
    });

    await wrapper.vm.loadJobActions();
    await wrapper.vm.$nextTick();

    const buttons = wrapper.findAll('.el-button');
    
    // Accept should be success type
    expect(buttons[0].classes()).toContain('el-button--success');
    
    // Reject should be danger type
    expect(buttons[1].classes()).toContain('el-button--danger');
    
    // Phone Call should be warning type
    expect(buttons[2].classes()).toContain('el-button--warning');
  });

  it('should show form dialog for actions with additional items', async () => {
    mockStore.dispatch.mockResolvedValue(mockApiResponse);

    const wrapper = mount(JobActions, {
      props: {
        jobKey: 'test-job-key'
      }
    });

    await wrapper.vm.loadJobActions();
    await wrapper.vm.$nextTick();

    // Click Accept button (has additional items)
    const acceptButton = wrapper.findAll('.el-button')[0];
    await acceptButton.trigger('click');

    // Should show dialog
    expect(wrapper.vm.showActionDialog).toBe(true);
    expect(wrapper.vm.currentAction.Display).toBe('Accept');
  });

  it('should execute action directly for actions without additional items', async () => {
    mockStore.dispatch.mockResolvedValue(mockApiResponse);

    const wrapper = mount(JobActions, {
      props: {
        jobKey: 'test-job-key'
      }
    });

    await wrapper.vm.loadJobActions();
    await wrapper.vm.$nextTick();

    // Click Phone Call button (no additional items)
    const phoneButton = wrapper.findAll('.el-button')[2];
    await phoneButton.trigger('click');

    // Should not show dialog
    expect(wrapper.vm.showActionDialog).toBe(false);
    
    // Should emit action event
    expect(wrapper.emitted('action')).toHaveLength(1);
    expect(wrapper.emitted('action')[0][0]).toEqual({
      jobKey: 'test-job-key',
      responseTag: 'Job',
      responseValue: 'PhoneCallRequested'
    });
  });

  it('should handle form data correctly', async () => {
    mockStore.dispatch.mockResolvedValue(mockApiResponse);

    const wrapper = mount(JobActions, {
      props: {
        jobKey: 'test-job-key'
      }
    });

    await wrapper.vm.loadJobActions();
    await wrapper.vm.$nextTick();

    // Click Accept button
    const acceptButton = wrapper.findAll('.el-button')[0];
    await acceptButton.trigger('click');

    // Check that form data is initialized
    expect(wrapper.vm.formData).toHaveProperty('ETA');
    expect(wrapper.vm.formData).toHaveProperty('Remarks');
    
    // Set form values
    wrapper.vm.formData.ETA = 15;
    wrapper.vm.formData.Remarks = 'Test remarks';

    // Mock form validation
    wrapper.vm.$refs.actionFormRef = {
      validate: vi.fn().mockResolvedValue(true)
    };

    // Execute action
    await wrapper.vm.executeAction();

    // Should emit action with additional data
    expect(wrapper.emitted('action')).toHaveLength(1);
    expect(wrapper.emitted('action')[0][0]).toEqual({
      jobKey: 'test-job-key',
      responseTag: 'Job',
      responseValue: 'Accept',
      additionalData: {
        ETA: 15,
        Remarks: 'Test remarks'
      }
    });
  });
});
