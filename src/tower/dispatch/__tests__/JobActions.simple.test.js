import { describe, it, expect } from 'vitest';

// Mock API response data
const mockApiResponse = {
  Result: "SUCCESS",
  Data: {
    Actions: [
      {
        Display: "Accept",
        ResponseTag: "Job",
        ResponseValue: "Accept",
        Description: "Accept the Job",
        AdditionalItems: [
          {
            Display: "ETA in Minutes",
            ResponseTag: "ETA",
            Required: "true",
            ResponseValue: "",
            ValueType: "Integer",
            Options: null
          },
          {
            Display: "Remarks",
            ResponseTag: "Remarks",
            Required: "false",
            ResponseValue: "",
            ValueType: "String",
            Options: null
          }
        ]
      },
      {
        Display: "Reject",
        ResponseTag: "Job", 
        ResponseValue: "Reject",
        Description: "Reject the Job",
        AdditionalItems: [
          {
            Display: "Reason",
            ResponseTag: "RejectReasonCode",
            Required: "true",
            ResponseValue: "",
            Options: [
              {
                Value: "OutOfArea",
                Description: "Out of Area"
              },
              {
                Value: "Weather",
                Description: "Weather"
              }
            ],
            ValueType: "String"
          }
        ]
      },
      {
        Display: "Phone Call",
        ResponseTag: "Job",
        ResponseValue: "PhoneCallRequested",
        Description: "Request a Phone Call for the Job"
      }
    ]
  }
};

describe('JobActions Data Structure', () => {
  it('should have correct structure for API response', () => {
    // Test the structure we expect from the API
    expect(mockApiResponse).toHaveProperty('Result', 'SUCCESS');
    expect(mockApiResponse).toHaveProperty('Data.Actions');
    expect(mockApiResponse.Data.Actions).toHaveLength(3);
    
    const acceptAction = mockApiResponse.Data.Actions[0];
    expect(acceptAction).toHaveProperty('Display', 'Accept');
    expect(acceptAction).toHaveProperty('ResponseTag', 'Job');
    expect(acceptAction).toHaveProperty('ResponseValue', 'Accept');
    expect(acceptAction).toHaveProperty('Description', 'Accept the Job');
    expect(acceptAction).toHaveProperty('AdditionalItems');
    expect(acceptAction.AdditionalItems).toHaveLength(2);
    
    const etaItem = acceptAction.AdditionalItems[0];
    expect(etaItem).toHaveProperty('Display', 'ETA in Minutes');
    expect(etaItem).toHaveProperty('ResponseTag', 'ETA');
    expect(etaItem).toHaveProperty('Required', 'true');
    expect(etaItem).toHaveProperty('ValueType', 'Integer');
  });

  it('should handle action without additional items', () => {
    const phoneAction = mockApiResponse.Data.Actions[2];
    expect(phoneAction).toHaveProperty('Display', 'Phone Call');
    expect(phoneAction).toHaveProperty('ResponseTag', 'Job');
    expect(phoneAction).toHaveProperty('ResponseValue', 'PhoneCallRequested');
    expect(phoneAction).not.toHaveProperty('AdditionalItems');
  });

  it('should handle options for dropdown fields', () => {
    const rejectAction = mockApiResponse.Data.Actions[1];
    const reasonItem = rejectAction.AdditionalItems[0];
    expect(reasonItem).toHaveProperty('Options');
    expect(reasonItem.Options).toHaveLength(2);
    expect(reasonItem.Options[0]).toHaveProperty('Value', 'OutOfArea');
    expect(reasonItem.Options[0]).toHaveProperty('Description', 'Out of Area');
  });
});
