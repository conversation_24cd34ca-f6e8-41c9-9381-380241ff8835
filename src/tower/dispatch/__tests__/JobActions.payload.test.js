import { describe, it, expect } from 'vitest';

describe('JobActions Payload Structure', () => {
  it('should build correct payload with additional data', () => {
    // Simulate form data from JobActions component
    const action = {
      ResponseTag: "Job",
      ResponseValue: "Accept",
      AdditionalItems: [
        { ResponseTag: "ETA", ResponseValue: "" },
        { ResponseTag: "Remarks", ResponseValue: "" },
        { ResponseTag: "MilesToVehicle", ResponseValue: "" },
        { ResponseTag: "MilesLoaded", ResponseValue: "" },
        { ResponseTag: "EstimatedPrice", ResponseValue: "" }
      ]
    };

    const formData = {
      ETA: 15,
      Remarks: "Roads are wet",
      MilesToVehicle: 10,
      MilesLoaded: 10,
      EstimatedPrice: 50
    };

    const jobKey = 1001;

    // Build payload like JobActions.vue does
    const actionPayload = {
      jobKey: jobKey,
      responseTag: action.ResponseTag,
      responseValue: action.ResponseValue
    };

    // Add additional items data if present
    if (action.AdditionalItems && action.AdditionalItems.length > 0) {
      action.AdditionalItems.forEach(item => {
        actionPayload[item.ResponseTag] = formData[item.ResponseTag];
      });
    }

    // Expected payload structure
    const expectedPayload = {
      jobKey: 1001,
      responseTag: "Job",
      responseValue: "Accept",
      ETA: 15,
      Remarks: "Roads are wet",
      MilesToVehicle: 10,
      MilesLoaded: 10,
      EstimatedPrice: 50
    };

    expect(actionPayload).toEqual(expectedPayload);
  });

  it('should build store API payload correctly', () => {
    const payload = {
      jobKey: 1001,
      responseTag: "Job",
      responseValue: "Accept",
      ETA: 15,
      Remarks: "Roads are wet",
      MilesToVehicle: 10,
      MilesLoaded: 10,
      EstimatedPrice: 50
    };

    // Simulate store module transformation
    const { jobKey, responseTag, responseValue, ...additionalData } = payload;
    
    const apiPayload = {
      Key: jobKey,
      ResponseTag: responseTag,
      ResponseValue: responseValue,
      ...additionalData
    };

    const expectedApiPayload = {
      Key: 1001,
      ResponseTag: "Job",
      ResponseValue: "Accept",
      ETA: 15,
      Remarks: "Roads are wet",
      MilesToVehicle: 10,
      MilesLoaded: 10,
      EstimatedPrice: 50
    };

    expect(apiPayload).toEqual(expectedApiPayload);
  });

  it('should handle payload without additional data', () => {
    const action = {
      ResponseTag: "Job",
      ResponseValue: "PhoneCallRequested"
      // No AdditionalItems
    };

    const jobKey = 1001;

    // Build payload like JobActions.vue does
    const actionPayload = {
      jobKey: jobKey,
      responseTag: action.ResponseTag,
      responseValue: action.ResponseValue
    };

    // No additional items to add

    const expectedPayload = {
      jobKey: 1001,
      responseTag: "Job",
      responseValue: "PhoneCallRequested"
    };

    expect(actionPayload).toEqual(expectedPayload);
  });
});
