<template>
<div id="record-view" data-page="lot" :data-search-mode="searchMode">
  <app-titlebar title="Edit Lot" v-if="!searchMode"></app-titlebar>

  <div class="content-section">
    <app-grid-form>
      <section class="_section-group">
        <form-section title="Lot" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-6 is-left">
              <app-text v-model="record.vc50Name" id="lot.vc50Name" :maxlength="50" :required="true">
                Name
              </app-text>
            </div>
            <div class="column is-3">
              <app-shortcode
                id="lot.lStorageLotTypeKey"
                v-model="record.lStorageLotTypeKey"
                :options="lotTypes"
                keyAlias="Key"
                valueAlias="Value"
                shortCodeAlias="ShortCode"
                activeAlias="Active"
                :required="true">
                Type
              </app-shortcode>
            </div>
            <div class="column is-3">
              <app-text v-model="record.ch5ShortCode" id="lot.ch5ShortCode" :maxlength="5" :required="true">
                Short Code
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <label>{{ TOPSCOMPANY__settings.vc15Label_Lot_UserDef1 }}</label>
              <input v-model="record.vc50UserDefined1" type="text" class="input" id="lot.vc50UserDefined1" maxlength="50" />
            </div>
            <div class="column is-6 is-bottom">
              <label>{{ TOPSCOMPANY__settings.vc15Label_Lot_UserDef2 }}</label>
              <input v-model="record.vc50UserDefined2" type="text" class="input" id="lot.vc50UserDefined2" maxlength="50" />
            </div>
          </div> <!-- /columns -->
        </form-section>

        <form-section title="Lot Address" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-12 is-left">
              <label>Address 1</label>
              <div class="field has-addons">
                <p class="control">
                  <input v-model="record.LotAddress.vc30Address1" type="text" class="input" id="lotAddress.vc30Address1" maxlength="30" />
                </p>
                <p class="control" style="width: 25px">
                  <a @click.prevent="toggleLotAddressSettings" tabindex="-1" class="button">
                    <i class="fal fa-map-marker-alt" :class="{ 'is-complete': lotAddressCoordinatesSet }"></i>
                  </a>
                </p>
              </div>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.LotAddress.vc30Address2" id="lotAddress.vc30Address2" :maxlength="30">
                Address 2
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.LotAddress.vc30City" id="lotAddress.vc30City" :maxlength="30">
                City
              </app-text>
            </div>
            <div class="column is-3">
              <app-select-state v-model="record.LotAddress.ch2StateKey" id="auctionAddress.ch2StateKey">
                State
              </app-select-state>
            </div>
            <div class="column is-3">
              <app-text v-model="record.LotAddress.vc10Zip" id="lotAddress.vc10Zip" :maxlength="10">
                Zip
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-text v-model="record.LotAddress.vc20Phone1" id="lotAddress.vc20Phone1" :maxlength="20">
                Phone
              </app-text>
            </div>
            <div class="column is-6 is-bottom">
              <app-text v-model="record.LotAddress.vc20Fax" id="lotAddress.vc20Fax" :maxlength="20">
                Fax
              </app-text>
            </div>
          </div> <!-- /columns -->
        </form-section>
      </section>

      <section class="_section-group">
        <form-section title="Auction Address" :fixed="true">
          <div class="columns is-multiline">
            <div class="column is-12 is-left">
              <label>Address 1</label>
              <div class="field has-addons">
                <p class="control">
                  <input v-model="record.AuctionAddress.vc30Address1" type="text" class="input" id="auctionAddress.vc30Address1" maxlength="30" />
                </p>
                <p class="control" style="width: 25px">
                  <a @click.prevent="toggleAuctionAddressSettings" tabindex="-1" class="button">
                    <i class="fal fa-map-marker-alt" :class="{ 'is-complete': auctionAddressCoordinatesSet }"></i>
                  </a>
                </p>
              </div>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="record.AuctionAddress.vc30Address2" id="auctionAddress.vc30Address2" :maxlength="30">
                Address 2
              </app-text>
            </div>
            <div class="column is-6 is-left">
              <app-text v-model="record.AuctionAddress.vc30City" id="auctionAddress.vc30City" :maxlength="30">
                City
              </app-text>
            </div>
            <div class="column is-3">
              <app-select-state v-model="record.AuctionAddress.ch2StateKey" id="auctionAddress.ch2StateKey">
                State
              </app-select-state>
            </div>
            <div class="column is-3">
              <app-text v-model="record.AuctionAddress.vc10Zip" id="auctionAddress.vc10Zip" :maxlength="10">
                Zip
              </app-text>
            </div>
            <div class="column is-6 is-left is-bottom">
              <app-text v-model="record.AuctionAddress.vc20Phone1" id="auctionAddress.vc20Phone1" :maxlength="20">
                Phone
              </app-text>
            </div>
            <div class="column is-6 is-bottom">
              <app-text v-model="record.AuctionAddress.vc20Fax" id="auctionAddress.vc20Fax" :maxlength="20">
                Fax
              </app-text>
            </div>
          </div>
        </form-section>
      </section>
    </app-grid-form>
  </div>

  <app-modal class="location" :show="lotAddressSettingsVisible" title="Lot Location" @close="toggleLotAddressSettings" :pad="false">
    <div class="_map">
      <gmap-map v-if="lotAddressMapVisible" :center="lotAddressMapCenter" :zoom="13" style="width: 100%; height: 100%">
        <gmap-marker :key="12345" :position="lotAddressMapMarker.position" :icon="lotAddressMapMarker.icon" :clickable="false" :draggable="false"></gmap-marker>
      </gmap-map>
      <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
    </div>

    <app-grid-form context="inline">
      <div class="_details columns is-multiline">
        <div class="column is-6">
          <app-text v-model="record.LotAddress.gcLatitude" id="lotAddress.gcLatitude">
            Latitude
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="record.LotAddress.gcLongitude" id="lotAddress.gcLongitude">
            Longitude
          </app-text>
        </div>
      </div>
    </app-grid-form>

    <div class="_actions">
      <button @click.prevent="geocodeLotAddress" :disabled="!lotAddressGeocodePossible" class="button">Geocode</button>
    </div>
  </app-modal>

  <app-modal class="location" :show="auctionAddressSettingsVisible" title="Auction Location" @close="toggleAuctionAddressSettings" :pad="false">
    <div class="_map">
      <gmap-map v-if="auctionAddressMapVisible" :center="auctionAddressMapCenter" :zoom="13" style="width: 100%; height: 100%">
        <gmap-marker :key="12346" :position="auctionAddressMapMarker.position" :icon="auctionAddressMapMarker.icon" :clickable="false" :draggable="false"></gmap-marker>
      </gmap-map>
      <app-placeholder v-else>Set a valid location to view the map.</app-placeholder>
    </div>

    <app-grid-form context="inline">
      <div class="_details columns is-multiline">
        <div class="column is-6">
          <app-text v-model="record.AuctionAddress.gcLatitude" id="auctionAddress.gcLatitude">
            Latitude
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="record.AuctionAddress.gcLongitude" id="auctionAddress.gcLongitude">
            Longitude
          </app-text>
        </div>
      </div>
    </app-grid-form>

    <div class="_actions">
      <button @click.prevent="geocodeAuctionAddress" :disabled="!auctionAddressGeocodePossible" class="button">Geocode</button>
    </div>
  </app-modal>

  <app-footerbar v-if="!searchMode">
    <template slot="left">
      <app-button type="primary" @click="saveLot" :disabled="!canEditLot">Save</app-button>
      <app-button v-if="!isNewRecord" type="default" @click="duplicate" :disabled="!canEditLot">Duplicate</app-button>
      <app-button type="default" @click="cancel">Close</app-button>
    </template>

    <app-button v-if="isDeletable" type="danger" @click="deleteRecord" :disabled="!canDeleteLot">Delete</app-button>
    <app-button v-if="isUndeletable" type="default" @click="undeleteRecord" :disabled="!canDeleteLot">Undelete</app-button>
  </app-footerbar>
</div> <!-- /lot-view -->
</template>

<script>
import is from 'is_js';
import { get } from 'lodash-es';
import Access from '@/utils/access.js';
import { mapActions } from 'vuex';
import RecordView from '@/components/ancestors/RecordView.vue';

export default {
  name: 'lot-view',

  extends: RecordView,

  data () {
    return {
      lotTypes: [],
      lotAddressSettingsVisible: false,
      auctionAddressSettingsVisible: false,
      viewConfig: {
        noun: 'StorageLot',
        recordKeyName: 'lStorageLotKey',
        returnRouteName: 'Lots',
        addRouteName: 'AddLot'
      },
      record: {
        lStorageLotKey: '',
        vc50Name: '',
        ch5ShortCode: '',
        lStorageLotTypeKey: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        LotAddress: {
          vc30Name1: '',
          vc30Name2: '',
          vc30Address1: '',
          vc30Address2: '',
          vc30City: '',
          ch2StateKey: '',
          vc10Zip: '',
          vc20Phone1: '',
          vc20Phone2: '',
          vc20Fax: '',
          vc50Email: '',
          vc50UserDefined1: '',
          vc50UserDefined2: '',
          gcLatitude: 0,
          gcLongitude: 0,
          dCreated: '',
          lUserKeyCreatedBy: ''
        },
        AuctionAddress: {
          vc30Name1: '',
          vc30Name2: '',
          vc30Address1: '',
          vc30Address2: '',
          vc30City: '',
          ch2StateKey: '',
          vc10Zip: '',
          vc20Phone1: '',
          vc20Phone2: '',
          vc20Fax: '',
          vc50Email: '',
          vc50UserDefined1: '',
          vc50UserDefined2: '',
          gcLatitude: 0,
          gcLongitude: 0,
          dCreated: '',
          lUserKeyCreatedBy: ''
        }
      }
    };
  },

  computed: {
    canDeleteLot () {
      return Access.has('lots.delete');
    },

    canEditLot () {
      return Access.has('lots.edit');
    },

    lotAddressCoordinatesSet () {
      return is.all.truthy([
        parseInt(get(this.record.LotAddress, 'gcLatitude', 0)),
        parseInt(get(this.record.LotAddress, 'gcLongitude', 0))
      ]);
    },

    auctionAddressCoordinatesSet () {
      return is.all.truthy([
        parseInt(get(this.record.AuctionAddress, 'gcLatitude', 0)),
        parseInt(get(this.record.AuctionAddress, 'gcLongitude', 0))
      ]);
    },

    lotAddressMapVisible () {
      return this.lotAddressCoordinatesSet && this.lotAddressSettingsVisible;
    },

    auctionAddressMapVisible () {
      return this.auctionAddressCoordinatesSet && this.auctionAddressSettingsVisible;
    },

    lotAddressMapCenter () {
      return {
        lat: parseFloat(get(this.record.LotAddress, 'gcLatitude', 0)),
        lng: parseFloat(get(this.record.LotAddress, 'gcLongitude', 0))
      };
    },

    auctionAddressMapCenter () {
      return {
        lat: parseFloat(get(this.record.AuctionAddress, 'gcLatitude', 0)),
        lng: parseFloat(get(this.record.AuctionAddress, 'gcLongitude', 0))
      };
    },

    lotAddressMapMarker () {
      return {
        position: {
          lat: parseFloat(get(this.record.LotAddress, 'gcLatitude', 0)),
          lng: parseFloat(get(this.record.LotAddress, 'gcLongitude', 0))
        },
        icon: { url: '/static/car-marker.svg' }
      };
    },

    auctionAddressMapMarker () {
      return {
        position: {
          lat: parseFloat(get(this.record.AuctionAddress, 'gcLatitude', 0)),
          lng: parseFloat(get(this.record.AuctionAddress, 'gcLongitude', 0))
        },
        icon: { url: '/static/car-marker.svg' }
      };
    },

    lotAddressGeocodePossible () {
      return get(this.record.LotAddress, 'vc30Address1').length > 4;
    },

    auctionAddressGeocodePossible () {
      return get(this.record.AuctionAddress, 'vc30Address1').length > 4;
    },

    shouldStripLotAddress () {
      return is.all.falsy([
        get(this.record.LotAddress, 'vc30Address1', ''),
        get(this.record.LotAddress, 'vc30Address2', ''),
        get(this.record.LotAddress, 'vc30City', ''),
        get(this.record.LotAddress, 'ch2StateKey', ''),
        get(this.record.LotAddress, 'vc10Zip', ''),
        get(this.record.LotAddress, 'vc20Phone1', ''),
        get(this.record.LotAddress, 'vc20Fax', ''),
        get(this.record.LotAddress, 'gcLatitude', ''),
        get(this.record.LotAddress, 'gcLongitude', '')
      ]);
    },

    shouldStripAuctionAddress () {
      return is.all.falsy([
        get(this.record.AuctionAddress, 'vc30Address1', ''),
        get(this.record.AuctionAddress, 'vc30Address2', ''),
        get(this.record.AuctionAddress, 'vc30City', ''),
        get(this.record.AuctionAddress, 'ch2StateKey', ''),
        get(this.record.AuctionAddress, 'vc10Zip', ''),
        get(this.record.AuctionAddress, 'vc20Phone1', ''),
        get(this.record.AuctionAddress, 'vc20Fax', ''),
        get(this.record.AuctionAddress, 'gcLatitude', ''),
        get(this.record.AuctionAddress, 'gcLongitude', '')
      ]);
    },

    lotAddressTemplate () {
      return {
        vc30Name1: '',
        vc30Name2: '',
        vc30Address1: '',
        vc30Address2: '',
        vc30City: '',
        ch2StateKey: '',
        vc10Zip: '',
        vc20Phone1: '',
        vc20Phone2: '',
        vc20Fax: '',
        vc50Email: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        gcLatitude: 0,
        gcLongitude: 0,
        dCreated: '',
        lUserKeyCreatedBy: ''
      };
    },

    auctionAddressTemplate () {
      return {
        vc30Name1: '',
        vc30Name2: '',
        vc30Address1: '',
        vc30Address2: '',
        vc30City: '',
        ch2StateKey: '',
        vc10Zip: '',
        vc20Phone1: '',
        vc20Phone2: '',
        vc20Fax: '',
        vc50Email: '',
        vc50UserDefined1: '',
        vc50UserDefined2: '',
        gcLatitude: 0,
        gcLongitude: 0,
        dCreated: '',
        lUserKeyCreatedBy: ''
      };
    }
  },

  methods: {
    ...mapActions([
      'LOT__getTypes',
      'MAP__getCoordinates'
    ]),

    saveLot () {
      if (this.shouldStripLotAddress) {
        this.$delete(this.record, 'LotAddress');
      }

      if (this.shouldStripAuctionAddress) {
        this.$delete(this.record, 'AuctionAddress');
      }

      this.save();
    },

    toggleLotAddressSettings () {
      this.lotAddressSettingsVisible = !this.lotAddressSettingsVisible;
    },

    toggleAuctionAddressSettings () {
      this.auctionAddressSettingsVisible = !this.auctionAddressSettingsVisible;
    },

    geocodeLotAddress () {
      let fullAddress = [
        get(this.record.LotAddress, 'vc30Address1', ''),
        get(this.record.LotAddress, 'vc30City', ''),
        get(this.record.LotAddress, 'ch2StateKey', ''),
        get(this.record.LotAddress, 'vc10Zip', '')
      ].join(' ');

      this.MAP__getCoordinates({
        location: fullAddress,
        callback: response => {
          if (is.all.falsy([response.Latitude, response.Longitude])) {
            return false;
          }

          this.$set(this.record.LotAddress, 'gcLatitude', response.Latitude);
          this.$set(this.record.LotAddress, 'gcLongitude', response.Longitude);
        }
      });
    },

    geocodeAuctionAddress () {
      let fullAddress = [
        get(this.record.AuctionAddress, 'vc30Address1', ''),
        get(this.record.AuctionAddress, 'vc30City', ''),
        get(this.record.AuctionAddress, 'ch2StateKey', ''),
        get(this.record.AuctionAddress, 'vc10Zip', '')
      ].join(' ');

      this.MAP__getCoordinates({
        location: fullAddress,
        callback: response => {
          if (is.all.falsy([response.Latitude, response.Longitude])) {
            return false;
          }

          this.$set(this.record.AuctionAddress, 'gcLatitude', response.Latitude);
          this.$set(this.record.AuctionAddress, 'gcLongitude', response.Longitude);
        }
      });
    },

    afterGetViewData () {
      this.createIfMissing('LotAddress', this.lotAddressTemplate);
      this.createIfMissing('AuctionAddress', this.auctionAddressTemplate);
    }
  },

  mounted () {
    this.LOT__getTypes({
      callback: response => {
        this.lotTypes = response;
      }
    });
  }
};
</script>
