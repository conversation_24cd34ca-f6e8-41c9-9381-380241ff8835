import StandardLayout from '@/layouts/Standard.vue';
import { ACCESS_CODE, PRODUCTKEY_ADMINISTRATION } from '@/config';

const administrationRoutes = {
  path: '/administration',
  components: { default: StandardLayout },
  meta: { product: PRODUCTKEY_ADMINISTRATION },
  children: [
    { 
      path: 'users', 
      label: 'Users', 
      name: 'Users', 
      component: () => import('@/components/administration/Users.vue'), 
      meta: { 
        navigatorContext: 'primary', 
        accessRight: ACCESS_CODE.users.manage 
      } 
    },
    { 
      path: 'txi-customers', 
      label: 'TXI Customers', 
      name: 'TxiCustomers', 
      component: () => import('@/components/administration/TxiCustomers.vue'), 
      meta: { 
        navigatorContext: 'primary', 
        accessRight: ACCESS_CODE.txiCustomer.manage 
      } 
    },
    { 
      path: 'txi-customers/create', 
      name: 'TxiCustomerCreate', 
      component: () => import('@/components/administration/TxiCustomer.vue'), 
      meta: { 
        navigatorContext: null, 
        accessRight: ACCESS_CODE.txiCustomer.manage 
      } 
    },
    { 
      path: 'txi-customers/:key/edit', 
      name: 'TxiCustomerEdit', 
      component: () => import('@/components/administration/TxiCustomer.vue'), 
      meta: { 
        navigatorContext: null, 
        accessRight: ACCESS_CODE.txiCustomer.manage 
      } 
    },
    { 
      path: 'utilities', 
      label: 'Utilities', 
      name: 'Utilities', 
      component: () => import('@/components/administration/Utilities.vue'), 
      meta: { 
        navigatorContext: 'primary', 
        accessRight: ACCESS_CODE.txiCustomer.manage 
      } 
    }
  ]
};

export default administrationRoutes;
