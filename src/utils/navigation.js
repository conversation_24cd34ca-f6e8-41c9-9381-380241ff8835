import { get, find, forEach, includes } from 'lodash-es';
import routes from '@/router';
import { PRODUCTS } from '@/config';
import store from '@/store/index.js';

export function getAllowedRoutes () {
  let views = [];
  const productKey = get(store.state, 'product.key', -1);
  const orgUnitKey = get(store.state, 'orgUnitKey', -1);
  const rights = get(store.state, 'role.Rights', []);

  if (!productKey) { return views; }

  const productRoute = find(routes, { meta: { product: productKey } });

  if (!productRoute) { return views; }

  if (PRODUCTS[productKey].orgUnitRequired && !orgUnitKey) { return views; }

  forEach(get(productRoute, 'children', []), child => {
    const navigatorContext = get(child, 'meta.navigatorContext', null);
    const accessRight = get(child, 'meta.accessRight', null);
    const negativeRight = get(child, 'meta.negativeRight', null);

    if (!navigatorContext) return;
    if (includes(rights, negativeRight)) return;
    if (!includes(rights, accessRight)) return;

    views.push({
      label: child.label,
      path: productRoute.path + '/' + child.path,
      meta: {
        navigatorContext,
        inNavigator: child.meta.inNavigator,
        inToolbar: child.meta.inToolbar
      }
    });
  });

  return views;
}
