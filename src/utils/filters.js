import is from 'is_js';
import numeral from 'numeral';

import parse from 'date-fns/parse';
import format from 'date-fns/format';
import distanceInWords from 'date-fns/distance_in_words';

import { forEach, isEmpty, toNumber, isString, toString } from 'lodash-es';

export function towSale (value) {
  return value === '1' ? 'Tow' : 'Sale';
};

export function json (value) {
  if (isEmpty(value)) return value;

  return JSON.stringify(value, undefined, 2);
};

export function relativeDate (value) {
  if (isEmpty(value)) return value;

  return distanceInWords(new Date(), value);
};

export function verbalDate (value) {
  if (isEmpty(value)) return value;

  return format(value, 'MMMM D, YYYY');
};

export function verbalTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'h:mm A');
};

export function simpleDate (value) {
  if (isEmpty(value)) return value;

  return format(value, 'MM/DD/YYYY');
};

export function simpleDateTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'M/D/YY h:mma');
};

export function fullDateTime (value) {
  if (isEmpty(value)) return value;

  return format(value, 'M/D/YYYY HH:mm:ss');
};

export function storifyDate (value) {
  if (isEmpty(value)) return value;

  if (isString(value)) value = parse(value);

  value = format(value, 'YYYY-MM-DD HH:mm:ss');

  return value;
};

export function affirmative (value) {
  if (is.inArray(toString(value), ['1', 'true'])) return 'Yes';

  return 'No';
};

export function wholeNumber (value, length = 0) {
  return toNumber(value).toFixed(length);
};

export function usd (value, language = 'en-US') {
  return numeral(value).format('$0,0.00');
};

export function prepareApiData (data) {
  forEach(data, (value, key) => {
    if (is.array(value) || is.object(value)) {   // Item is an object or array so go deep
      if (is.date(value)) {
        data[key] = storifyDate(value);
      } else {
        prepareApiData(value);
      }
    } else {
      if (key.length > 2) {
        let firstCharacter = key[0];
        if (firstCharacter === 'd' && value !== '') {
          data[key] = storifyDate(value);
        }
      }
    }
  });
  return data;
};

export function castDataTypes(data) {
  const rateKeys = ['scInitialRate', 'scSecondaryRate', 'scTertiaryRate'];
  const numberKeys = ['bOwnerWithVehicle'];
  const trueValues = ['true', '1'];
  const numericPrefixes = ['p', 't', 'c', 'f', 'l', 'i'];

  forEach(data, (value, key) => {
    // Recurse into objects and arrays
    if (is.array(value) || is.object(value)) {
      castDataTypes(value);
      return;
    }

    if (!is.string(value) || key.length <= 2 || value === '') {
      return;
    }

    if (rateKeys.includes(key)) {
      // Convert rate strings to fixed precision
      data[key] = parseFloat(value).toFixed(2);
    } else if (numberKeys.includes(key)) {
      // Explicit numeric flags
      data[key] = Number(value);
    } else if (key.startsWith('b')) {
      // Boolean flags
      data[key] = trueValues.includes(value);
    } else if (key.startsWith('j')) {
      // JSON content
      try {
        data[key] = JSON.parse(value);
      } catch {
        data[key] = value;
      }
    } else if (
      numericPrefixes.includes(key[0]) &&
      !(key.startsWith('ch'))
    ) {
      // Generic numeric conversion
      data[key] = toNumber(value);
    }
  });

  return data;
}
