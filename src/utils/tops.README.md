# TOPS API Utility

A concise, namespace-based utility for interacting with the TOPS API.

## Design

This utility provides a clean, intuitive interface for making API requests to the TOPS API. It uses a namespace-based approach where:

- The top-level object `tops` represents the API
- Each noun (Call, Customer, Employee, etc.) is a property of the `tops` object
- Each verb (Read, Create, Update, etc.) is a method on the noun object

This design allows for a very concise and readable syntax:

```javascript
// Read a call
const response = await tops.Call.Read({ lCallKey: 12345 });
const call = response.data;

// Get customer types
const response = await tops.Customer.GetTypes();
const types = response.data;
```

## Features

- **Concise Syntax**: Clean, intuitive API with minimal boilerplate
- **Namespace Organization**: Organized by noun and verb for easy discovery
- **Promise-Based**: Fully supports async/await for modern JavaScript
- **Raw Response**: Returns the full axios response for maximum flexibility
- **Error Handling**: Consistent error handling with detailed information
- **Debug Logging**: Automatic logging in debug mode
- **Type Safety**: Provides good IDE autocompletion support

## Usage

### Basic Usage

```javascript
import tops from '@/api/tops-api/tops';

// Read a call
const response = await tops.Call.Read({ lCallKey: 12345 });
const call = response.data;

// Create a call
const createResponse = await tops.Call.Create({
  CustomerKey: 67890,
  StatusKey: 110,
  // ... other call data
});
const newCall = createResponse.data;

// Update a call
const updateResponse = await tops.Call.Update({
  Key: 12345,
  StatusKey: 115,
  // ... other call data
});
const updatedCall = updateResponse.data;
```

### Parallel Requests

```javascript
// Load multiple resources in parallel
const [callResponse, statusesResponse, customersResponse] = await Promise.all([
  tops.Call.Read({ lCallKey: 12345 }),
  tops.Call.GetStatuses({}),
  tops.Call.GetCustomers({})
]);

const call = callResponse.data;
const statuses = statusesResponse.data;
const customers = customersResponse.data;
```

### File Downloads

```javascript
// Export to CSV
const csvResponse = await tops.Call.GetRecordsViewDataAsCSVFile({
  Filters: [/* ... */]
});

// Handle the CSV download manually
const link = document.createElement('a');
link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvResponse.data);
link.download = 'calls.csv';
link.click();

// Export to PDF
const pdfResponse = await tops.Call.GetRecordsViewDataAsPDF({
  Filters: [/* ... */]
});

// Handle the PDF download manually
const blob = new Blob([pdfResponse.data], { type: 'application/pdf' });
const url = URL.createObjectURL(blob);
const link = document.createElement('a');
link.href = url;
link.download = 'calls.pdf';
link.click();
URL.revokeObjectURL(url);
```

### Error Handling

```javascript
try {
  const response = await tops.Call.Read({ lCallKey: 12345 });
  const call = response.data;
  // Process call data
} catch (error) {
  console.error('Error reading call:', error);
  // Handle error
}
```

## In Vue Components

```javascript
export default {
  data() {
    return {
      call: null,
      statuses: [],
      loading: false
    };
  },

  async mounted() {
    await this.loadCall();
  },

  methods: {
    async loadCall() {
      this.loading = true;

      try {
        // Load call and statuses in parallel
        const [callResponse, statusesResponse] = await Promise.all([
          tops.Call.Read({ lCallKey: this.$route.params.id }),
          tops.Call.GetStatuses({})
        ]);

        this.call = callResponse.data;
        this.statuses = statusesResponse.data;
      } catch (error) {
        this.$message.error('Failed to load call: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    async saveCall() {
      try {
        const response = await tops.Call.Update(this.call);
        this.call = response.data;
        this.$message.success('Call saved successfully');
      } catch (error) {
        this.$message.error('Failed to save call: ' + error.message);
      }
    }
  }
};
```

## In Vuex Store

```javascript
import tops from '@/api/tops-api/tops';

const actions = {
  async CALL_read({ commit }, { callKey }) {
    try {
      const response = await tops.Call.Read({ lCallKey: callKey });
      const call = response.data;
      commit('SET_CALL', call);
      return call;
    } catch (error) {
      console.error('Error reading call:', error);
      throw error;
    }
  },

  async CALL_getStatuses({ commit }) {
    try {
      const response = await tops.Call.GetStatuses({});
      const statuses = response.data;
      commit('SET_CALL_STATUSES', statuses);
      return statuses;
    } catch (error) {
      console.error('Error getting call statuses:', error);
      throw error;
    }
  }
};
```

## Advanced Options

The second parameter to each verb method is an options object that supports:

- `lastRead`: String - LastRead timestamp for optimistic concurrency control

Examples:

```javascript
// Update with LastRead for concurrency control
const updateResponse = await tops.Call.Update({
  Key: 12345,
  StatusKey: 115,
  // ... other call data
}, {
  lastRead: '2023-12-01T10:30:00Z'
});
```

## Available Nouns

The API dynamically creates namespaces for any noun you request, so you can use any noun supported by the TOPS API:

- Call
- Customer
- Employee
- Image
- Label
- Lien
- Lot
- Map
- Price
- Truck
- etc.

## Available Verbs

Each noun supports the verbs available for that resource in the TOPS API. For example, the Call noun supports:

- Create
- CreateFilterClauses
- GetAllModels
- GetBodyTypes
- GetCancelReasons
- GetColors
- GetCustomers
- GetDispatchDetails
- GetFilterValueOptions
- GetFinalDispositions
- GetMakes
- GetMetadata
- GetModels
- GetNewDefaults
- GetPossibleActions
- GetReasons
- GetRecordViewData
- GetRecordViewSettings
- GetRecordsViewData
- GetRecordsViewDataAsCSVFile
- GetRecordsViewSettings
- GetSnapshotViewData
- GetSources
- GetStatuses
- GetSubterminals
- GetTowTypes
- GetTruckTypes
- HandleAction
- Read
- SaveRecordsViewSettings
- Update
