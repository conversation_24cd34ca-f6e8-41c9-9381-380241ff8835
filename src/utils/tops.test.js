import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import { storifyDate } from '@/utils/filters';

// Mock dependencies
vi.mock('axios');
vi.mock('@/utils/filters');
vi.mock('@/store', () => ({
  default: {
    state: {
      product: { key: 'test-product-key' },
      orgUnitKey: 'test-org-unit',
      appMode: 'TEST',
      user: { Key: 'test-user-key' },
      instance: {
        Key: 'test-instance-key',
        Authentication: 'test-auth-key'
      }
    },
    getters: {
      __state: { appMode: 'TEST' }
    }
  }
}));

describe('TOPS API Client', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock storifyDate
    storifyDate.mockImplementation((date) => date || '');

    // Mock axios response
    axios.mockResolvedValue({
      data: { Result: 'SUCCESS', Data: { test: 'data' } }
    });
  });

  describe('LastRead support', () => {
    it('should include empty LastRead when no lastRead option is provided', async () => {
      // Import tops after mocks are set up
      const tops = (await import('./tops.js')).default;

      await tops.Call.Read({ lCallKey: 12345 });

      expect(axios).toHaveBeenCalledWith({
        method: 'post',
        headers: {
          'X-txi-api': 'App:TOPS Browser,Version:2025.6.3,Noun:Call,Verb:Read'
        },
        url: 'https://apiqa.towxchange.net/v3/index.php',
        data: {
          Operation: {
            Noun: 'Call',
            Verb: 'Read',
            ProductKey: 'test-product-key',
            OrgUnitKey: 'test-org-unit',
            Mode: 'TEST',
            ResponseData: 'JSON',
            LastRead: ''
          },
          Authentication: {
            UserKey: 'test-user-key',
            InstanceKey: 'test-instance-key',
            AuthenticationKey: 'test-auth-key'
          },
          Data: {
            Parameters: {
              IncludeHeader: false,
              NameValuePairs: true
            },
            lCallKey: 12345
          }
        },
        timeout: 60000
      });
    });

    it('should include LastRead when lastRead option is provided', async () => {
      const tops = (await import('./tops.js')).default;
      const testLastRead = '2023-12-01T10:30:00Z';
      storifyDate.mockReturnValue(testLastRead);

      await tops.Call.Update({
        Key: 12345,
        StatusKey: 115
      }, {
        lastRead: testLastRead
      });

      expect(storifyDate).toHaveBeenCalledWith(testLastRead);
      expect(axios).toHaveBeenCalledWith({
        method: 'post',
        headers: {
          'X-txi-api': 'App:TOPS Browser,Version:2025.6.3,Noun:Call,Verb:Update'
        },
        url: 'https://apiqa.towxchange.net/v3/index.php',
        data: {
          Operation: {
            Noun: 'Call',
            Verb: 'Update',
            ProductKey: 'test-product-key',
            OrgUnitKey: 'test-org-unit',
            Mode: 'TEST',
            ResponseData: 'JSON',
            LastRead: testLastRead
          },
          Authentication: {
            UserKey: 'test-user-key',
            InstanceKey: 'test-instance-key',
            AuthenticationKey: 'test-auth-key'
          },
          Data: {
            Parameters: {
              IncludeHeader: false,
              NameValuePairs: true
            },
            Key: 12345,
            StatusKey: 115
          }
        },
        timeout: 60000
      });
    });

    it('should handle empty string lastRead option', async () => {
      const tops = (await import('./tops.js')).default;
      storifyDate.mockReturnValue('');

      await tops.Call.Update({
        Key: 12345
      }, {
        lastRead: ''
      });

      expect(storifyDate).toHaveBeenCalledWith('');
      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            Operation: expect.objectContaining({
              LastRead: ''
            })
          })
        })
      );
    });

    it('should work with other options alongside lastRead', async () => {
      const tops = (await import('./tops.js')).default;
      const testLastRead = '2023-12-01T10:30:00Z';
      storifyDate.mockReturnValue(testLastRead);

      await tops.Call.GetRecordsViewDataAsPDF({
        Filters: []
      }, {
        lastRead: testLastRead,
        responseType: 'blob'
      });

      expect(storifyDate).toHaveBeenCalledWith(testLastRead);
      expect(axios).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            Operation: expect.objectContaining({
              LastRead: testLastRead
            })
          })
        })
      );
    });
  });
});
