import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import tops from '../tops.js';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock store
vi.mock('@/store', () => ({
  default: {
    state: {
      product: { key: 'test-product' },
      orgUnitKey: 'test-org',
      appMode: 'TEST',
      user: { Key: 'test-user' },
      instance: { Key: 'test-instance', Authentication: 'test-auth' }
    },
    getters: {
      __state: { appMode: 'TEST' }
    }
  }
}));

// Mock config
vi.mock('@/config', () => ({
  VERSION: '1.0.0'
}));

// Mock filters
vi.mock('@/utils/filters', () => ({
  storifyDate: vi.fn((date) => date || '')
}));

describe('TOPS API Client', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockedAxios.post.mockResolvedValue({ data: 'test response' });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should make a basic request', async () => {
    const response = await tops.Call.Get({ CallKey: 123 });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      expect.any(String), // API URL
      expect.objectContaining({
        Operation: expect.objectContaining({
          Noun: 'Call',
          Verb: 'Get'
        }),
        Data: expect.objectContaining({
          CallKey: 123
        })
      }),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-txi-api': expect.stringContaining('Noun:Call,Verb:Get')
        }),
        signal: expect.any(AbortSignal)
      })
    );

    expect(response).toEqual({ data: 'test response' });
  });

  it('should support custom AbortSignal', async () => {
    const controller = new AbortController();

    await tops.Call.Get({ CallKey: 123 }, { signal: controller.signal });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.any(Object),
      expect.objectContaining({
        signal: controller.signal
      })
    );
  });

  it('should handle request cancellation', async () => {
    const abortError = new Error('Request aborted');
    abortError.name = 'AbortError';
    mockedAxios.post.mockRejectedValue(abortError);

    await expect(tops.Call.Get({ CallKey: 123 })).rejects.toThrow('Request cancelled: Call.Get');
  });

  it('should deduplicate identical requests', async () => {
    // Start two identical requests simultaneously
    const promise1 = tops.Call.Get({ CallKey: 123 });
    const promise2 = tops.Call.Get({ CallKey: 123 });

    await Promise.allSettled([promise1, promise2]);

    // Should only make one actual HTTP request due to deduplication
    expect(mockedAxios.post).toHaveBeenCalledTimes(1);
  });

  it('should not deduplicate different requests', async () => {
    await Promise.all([
      tops.Call.Get({ CallKey: 123 }),
      tops.Call.Get({ CallKey: 456 })
    ]);

    // Should make two separate requests
    expect(mockedAxios.post).toHaveBeenCalledTimes(2);
  });

  it('should include lastRead parameter when provided', async () => {
    await tops.Call.Get({ CallKey: 123 }, { lastRead: '2023-01-01' });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        Operation: expect.objectContaining({
          LastRead: '2023-01-01'
        })
      }),
      expect.any(Object)
    );
  });

  it('should make heartbeat requests correctly', async () => {
    await tops.Instance.Heartbeat({ viewName: 'TestView' });

    expect(mockedAxios.post).toHaveBeenCalledWith(
      expect.any(String),
      expect.objectContaining({
        Operation: expect.objectContaining({
          Noun: 'Instance',
          Verb: 'Heartbeat'
        }),
        Data: expect.objectContaining({
          viewName: 'TestView'
        })
      }),
      expect.objectContaining({
        headers: expect.objectContaining({
          'X-txi-api': expect.stringContaining('Noun:Instance,Verb:Heartbeat')
        })
      })
    );
  });
});
