import is from 'is_js';
import axios from 'axios';
import { map, toString } from 'lodash-es';
import { PLACES_API_URLS } from '@/config';

export function suggestPlaces (queryString, callback) {
  if (is.under(queryString.length, 5)) {
    return [];
  }

  let parameters = {
    'query': queryString,
    'hitsPerPage': 10,
    'countries': 'us,ca'
  };

  axios.post(PLACES_API_URLS[1], parameters)
    .then(response => {
      if (is.not.equal(response.status, 200)) {
        throw response;
      }

      let locations = [];

      if (is.above(response.data.hits.length, 0)) {
        locations = map(response.data.hits, hit => {
          let location = {};

          location = {
            street: (is.existy(hit.locale_names)) ? toString(hit.locale_names.default) : '',
            city: (is.existy(hit.city)) ? toString(hit.city.default) : '',
            state: (is.existy(hit.administrative)) ? toString(hit.administrative) : '',
            postcode: (is.existy(hit.postcode)) ? toString(hit.postcode[0]) : '',
            country: (is.existy(hit.country)) ? hit.country.default : '',
            latitude: (is.existy(hit._geoloc)) ? hit._geoloc.lat : '',
            longitude: (is.existy(hit._geoloc)) ? hit._geoloc.lng : ''
          };

          location.locale = `${location.street}, ${location.city}, ${location.state} ${location.postcode}`;

          return location;
        });
      }

      callback(locations);
    })
    .catch(function (error) {
      console.error('suggestPlaces API', error);
    });
};
