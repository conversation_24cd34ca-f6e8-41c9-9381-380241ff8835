import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import AppBadge from './AppBadge.vue'

describe('AppBadge', () => {
  it('renders with default props', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 5
      }
    })
    
    expect(wrapper.text()).toBe('5')
    expect(wrapper.classes()).toContain('app-badge--primary')
    expect(wrapper.classes()).toContain('app-badge--default')
  })

  it('shows nothing when count is 0 by default', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 0
      }
    })
    
    expect(wrapper.isVisible()).toBe(false)
    expect(wrapper.text()).toBe('')
  })

  it('shows zero when show<PERSON>ero is true', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 0,
        showZero: true
      }
    })
    
    expect(wrapper.text()).toBe('0')
  })

  it('shows max+ when count exceeds max', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 150,
        max: 99
      }
    })
    
    expect(wrapper.text()).toBe('99+')
  })

  it('displays as dot when dot prop is true', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        dot: true
      }
    })
    
    expect(wrapper.classes()).toContain('app-badge--dot')
    expect(wrapper.text()).toBe('')
  })

  it('applies custom color', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 5,
        color: '#ff0000'
      }
    })
    
    expect(wrapper.attributes('style')).toContain('background-color: rgb(255, 0, 0)')
  })

  it('applies different variants', () => {
    const variants = ['primary', 'secondary', 'success', 'warning', 'danger', 'info']
    
    variants.forEach(variant => {
      const wrapper = mount(AppBadge, {
        propsData: {
          count: 5,
          variant
        }
      })
      
      expect(wrapper.classes()).toContain(`app-badge--${variant}`)
    })
  })

  it('applies different sizes', () => {
    const sizes = ['small', 'default', 'large']
    
    sizes.forEach(size => {
      const wrapper = mount(AppBadge, {
        propsData: {
          count: 5,
          size
        }
      })
      
      expect(wrapper.classes()).toContain(`app-badge--${size}`)
    })
  })

  it('handles non-numeric count', () => {
    const wrapper = mount(AppBadge, {
      propsData: {
        count: 'NEW'
      }
    })
    
    expect(wrapper.text()).toBe('NEW')
  })
})
