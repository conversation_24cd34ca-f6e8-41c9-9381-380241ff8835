<template>
  <div id="images-section-thing">
    <div class="files-menu">
      <button title="Add files" v-if="canAdd" @click="toggleUploadAreaVisibility">
        <template v-if="isUploadAreaVisible">
          <i class="far fa-times"></i> Cancel
        </template>
        <template v-else>
          <i class="far fa-plus"></i> Add
        </template>
      </button>

      <tab-group v-model="viewLayout" v-if="canView">
        <tab-item value="grid">
          <i class="far fa-grid-2"></i>
        </tab-item>
        <tab-item value="list">
          <i class="far fa-list"></i>
        </tab-item>
      </tab-group>

      <div class="flex-space"></div>

      <button id="settings-trigger" popovertarget="settings-popover" @click="setPopoverPosition">
        <i class="far fa-circle-ellipsis"></i>
      </button>
    </div>

    <UploadArea v-if="canAdd && isUploadAreaVisible" :callKey="call.lCallKey"
      @toggleVisibility="toggleUploadAreaVisibility" ref="uploadAreaRef" />

    <FileBrowser v-if="canView && !isUploadAreaVisible" :viewLayout="viewLayout" :allFiles="filteredFiles"
      :canDelete="canDelete" @refresh="onRefreshFilesClicked" ref="fileBrowserRef" />

    <div id="settings-popover" popover anchor="settings-trigger" :style="{
      '--top': popover.position.top + 'px',
      '--left': popover.position.left + 'px'
    }">

      <ul class="settings-list" v-if="allTags.length > 1">
        <li class="settings-header" @click="filter.tag = null">
          Tags
        </li>
        <li class="settings-item" @click="filter.tag = null">
          <i class="settings-icon far fa-check" v-show="!filter.tag"></i> <span class="label">All</span>
        </li>
        <li class="settings-item" v-for="tag in allTags" :key="tag" @click="filter.tag = tag">
          <i class="settings-icon far fa-check" v-show="filter.tag === tag"></i> <span class="label">{{ tag }}</span>
        </li>
      </ul>
      <ul class="settings-list" v-if="allFileTypes.length > 1">
        <li class="settings-header" @click="filter.fileType = null">
          File types
        </li>
        <li class="settings-item" @click="filter.fileType = null">
          <i class="settings-icon far fa-check" v-show="!filter.fileType"></i> <span class="label">All</span>
        </li>
        <li class="settings-item" v-for="fileType in allFileTypes" :key="fileType" @click="filter.fileType = fileType">
          <i class="settings-icon far fa-check" v-show="filter.fileType === fileType"></i> <span class="label">{{
            fileType
            }}</span>
        </li>
      </ul>
      <ul class="settings-list">
        <li class="settings-header" v-if="canAdd" @click="openAlbumLink">
          Images
        </li>
        <li class="settings-item" :disabled="isDisabled" @click="onRefreshFilesClicked">
          <i :class="{ 'settings-icon far fa-refresh': true, 'fa-spin': isSpinning }"></i> <span
            class="label">Refresh</span>
        </li>
        <li class="settings-item" v-if="canAdd" @click="openAlbumLink">
          <i class="settings-icon far fa-folder-grid"></i> <span class="label">Manage</span>
        </li>
        <li class="settings-item" title="Download zip" @click="openDownloadAlbumLink" :disabled="!hasFiles">
          <i class="settings-icon far fa-arrow-to-bottom"></i> <span class="label">Download</span>
        </li>
      </ul>
    </div>

  </div>
</template>

<script>
import { forEach, filter, includes, castArray } from 'lodash-es';
import _ from 'lodash-es';
import Vue from 'vue';
import { mapGetters } from 'vuex';
import Access from '@/utils/access.js';
import { AFTER_CALL_READ } from '@/config.js';
import BaseSection from '../call/BaseSection.vue';
import UploadArea from './UploadArea.vue';
import FileBrowser from './FileBrowser.vue';

export default {
  name: 'images-section-thing',

  extends: BaseSection,

  components: {
    FileBrowser,
    UploadArea
  },

  data() {
    return {
      allFiles: [],
      hiddenTags: ['originalFileName'],
      isUploadAreaVisible: false,
      isDevMode: import.meta.env.VITE_API_MODE === 'Dev',
      isDisabled: false,
      isSpinning: false,
      viewLayout: 'grid',

      filter: {
        tag: null,
        fileType: null
      },

      popover: {
        position: {
          top: 0,
          left: 0
        }
      }
    };
  },

  mounted() {
    this.$hub.$on(AFTER_CALL_READ, async () => {
      await this.$awaitNextTicks(1);
      this.refreshAllFiles();
    });
  },

  destroyed() {
    this.$hub.$off(AFTER_CALL_READ);
  },

  computed: {
    ...mapGetters(['__state']),

    canAdd() {
      return Access.has('images.add');
    },

    canView() {
      return Access.has('images.view');
    },

    canDelete() {
      return false; // Access.has('images.delete');
    },

    albumLink() {
      return `https://images.towxchange.net/tops/gallery.php?callNum=${this.call.lCallKey}&selectedOrgUnit=${this.__state.orgUnitKey}`;
    },

    downloadAlbumLink() {
      let imageNames = [];

      forEach(this.allImages, image => {
        imageNames.push(`${image.sFileName}.${image.sFileType}`);
      });

      imageNames = JSON.stringify(imageNames);

      return `https://images.towxchange.net/tops/downloadGalleryZip.php?zipName=TOPSImages_${this.call.lCallKey}&filesList=${imageNames}`;
    },

    successfullyUploadedCount() {
      return this.uploadProgress.filter(
        item => item.isComplete && !item.error
      ).length;
    },

    hasFiles() {
      return this.allFiles.length > 0;
    },

    filteredFiles() {
      if (!this.filter.tag && !this.filter.fileType) {
        return this.allFiles;
      }

      return filter(this.allFiles, file => {
        if (this.filter.tag && this.filter.fileType) {
          return includes(file.jTags, this.filter.tag) && file.sFileType === this.filter.fileType;
        } else if (this.filter.tag) {
          return includes(file.jTags, this.filter.tag);
        } else if (this.filter.fileType) {
          return file.sFileType === this.filter.fileType;
        }

        return false;
      });
    },

    allTags() {
      if (!Array.isArray(this.allFiles)) {
        return [];
      }
      
      return _(this.allFiles)
        .map('jTags')
        .map(tags => {
          try {
            if (tags && tags !== 'null') {
              const parsed = JSON.parse(tags);
              return Array.isArray(parsed) ? parsed : [];
            }
          } catch (e) {
            console.warn('Error parsing tags:', tags, e);
          }
          return [];
        })
        .flatten()
        .filter(tag => tag && tag.tagType && !this.hiddenTags.includes(tag.tagType))
        .map('tagType')
        .uniq()
        .value();
    },

    allFileTypes() {
      return _(this.allFiles)
        .map('sFileType')
        .uniq()
        .value();
    }
  },

  methods: {
    async onRefreshFilesClicked() {
      this.isDisabled = true;
      this.isSpinning = true;
      await this.refreshAllFiles();
      setTimeout(() => {
        this.isDisabled = false;
        this.isSpinning = false;
      }, 200);
    },

    refreshAllFiles() {
      return new Promise((resolve, reject) => {
        const payload = {
          'Authentication': {
            'InstanceKey': this.$store.state.instance.Key,
            'AuthenticationKey': this.$store.state.instance.Authentication
          },
          'Data': {
            'CallKey': this.call.lCallKey,
            'OrgUnitKey': this.$store.state.orgUnitKey
          }
        };

        Vue.axios.post(import.meta.env.VITE_TXI_API_IMAGE_DOWNLOAD, payload).then(response => {
          this.onHandleDownloadResponse(response);
          resolve(response);
        }).catch(error => {
          this.onHandleDownloadError(error);
          reject(error);
        });
      });
    },

    onHandleDownloadResponse(response) {
      // search all images and remove any pdf
      this.allFiles = castArray(response.data);

      // if the call has zero images let's show the drag-n-drop area
      if (!this.hasFiles) {
        this.toggleUploadAreaVisibility();
      }
    },

    onHandleDownloadError(error) {
      console.error('out_ Error uploading the photo:', error);
    },

    openAlbumLink() {
      window.open(this.albumLink, '_blank');
    },

    openDownloadAlbumLink() {
      window.open(this.downloadAlbumLink, '_blank');
    },

    toggleUploadAreaVisibility() {
      this.isUploadAreaVisible = !this.isUploadAreaVisible;
    },

    simulateUpload() {
      this.$refs.uploadAreaRef.simulateUpload();
    },

    setPopoverPosition(event) {
      const { top, left } = event.target.getBoundingClientRect();
      this.popover.position = { top, left };
    },

    filterHiddenTags(tags = []) {
      try {
        if (!tags || tags === 'null') {
          return [];
        }

        if (typeof tags === 'string') {
          tags = JSON.parse(tags);
        }

        return tags.filter(tag => !this.hiddenTags.includes(tag.tagType));
      } catch (error) {
        console.log('*** filterHiddenTags:error', error);
      }
    }
  },

  provide() {
    return {
      filterHiddenTags: this.filterHiddenTags
    };
  }
};
</script>

<style>
#images-section-thing {
  padding: 0.25rem;
}

.files-menu {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  width: 100%;
  margin-bottom: 0.25rem;

  button {
    align-self: stretch;

    padding: 0.25rem 1rem;
    background-color: white;
    border: 0.2rem solid var(--placeholder-bg);
    border-radius: 0.5rem;
  }
}

#settings-popover {
  --top: 0;
  --left: 0;

  position: absolute;
  top: var(--top);
  left: var(--left);

  margin: 0;
  width: 13rem;
  border-radius: 0.5rem;
  box-shadow: var(--box-shadow-100);
  border: none;

  &:popover-open {
    display: flex;
    flex-direction: column;
  }

  &>* {
    border: none;
  }
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.settings-header,
.settings-item {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.25rem;
  align-items: center;

  padding: 0.25rem 0.5rem;

  .settings-icon {
    grid-area: icon;

    place-items: center;
  }

  .label {
    grid-area: label;
    font-weight: normal;
  }
}

.settings-header {
  opacity: 0.3;
}

.settings-item {
  grid-template-columns: 1.25rem 1fr;
  grid-template-areas: "icon label";
  background-color: white;
}

.settings-item {
  &:nth-of-type(2) {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }

  &:last-of-type {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
}
</style>
