<template>
  <div class="tags-control">
    <h3 class="is-small is-upper">Tags</h3>
    <label class="tags">
      <div class="tag-item" v-for="tag in filterHiddenTags(tags)" :key="tag.tagUUID">
        <i class="delete far fa-xmark" @click="deleteTag(tag.tagUUID)"></i>
        <div class="name">{{ tag.tagType }}</div>
      </div>
      <div class="tag-entry">
        <input type="text"
          v-model="tagEntry"
          list="suggested-tags"
          @keyup.enter="addTag()"
          placeholder="Add tag" />

        <datalist id="suggested-tags">
          <option v-for="tag in suggestedTags" :value="tag.id" :key="tag.id" />
        </datalist>
      </div>
    </label>
  </div>
</template>

<script>import { get } from 'lodash-es';


import Vue from 'vue';

export default {
  name: 'tags-control',

  inject: ['filterHiddenTags'],

  props: {
    file: {
      type: Object
    }
  },

  data () {
    return {
      suggestedTags: [
        { id: 'invoice' }
      ],
      tags: [],
      tagEntry: ''
    };
  },

  watch: {
    file: {
      handler () {
        this.getTags();
      },
      immediate: true
    }
  },

  methods: {
    fetchTags () {
      return new Promise((resolve, reject) => {
        const payload = {
          'Authentication': {
            'InstanceKey': this.$store.state.instance.Key,
            'AuthenticationKey': this.$store.state.instance.Authentication
          },
          'Data': {
            'ImagePrefix': this.file.sFileName,
            'OrgUnitKey': this.$store.state.orgUnitKey
          }
        };

        Vue.axios.post(import.meta.env.VITE_TXI_API_IMAGE_GET_TAGS, payload).then(response => {
          const data = get(response.data, 'towXResponse.Data.Recordset', null);
          resolve(data);
        }).catch(error => {
          reject(error);
        });
      });
    },

    async getTags () {
      this.tags = await this.fetchTags();
    },

    addTag (tag = this.tagEntry, extraTagData = null) {
      return new Promise((resolve, reject) => {
        const payload = {
          'Authentication': {
            'InstanceKey': this.$store.state.instance.Key,
            'AuthenticationKey': this.$store.state.instance.Authentication
          },
          'Data': {
            'ImagePrefix': this.file.sFileName,
            'OrgUnitKey': this.$store.state.orgUnitKey,
            'TagType': tag
          }
        };

        // extraTagData should be an object when present
        if (extraTagData) {
          payload.Data.ExtraTagData = extraTagData;
        }

        // if (!this.suggestedTags.some(suggestedTag => suggestedTag.id === tag)) {
        //   reject(new Error('Unsupported tag entered.'));
        //   this.tagEntry = '';
        //   return;
        // }

        Vue.axios.post(import.meta.env.VITE_TXI_API_IMAGE_ADD_TAGS, payload).then(response => {
          this.tagEntry = '';
          this.getTags();
          resolve(response);
        }).catch(error => {
          reject(error);
        });
      });
    },

    deleteTag (tagUuid) {
      return new Promise((resolve, reject) => {
        const payload = {
          'Authentication': {
            'InstanceKey': this.$store.state.instance.Key,
            'AuthenticationKey': this.$store.state.instance.Authentication
          },
          'Data': {
            'ImagePrefix': this.file.sFileName,
            'OrgUnitKey': this.$store.state.orgUnitKey,
            'TagUUID': tagUuid
          }
        };

        Vue.axios.post(import.meta.env.VITE_TXI_API_IMAGE_DELETE_TAGS, payload).then(response => {
          this.getTags();
          resolve(response);
        }).catch(error => {
          reject(error);
        });
      });
    }
  }
};
</script>

<style scoped>
.tags-control {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;

  width: 100%;
  max-width: 55ch;
  padding: 0.5rem;
  border: 0.1rem solid var(--placeholder-bg);
  border: 0.1rem solid var(--input-border);
  border-radius: 0.5rem;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  padding: 0.25rem 0.5rem;
  color: hsl(var(--pure-purple-hsl));
  background: hsla(var(--pure-purple-hsl), 0.2);
  border-radius: 0.25rem;
}

.tag-entry {
  input {
    appearance: none;
    border: none;
  }
}
</style>
