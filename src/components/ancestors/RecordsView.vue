<script>import { hasIn, get, clone, forEach, take, isString, keys, map, has, find, join, set } from 'lodash-es';


import is from 'is_js';
import datefns from 'date-fns';
import { mapGetters, mapActions } from 'vuex';
import QuickViews from '../features/QuickViews.vue';
import SpotlightControl from '../features/SpotlightControl.vue';

import {
  EVENT_INFO,
  STASH_FILTER_RELAY
} from '@/config.js';

export default {
  components: {
    QuickViews,
    SpotlightControl
  },

  data () {
    return {
      // Set by child component
      viewConfig: {
        key: null,
        uuid: '',
        noun: '',
        recordKeyName: '',
        readRouteName: '',
        addRouteName: '',

        requireData: false,
        requireFilters: false,
        shouldImmediatelyLoadData: true,
        shouldImmediatelySelectAllRecords: false,

        trimField: {
          key: '',
          name: ''
        },

        dataAdditional: {
          ShowInactive: false
        }
      },

      columnCap: 50,
      viewData: {},
      viewSettings: {},
      showLoader: false,
      canOpenOnlyRecord: false
    };
  },

  computed: {
    ...mapGetters([
      '__cachedFilters',
      'RECORDS__settings',
      '__selectedRecords'
    ]),

    preserveFilters: {
      get () {
        if (!this.viewConfig.uuid) return false;

        return window.sessionStorage.getItem(`${this.viewConfig.uuid}-preserve-filters`) || false;
      },
      set (boolean) {
        if (!this.viewConfig.uuid) return;

        window.sessionStorage.setItem(`${this.viewConfig.uuid}-preserve-filters`, boolean);
      }
    },

    gridSettings: {
      get () {
        let grid = {};
        let relayedFilters = JSON.parse(window.sessionStorage.getItem(STASH_FILTER_RELAY)) || [];

        if (!hasIn(this.viewSettings, 'Grids[0]')) {
          return {};
        }

        grid = this.viewSettings.Grids[0];

        // Replace default filters with temporary filters relayed from another screen
        if (relayedFilters.length > 0) {
          grid.Filters = relayedFilters;
          this.canOpenOnlyRecord = true;
          window.sessionStorage.removeItem(STASH_FILTER_RELAY);
        }

        return grid;
      },
      set (grid) {
        this.viewSettings.Grids[0] = grid;
      }
    },

    gridData () {
      if (!hasIn(this.viewData, `Grids[${this.viewKey}]`)) {
        return [];
      }

      return this.viewData.Grids[this.viewKey];
    },

    refreshedAt () {
      return get(this.viewData, 'Current', datefns.format(new Date(), 'YYYY-MM-DD HH:mm:ss'));
    },

    hasData () {
      if (!hasIn(this.viewData, `Grids[${this.viewKey}]`)) {
        return false;
      }

      return this.viewData.Grids[this.viewKey].length > 0;
    },

    inactiveToggleClasses () {
      return {
        'fal': true,
        'fa-eye': this.viewConfig.dataAdditional.ShowInactive,
        'fa-eye-slash': !this.viewConfig.dataAdditional.ShowInactive
      };
    },

    viewKey () {
      return this.RECORDS__settings.Key;
    },

    oneRecordIsSelected () {
      return this.__selectedRecords.length === 1;
    },

    anyRecordsAreSelected () {
      return this.__selectedRecords.length >= 1;
    },

    manyRecordsAreSelected () {
      return this.__selectedRecords.length > 1;
    },

    selectedRecordKey () {
      if (!this.oneRecordIsSelected) return false;

      return this.__selectedRecords[0][this.viewConfig.recordKeyName];
    },

    isDeletable () {
      if (!this.oneRecordIsSelected) return false;

      if (!this.viewConfig.dataAdditional.ShowInactive) return true;

      return is.inArray(toString(this.__selectedRecords[0].bActive), ['1', 'true']);
    },

    isUndeletable () {
      if (!this.oneRecordIsSelected) return false;

      return is.inArray(toString(this.__selectedRecords[0].bActive), ['0', 'false']);
    }
  },

  methods: {
    ...mapActions([
      '__cacheRecords',
      'RECORD__delete',
      '__selectRecords',
      'REPORT__getData',
      '__setCurrentView',
      'RECORD__undelete',
      'RECORDS__exportPDF',
      'RECORDS__exportCSV',
      'RECORDS__getSettings',
      'RECORDS__saveSettings',
      'RECORDS__cacheSettings',
      'TOPSCOMPANY__getSettings',
      'TOPSCOMPANY__setSettings',
      'RECORDS__createQuickSearch'
    ]),

    openRecord (record) {
      let key = this.viewConfig.readRouteKey || this.viewConfig.recordKeyName;

      this.$router.push({
        name: this.viewConfig.readRouteName,
        params: { key: record[key] }
      });
    },

    refresh () {
      this.loadData();
    },

    getTOPSCompanySettings () {
      this.TOPSCOMPANY__getSettings({
        callback: response => {
          this.TOPSCOMPANY__setSettings(response);

          if (this.afterGetTOPSCompanySettings !== undefined) {
            this.afterGetTOPSCompanySettings();
          }
        }
      });
    },

    getSettings (props) {
      let viewKey = get(props, 'viewKey', '');

      if (!this.viewConfig.noun) return;

      this.showLoader = true;

      this.RECORDS__getSettings({
        noun: this.viewConfig.noun,
        userViewKey: viewKey,
        callback: response => {
          this.RECORDS__cacheSettings(this.pruneColumns(response));
          this.__setCurrentView(this.RECORDS__settings.Title);

          this.viewSettings = clone(this.RECORDS__settings);

          if (this.viewConfig.shouldImmediatelyLoadData) {
            this.$nextTick(() => {
              this.loadData();
            });
          } else {
            this.showLoader = false;
          }
        }
      });
    },

    pruneColumns (settings) {
      forEach(settings.Grids, grid => {
        // Remove filters if this is a clean session or
        // if the user has changed orgs
        if (!this.preserveFilters) {
          grid.Filters = [];
        }

        if (grid.Columns.length > this.columnCap) {
          grid.Columns = take(grid.Columns, this.columnCap);
        }
      });

      if (!this.preserveFilters) {
        this.preserveFilters = true;
      }

      return settings;
    },

    cloneCustomizer (value) {
      if (isString(value)) return value;
    },

    async beforeLoadData () {
      if (this.viewConfig.requireFilters &&
        get(this.gridSettings, 'Filters', []).length === 0) {
        throw new Error('Push to search');
      }

      return;
    },

    async loadData (targetGridSettings = this.RECORDS__settings) {
      let settingsPresent = false;

      forEach(targetGridSettings.Grids, grid => {
        if (keys(grid).length > 0) settingsPresent = true;
      });

      if (!settingsPresent) return;

      this.beforeLoadData()
        .then(() => {
          // Hydrate column changes that may have happened in Vuex.
          // Relevant to loading quick views.
          this.viewSettings = clone(this.RECORDS__settings);

          this.$store.dispatch('RECORDS__getData', {
            noun: this.viewConfig.noun,
            data: targetGridSettings,
            dataAdditional: this.viewConfig.dataAdditional,
            callback: response => {
              this.showLoader = false;
              this.viewData = response;

              this.cacheRecords();
              this.afterLoadData();

              if (this.viewConfig.requireData && !this.hasData) {
                this.$notify.info({
                  title: 'Info',
                  message: 'No data for this search.'
                });

                return;
              }

              if (this.viewConfig.shouldImmediatelySelectAllRecords) {
                this.selectAllRecords();
              }

              if (this.shouldOpenOnlyRecord()) {
                this.openRecord(this.viewData.Grids[this.viewKey][0]);
              }
            },
            failCallback: () => {
              // this.$router.go(-1);
            }
          });
        })
        .catch(() => {
          this.search();
        });
    },

    afterLoadData () {
      // Extended by children
    },

    search () {
      this.$router.push({
        name: 'GridSearch',
        params: { key: this.gridSettings.Key },
        query: {
          noun: this.viewConfig.noun,
          mode: 'form'
        }
      });
    },

    cacheRecords () {
      let keys = [];

      forEach(this.viewData.Grids[this.viewKey], record => {
        keys.push(record[this.viewConfig.recordKeyName]);
      });

      this.__cacheRecords(keys);
    },

    selectAllRecords () {
      let records = [];

      forEach(this.viewData.Grids[this.viewKey], record => {
        records.push(record);
      });

      this.__selectRecords(records);
    },

    shouldOpenOnlyRecord () {
      return this.canOpenOnlyRecord && this.viewData.Grids[this.viewKey].length === 1;
    },

    trimToSelection (grid = this.gridSettings) {
      if (!this.manyRecordsAreSelected) return;

      let recordKeys = map(this.__selectedRecords, record => {
        if (has(record, this.viewConfig.recordKeyName)) return record[this.viewConfig.recordKeyName];
      });

      let newFilter = {
        And: false,
        Or: false,
        Not: false,
        OpenParen: false,
        FieldID: this.viewConfig.trimField.key,
        FieldName: this.viewConfig.trimField.name,
        preset: '',
        Operator: 'In',
        Value: recordKeys.join(', '),
        DisplayValue: recordKeys.join(', '),
        CloseParen: false
      };

      grid.Filters = [];
      grid.Filters.push(newFilter);

      this.save(grid);
    },

    save (grid) {
      let targetGrid = find(this.RECORDS__settings.Grids, ['Key', grid.Key]);

      targetGrid = grid;

      let uselessValue = true;
      if (!uselessValue) {
        console.log('Pacifier', targetGrid);
      }

      this.RECORDS__saveSettings({
        noun: this.viewConfig.noun,
        data: this.RECORDS__settings,
        callback: response => {
          this.refresh();
        }
      });
    },

    copyToClipboard (event) {
      if (!this.manyRecordsAreSelected) return;

      this.$hub.$emit(EVENT_INFO, 'Copied to clipboard.');

      let recordKeys = [];

      forEach(this.__selectedRecords, record => {
        recordKeys.push(record[this.viewConfig.recordKeyName]);
      });

      event.clipboardData.setData('text/plain', join(recordKeys, '\n'));

      event.preventDefault();
    },

    exportData ({ format, gridKey }) {
      let actionName = '';

      switch (format) {
        case 'CSVFile':
          actionName = 'RECORDS__exportCSV';
          break;

        case 'PDF':
          actionName = 'RECORDS__exportPDF';
          break;
      }

      this[actionName]({
        noun: this.viewConfig.noun,
        viewKey: this.viewKey,
        gridKey: gridKey,
        name: this.viewSettings.Title,
        description: this.viewSettings.Description,
        grids: this.viewSettings.Grids,
        showInactive: this.viewConfig.dataAdditional.ShowInactive
      });
    },

    toggleInactiveRecords () {
      this.viewConfig.dataAdditional.ShowInactive = !this.viewConfig.dataAdditional.ShowInactive;

      this.__selectRecords([]);
      this.loadData();
    },

    add () {
      this.$router.push({ name: this.viewConfig.addRouteName });
    },

    duplicate () {
      this.$router.push({
        name: this.viewConfig.addRouteName,
        query: { duplicate: this.selectedRecordKey }
      });
    },

    deleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      set(dataPackage, this.viewConfig.recordKeyName, this.selectedRecordKey);

      this.RECORD__delete({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.loadData();
        }
      });
    },

    undeleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      set(dataPackage, this.viewConfig.recordKeyName, this.selectedRecordKey);

      this.RECORD__undelete({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.loadData();
        }
      });
    },

    setFilters (filters) {
      this.gridSettings.Filters = filters;
      this.canOpenOnlyRecord = true;

      this.loadData();
    }
  },

  mounted () {
    document.addEventListener('copy', this.copyToClipboard);

    this.viewConfig.key = this.viewKey;

    this.getSettings();
    this.getTOPSCompanySettings();
  },

  beforeDestroy () {
    document.removeEventListener('copy', this.copyToClipboard);

    this.canOpenOnlyRecord = false;
  }
};
</script>
