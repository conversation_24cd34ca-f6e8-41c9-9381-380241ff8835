<script>import { isEmpty, get, set, isFunction, forOwn, has, castArray } from 'lodash-es';


import { mapGetters, mapActions } from 'vuex';
import { castDataTypes } from '../../utils/filters';

import {
  EVENT_SEARCH_CATCH_BOOMERANG,
  EVENT_SEARCH_THROW_BOOMERANG,
  EVENT_ERROR
} from '@/config.js';

export default {
  props: {
    recordKey: { type: [Number, String], default: null, required: false },
    searchMode: { type: Boolean, default: false },
    isNested: { type: Boolean, default: false }
  },

  data () {
    return {
      // Set by child component
      viewConfig: {
        noun: '',
        recordKeyName: '',
        returnRouteName: ''
      },

      now: '',
      // record: {}, Set by child component
      defaults: {},
      settings: {}
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'RECORDS__settings',
      'TOPSCOMPANY__settings'
    ]),

    key () {
      return this.recordKey || this.$route.params.key;
    },

    recordToDuplicate () {
      return this.$route.query.duplicate;
    },

    isNewRecord () {
      return isEmpty(this.key);
    },

    isDeletable () {
      if (this.isNewRecord) return false;

      let active = get(this.record, 'bActive', false);

      return ['1', 'true'].includes(active.toString());
    },

    isUndeletable () {
      if (this.isNewRecord) return false;

      let active = get(this.record, 'bActive', false);

      return ['0', 'false'].includes(active.toString());
    }
  },

  // watch: {
  //   '$route': 'initializeView'
  // },

  methods: {
    ...mapActions([
      '__getNow',
      '__setCurrentView',

      'RECORD__create',
      'RECORD__delete',
      'RECORD__update',
      'RECORD__undelete',
      'RECORD__getData',
      'RECORD__getDefaults',
      'RECORD__getSettings',

      'RECORDS__getSettings',
      'RECORDS__saveSettings'
    ]),

    initializeView () {
      if (this.searchMode) return;

      this.getNow();
      this.getViewSettings();
      this.getViewData();
      this.getNewDefaults();
    },

    getViewSettings () {
      this.RECORD__getSettings({
        noun: this.viewConfig.noun,
        callback: response => {
          this.__setCurrentView(this.RECORDS__settings.Title);

          this.settings = response;
        }
      });
    },

    getViewData () {
      if (this.isNewRecord && !this.recordToDuplicate) return;

      let dataPackage = {};
      let targetRecordKey = this.recordToDuplicate ? this.recordToDuplicate : this.key;

      set(dataPackage, this.viewConfig.recordKeyName, targetRecordKey);

      if (isFunction(this.beforeGetViewData)) this.beforeGetViewData();

      this.RECORD__getData({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.record = castDataTypes(response);

          if (isFunction(this.afterGetViewData)) this.afterGetViewData();
        }
      });
    },

    getNewDefaults () {
      if (!this.isNewRecord) return;

      let dataPackage = {};

      set(dataPackage, this.viewConfig.recordKeyName, this.recordToDuplicate);

      this.RECORD__getDefaults({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.defaults = castDataTypes(response);

          this.fillDefaultValues();
        }
      });
    },

    fillDefaultValues () {
      if (isFunction(this.beforeFillDefaultValues)) this.beforeFillDefaultValues();

      forOwn(this.defaults, (value, property) => {
        this.$set(this.record, property, this.defaults[property]);
      });

      if (isFunction(this.afterFillDefaultValues)) this.afterFillDefaultValues();
    },

    cancel () {
      this.$router.push({ name: this.viewConfig.returnRouteName });

      this.$emit('close');
    },

    async save (handle = {}) {
      try {
        if (isFunction(this.beforeSave)) {
          await this.beforeSave();
        }

        if (this.isNewRecord) {
          const response = await this.create();

          const error = get(response, 'Error.Message', null);
          if (error) {
            throw new Error(error);
          }

          this.$emit('created', response);

          if (isFunction(handle.onCreated)) {
            handle.onCreated();
          }
        } else {
          const response = await this.update();

          const error = get(response, 'Error.Message', null);
          if (error) {
            throw new Error(error);
          }

          if (isFunction(handle.onUpdated)) {
            handle.onUpdated();
          }
        }

        this.$emit('close');
        this.$router.push({ name: this.viewConfig.returnRouteName });

      } catch (error) {
        this.$hub.$emit(EVENT_ERROR, error);
        return;
      }
    },

    create () {
      return new Promise((resolve, reject) => {
        this.__getNow({
          callback: response => {
            this.$set(this.record, 'dDateLastModified', response.Now);
            this.$set(this.record, 'lUserKey', this.__state.user.Key);
            this.$set(this.record, 'lUserKey_LastModifiedBy', this.__state.user.Key);
            this.$set(this.record, 'bActive', true);

            if ('lCreatedBy' in this.record) {
              this.$set(this.record, 'lCreatedBy', this.__state.user.Key);
            }

            if ('dCreated' in this.record) {
              this.$set(this.record, 'dCreated', response.Now);
            }

            if ('lSubterminalKey' in this.record && !this.record.lSubterminalKey) {
              this.$set(this.record, 'lSubterminalKey', this.__state.topsCompany.settings.SubterminalKey);
            }

            this.RECORD__create({
              noun: this.viewConfig.noun,
              data: this.record,
              callback: response => {
                resolve(response);
              },
              fail: error => {
                reject(error);
              }
            });
          }
        });
      });
    },

    update () {
      return new Promise((resolve, reject) => {
        this.RECORD__update({
          lastRead: this.now,
          noun: this.viewConfig.noun,
          data: this.record,
          callback: response => {
            resolve(response);
          },
          fail: error => {
            reject(error);
          }
        });
      });
    },

    duplicate () {
      if (this.isNewRecord) return;

      this.record[this.viewConfig.recordKeyName] = '';

      this.$router.push({
        name: this.viewConfig.addRouteName,
        query: { duplicate: this.key }
      });
    },

    deleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      set(dataPackage, this.viewConfig.recordKeyName, this.key);

      this.RECORD__delete({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: () => {
          this.$emit('close');
          this.$router.push({ name: this.viewConfig.returnRouteName });
        }
      });
    },

    undeleteRecord () {
      let dataPackage = { OverwriteUpdate: false };

      set(dataPackage, this.viewConfig.recordKeyName, this.key);

      this.RECORD__undelete({
        noun: this.viewConfig.noun,
        data: dataPackage,
        callback: response => {
          this.$router.push({ name: this.viewConfig.returnRouteName });
        }
      });
    },

    createIfMissing (name, value = '') {
      if (!has(this.record, name)) {
        this.$set(this.record, name, value);
      }
    },

    getNow () {
      this.__getNow({
        callback: response => {
          this.now = response.Now;
        }
      });
    },

    async remoteUpdateSettings (noun, filters) {
      let loadedSettings = await this.remoteGetSettings(noun);

      loadedSettings.Grids[0].Filters = castArray(filters);

      return await this.remoteSaveSettings(noun, loadedSettings);
    },

    async remoteGetSettings (noun) {
      return new Promise((resolve, reject) => {
        this.RECORDS__getSettings({
          noun: noun,
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async remoteSaveSettings (noun, settings) {
      return new Promise((resolve, reject) => {
        this.RECORDS__saveSettings({
          noun: noun,
          data: settings,
          callback: response => {
            resolve(response);
          }
        });
      });
    }
  },

  async mounted () {
    this.initializeView();

    this.$hub.$on(EVENT_SEARCH_THROW_BOOMERANG, onSuccess => {
      this.$hub.$emit(EVENT_SEARCH_CATCH_BOOMERANG, {
        noun: this.viewConfig.noun,
        data: this.record,
        onSuccess: onSuccess
      });
    });
  },

  beforeDestroy () {
    this.$hub.$off(EVENT_SEARCH_THROW_BOOMERANG);
  }
};
</script>
