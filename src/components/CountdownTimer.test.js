import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import CountdownTimer from './CountdownTimer.vue';

describe('CountdownTimer', () => {
  let wrapper;
  
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
    vi.useRealTimers();
  });

  it('shows correct countdown format for hours and minutes', () => {
    const futureTime = new Date(Date.now() + 3 * 60 * 60 * 1000 + 4 * 60 * 1000); // 3h 4m from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.text()).toBe('3h 4m');
  });

  it('shows correct countdown format for minutes and seconds', () => {
    const futureTime = new Date(Date.now() + 4 * 60 * 1000 + 30 * 1000); // 4m 30s from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.text()).toBe('4m 30s');
  });

  it('shows correct countdown format for seconds only', () => {
    const futureTime = new Date(Date.now() + 43 * 1000); // 43s from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.text()).toBe('43s');
  });

  it('shows Expired when time has expired', () => {
    const pastTime = new Date(Date.now() - 1000); // 1 second ago
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: pastTime.toISOString()
      }
    });

    expect(wrapper.text()).toBe('Expired');
  });

  it('updates the countdown every second', async () => {
    const futureTime = new Date(Date.now() + 65 * 1000); // 65 seconds from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.text()).toBe('1m 5s');
    
    // Advance time by 1 second
    vi.advanceTimersByTime(1000);
    await wrapper.vm.$nextTick();
    
    expect(wrapper.text()).toBe('1m 4s');
  });

  it('applies urgent class when less than 1 minute remaining', () => {
    const futureTime = new Date(Date.now() + 30 * 1000); // 30 seconds from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.classes()).toContain('urgent');
  });

  it('does not apply urgent class when more than 1 minute remaining', () => {
    const futureTime = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes from now
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    expect(wrapper.classes()).not.toContain('urgent');
  });

  it('cleans up timer on destroy', () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    const futureTime = new Date(Date.now() + 1000);
    
    wrapper = mount(CountdownTimer, {
      propsData: {
        endTime: futureTime.toISOString()
      }
    });

    wrapper.destroy();
    expect(clearIntervalSpy).toHaveBeenCalled();
  });
});
