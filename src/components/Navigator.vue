<template>
  <div id="navigator">
    <section class="tool-bar" :data-api-mode="__state.apiMode">
      <div class="flex-space"></div>

      <transition-group tag="ul" name="smooth-list" v-if="primaryRoutes.length">
        <li class="link" v-for="route in primaryRoutes" :key="route.path">
          <router-link :to="{ path: route.path }" :title="route.label">
            <i :class="getIcon(route)"></i>
            <div class="_label">{{ route.label.substr(0, 8) }}</div>
          </router-link>
        </li>
        <li class="link" key="navigator-trigger">
          <a @click.prevent="toggleNavigator">
            <i class="fal fa-circle-ellipsis"></i>
            <div class="_label">More</div>
          </a>
        </li>
      </transition-group>

      <div class="flex-space"></div>
    </section>

    <div class="backdrop" v-if="isExpanded" @click="toggleNavigator"></div>

    <transition @enter="enter" @leave="leave" :css="false">
      <section class="tool-panel" v-if="isExpanded">

        <div class="product-selector" v-if="shouldSelectProduct">
          <div class="_liner">
            <div class="dialog">Please choose a persona to get started.</div>

            <div class="v-space"></div>

            <label class="product" v-for="product in productsProxy" @click="selectProduct(product)" :key="product.key">
              {{ product.label }}
            </label>
          </div>
        </div>

        <div class="navigator-selector" v-if="shouldSeeNavigator">
          <div class="_liner">
            <div class="app">
              <img class="brand" src="../assets/images/tops-traxero.svg">
              <div class="version is-small is-selectable">v{{ version }}</div>
            </div>

            <div class="v-space"></div>

            <section class="maintenance" v-if="maintenanceRoutes.length > 0">
              <i class="avatar fal fa-wrench-simple"></i>
              <div>
                <div class="heading">Maintenance</div>

                <ul>
                  <li v-for="(route, index) in maintenanceRoutes" :key="index">
                    <router-link class="link" :to="{ path: route.path }">{{ route.label }}</router-link>
                  </li>
                </ul>
              </div>
            </section>

            <section class="sale" v-if="saleRoutes.length > 0">
              <i class="avatar fal fa-coins"></i>
              <div>
                <div class="heading">Sale</div>

                <ul>
                  <li v-for="(route, index) in saleRoutes" :key="index">
                    <router-link class="link" :to="{ path: route.path }">{{ route.label }}</router-link>
                  </li>
                </ul>
              </div>
            </section>

            <section class="profile">
              <i class="avatar fal fa-user-circle"></i>
              <div>
                <router-link class="link heading" :to="{ name: 'Profile' }">{{ __state.user.Name }}</router-link>

                <div class="company" v-if="__state.product.orgUnitRequired">
                  <div>{{ __orgUnitName }} <i class="far fa-angle-down" v-if="canSelectCompany"></i></div>
                  <select v-model="orgUnitKey" v-if="canSelectCompany">
                    <option v-for="orgUnit in orgUnitsProxy" :value="orgUnit.Key" v-text="orgUnit.Name"
                      :key="orgUnit.Key"></option>
                  </select>
                </div>

                <div class="v-space"></div>

                <ul>
                  <!-- <li>
                    <a class="link" @click="revisitProductSelector = true">Change Persona</a>
                  </li> -->
                  <li>
                    <router-link class="link exit" :to="{ name: 'SignIn' }">Sign Out</router-link>
                  </li>
                </ul>
              </div>
            </section>

            <section class="manage">
              <ul>
                <!-- <li>
                  <a @click.prevent="notifications">Notifications</a>
                </li>
                <li>
                  <a @click.prevent="messages">Messages</a>
                </li> -->
                <li>
                  <a class="link" href="https://www.towxchange.net/support.php" target="_blank">Help</a>
                </li>
                <li v-if="playgroundVisible">
                  <router-link class="link" :to="{ name: 'Playground' }">Playground</router-link>
                </li>
              </ul>
              <ul>
                <li>
                  <portal-target name="quick-views" key="quick-views"></portal-target>
                </li>
              </ul>
            </section>

            <div class="v-space"></div>

            <details>
              <summary class="is-small">Technical details</summary>
              <section class="technical-details is-small">
                <div class="heading">Software</div>

                <div class="is-selectable" :data-test-mode="__state.apiMode !== 'PRODUCTION'">App Mode: {{
                  __state.appMode || '--' }}</div>
                <div class="is-selectable" :data-test-mode="__state.apiMode !== 'PRODUCTION'">API Mode: {{
                  __state.apiMode }}</div>

                <div class="v-space"></div>

                <div class="heading">Hardware</div>

                <div class="is-selectable">{{ browser.name }} v{{ browser.version }}</div>
                <div class="is-selectable">{{ os.name }} v{{ os.version }}</div>

                <div class="v-space"></div>

                <div class="heading">Attributions</div>

                <div class="attributions">
                  <div class="is-selectable" v-for="(version, dependency) in dependencies" :key="dependency"
                    :title="version">{{ dependency }}</div>
                </div>
              </section>
            </details>
          </div>
        </div>

      </section>
    </transition>
  </div>
</template>

<script>
import bowser from 'bowser';
import { mapGetters, mapActions } from 'vuex';
import { dependencies } from '@/../package.json';
import { getAllowedRoutes } from '@/utils/navigation.js';
import { filter, get, reject, trim, find } from 'lodash-es';

import {
  VERSION,
  EVENT_ERROR,
  EVENT_GET_VIEWS,
  EVENT_ORG_UNITS_CHANGED,
  EVENT_TOGGLE_NAVIGATION,
  EVENT_ROLE_CHANGED,
  EVENT_PRODUCT_CHANGED,
  EVENT_ORG_UNIT_CHANGED,
  PRODUCTKEY_WEB_PORTAL
} from '@/config';

const browser = bowser.getParser(window.navigator.userAgent);

export default {
  name: 'navigator',

  data() {
    return {
      isExpanded: false,
      revisitProductSelector: false,
      allowedRoutes: [],

      browser: {
        name: browser.getBrowserName(),
        version: browser.getBrowserVersion()
      },

      os: {
        name: browser.getOSName(),
        version: browser.getOSVersion()
      },

      version: VERSION,
      dependencies: dependencies
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      '__orgUnitName',
      'USER__products',
      'INSTANCE__isValid',
      'RECORDS__settings'
    ]),

    primaryRoutes() {
      return this.allowedRoutes.filter(route => {
        return route.meta.navigatorContext === 'primary';
      });
    },

    maintenanceRoutes() {
      return filter(this.allowedRoutes, route => {
        if (import.meta.env.VITE_OBSERVE_LIEN_BATCH_RULE === 'true' &&
          route.label === 'Lien' &&
          Number(get(this.__state, 'topsCompany.settings.bWebTOPSLienAvailable', 0)) === 0) {
          return false;
        }

        return route.meta.navigatorContext === 'maintenance';
      });
    },

    saleRoutes() {
      return filter(this.allowedRoutes, route => {
        return route.meta.navigatorContext === 'sale';
      });
    },

    orgUnitsProxy() {
      return this.__state.user.OrgUnits;
    },

    productKeyProxy() {
      return this.__state.product.key;
    },

    productsProxy() {
      return reject(this.USER__products, ['key', PRODUCTKEY_WEB_PORTAL]);
    },

    orgUnitKey: {
      get() {
        return this.__state.orgUnitKey;
      },
      set(orgUnitKey = this.__state.orgUnitKey) {
        if (orgUnitKey !== this.__state.orgUnitKey) {
          this.__changeProductOrgUnit({
            product: this.productKeyProxy,
            orgUnit: orgUnitKey,
            callback: response => {
              this.identifyGroup(response);

              // This point marks the final step in the sign in process,
              // so send back to entry point if it exists.
              let entryPoint = window.localStorage.getItem('entry_point');

              if (entryPoint && entryPoint.length > 3) {
                this.$router.push({ path: trim(entryPoint, '#') });

                if (this.$route.name !== 'Default') {
                  this.toggleNavigator();
                }
              }
            }
          });

          // Preserve certain values from localStorage
          let entryPoint = window.localStorage.getItem('entry_point');
          let deviceId = window.localStorage.getItem('device.id');
          let lastCompany = window.localStorage.getItem('last_company');

          // Preserve Vuex state from sessionStorage
          let vuexState = window.sessionStorage.getItem('vuex');

          // Clear storages but maintain authentication state
          window.localStorage.clear();

          // Don't clear sessionStorage completely as it contains authentication data
          // Instead, we'll selectively clear non-essential session data
          // This prevents logout when changing companies
          const keysToKeep = ['vuex'];
          const sessionKeys = Object.keys(window.sessionStorage);

          sessionKeys.forEach(key => {
            if (!keysToKeep.includes(key)) {
              window.sessionStorage.removeItem(key);
            }
          });

          // Restore preserved values
          if (entryPoint) {
            window.localStorage.setItem('entry_point', entryPoint);
          }

          if (deviceId) {
            window.localStorage.setItem('device.id', deviceId);
          }

          if (lastCompany) {
            window.localStorage.setItem('last_company', lastCompany);
          }
        }
      }
    },

    productAndOrgSelected() {
      return !!this.productKeyProxy && !!this.orgUnitKey;
    },

    shouldSelectProduct() {
      return (this.productKeyProxy === PRODUCTKEY_WEB_PORTAL) ||
        (!this.productKeyProxy && this.USER__products.length > 2) ||
        this.revisitProductSelector;
    },

    canSelectCompany() {
      return this.orgUnitsProxy.length > 1;
    },

    shouldSeeNavigator() {
      return !!this.productKeyProxy &&
        (this.productKeyProxy !== PRODUCTKEY_WEB_PORTAL) &&
        !this.revisitProductSelector;
    },

    playgroundVisible() {
      return import.meta.env.VITE_PLAYGROUND_VISIBLE === 'true';
    }
  },

  watch: {
    '$route'() {
      this.isExpanded = !this.productAndOrgSelected;
    },

    productKeyProxy: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          if (!this.productAndOrgSelected) {
            this.isExpanded = true;
          }
        });
      }
    },

    orgUnitKey: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          if (!this.productAndOrgSelected) {
            this.isExpanded = true;
          }
        });
      }
    }
  },

  methods: {
    ...mapActions([
      '__signOut',
      '__selectProduct',
      '__changeProductOrgUnit'
    ]),

    setAllowedRoutes() {
      const routes = getAllowedRoutes();
      this.allowedRoutes = routes;
    },

    identifyGroup(session) {
      window.Appcues.group(
        session.OrgUnit, // unique, required
        {
          // example properties
          // createdAt: **********, // Unix timestamp of account signup date
          // purchasedAt: **********, // Unix timestamp of account purchase date (leave null if empty)
          // planTier: "Standard", // Account's plan tier
          // companyName: "CoolCo", // Account's name
          // MRR: 599, // Account's monthly recurring revenue
          // renewalDate: "********" // US timestamp of upcoming account renewal date
          // accountmanager: "Jerry" // Account manager
        }
      );
    },

    selectProduct({ key }) {
      this.__selectProduct(key);
      this.revisitProductSelector = false;
    },

    getIcon({ label }) {
      let dictionary = {
        'Users': 'fal fa-user',
        'TXI Customers': 'fal fa-user-tie',
        'Utilities': 'fal fa-toolbox',
        'TowLien Setup': 'fal fa-link',

        'Calls': 'fal fa-phone',
        'Customers': 'fal fa-user-tie',
        'Dispatch': 'fal fa-headset',
        'Drivers': 'fal fa-address-card',
        'Reports': 'fal fa-chart-pie',
        'Trucks': 'fal fa-steering-wheel',
        'Quotes': 'fal fa-file-invoice-dollar'
      };

      return dictionary[label];
    },

    toggleNavigator() {
      this.isExpanded = !this.isExpanded;
    },

    enter(element, done) {
      let timeline = this.$gsap.timeline();

      timeline.from(element, {
        x: '-3rem',
        opacity: 0,
        scale: 0.9,
        ease: 'back.out(1.7)',
        duration: 0.4
      })
        .call(() => {
          done();
        });
    },

    leave(element, done) {
      let timeline = this.$gsap.timeline();

      timeline.to(element, {
        x: '-3rem',
        opacity: 0,
        scale: 0.9,
        ease: 'sine.in',
        duration: 0.2
      })
        .call(() => {
          done();
        });
    }
  },

  mounted() {
    if (!this.primaryRoutes.length) {
      this.$nextTick(() => {
        this.$hub.$emit(EVENT_GET_VIEWS);
      });
    }

    this.$hub.$on(EVENT_ROLE_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_ROLE_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }
      this.setAllowedRoutes();
    });

    this.$hub.$on(EVENT_GET_VIEWS, () => {
      this.setAllowedRoutes();
    });

    // Handle a change to the Product (user selected new Product)
    this.$hub.$on(EVENT_PRODUCT_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_PRODUCT_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }

      this.setAllowedRoutes();
      this.$router.push({ name: 'Welcome' });
    });

    this.$hub.$on(EVENT_ORG_UNIT_CHANGED, () => {
      this.setAllowedRoutes();
    });

    this.$hub.$on(EVENT_TOGGLE_NAVIGATION, (value = !this.isExpanded) => {
      this.isExpanded = value;
    });

    this.$hub.$on(EVENT_ORG_UNITS_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_ORG_UNITS_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }

      if (!this.INSTANCE__isValid) return;

      // If the product doesn't use an org unit then clear it
      if (!this.__state.product.orgUnitRequired) {
        this.orgUnitKey = '';
        return;
      }

      // Make sure that the selected Org Unit is in the list of available ones for the user
      if (this.__state.orgUnitKey) {
        let targetOrgUnit = find(this.__state.user.OrgUnits, ['Key', this.__state.orgUnitKey]);
        if (!targetOrgUnit) this.orgUnitKey = '';
        return;
      }

      if (this.__state.user.OrgUnits.length < 1) {
        this.$hub.$emit(EVENT_ERROR, 'User does not have access to any companies');
        this.__signOut();
        return;
      }

      // Set Default Company
      let lastCompany = window.localStorage.getItem('last_company');
      let firstCompany = this.__state.user.OrgUnits[0].Key;
      let lastCompanyValidated = find(this.__state.user.OrgUnits, ['Key', lastCompany]);

      if (!lastCompanyValidated) lastCompany = null;
      this.orgUnitKey = lastCompany || firstCompany;
    });
  },

  beforeDestroy() {
    this.$hub.$off(EVENT_TOGGLE_NAVIGATION);
    this.$hub.$off(EVENT_ORG_UNITS_CHANGED);
  }
};
</script>

<style>
#navigator {
  --color: var(--white);

  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  display: grid;
  grid-template-columns: var(--tool-bar-width) 1fr;
  grid-template-rows: auto;
  grid-template-areas: "tool-bar tool-panel";

  color: var(--color);
  visibility: hidden;
  z-index: var(--layer-navigator);
  user-select: none;

  a {
    font-weight: bold;
    color: var(--color);
    transition: all var(--transition-fast) linear;
  }

  >.tool-bar {
    position: relative;

    grid-area: tool-bar;

    display: flex;
    flex-direction: column;

    padding: 0.5rem;
    background: var(--san-marino);
    visibility: visible;

    a {
      position: relative;

      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      width: 100%;
      height: 100%;
      font-size: var(--font-size-small1);
      letter-spacing: 0.1em;
      white-space: nowrap;
      background-color: hsla(0, 0%, 100%, 0.1);
      border-radius: 0.5rem;
      aspect-ratio: 1 / 1;
      overflow: hidden;
      transition: all var(--transition-fast) linear;

      &:hover {
        background-color: hsla(0, 0%, 100%, 0.3);
      }

      &[data-selected] {
        color: var(--pure-blue);
        background-color: white;
      }
    }

    .link {
      margin-bottom: 0.5rem;

      &:last-of-type {
        margin-bottom: 0;
      }

      i {
        font-size: var(--font-size-h4);
      }

      ._label {
        position: absolute;
        bottom: 0.25rem;
      }
    }

    .api-mode {
      position: absolute;
      left: 0.5rem;
      bottom: 0.5rem;

      width: calc(100% - 1rem);
      text-align: center;

      >span {
        padding: 0.25rem 0.5rem;
        font-weight: bold;
        background-color: var(--pure-orange);
        box-shadow: 0 0.25rem 0.25rem hsla(var(--pure-orange-h), var(--pure-orange-s), var(--pure-orange-l), 0.25);
        border-radius: 0.25rem;
      }
    }
  }

  >.tool-panel {
    grid-area: tool-panel;
    align-self: center;

    width: 55ex;
    max-width: calc(100vw - var(--tool-bar-width));
    max-height: calc(100vh - 2rem);
    margin: 1rem;
    background: hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 5%));
    overflow-y: auto;
    visibility: visible;
    border-radius: 1rem;
    box-shadow:
      inset 0 0 1px 1px hsl(var(--blue-h), var(--blue-s), calc(var(--blue-l) + 20%)),
      0 2rem 2rem hsla(var(--pure-blue-h), calc(var(--pure-blue-s) + 400%), calc(var(--pure-blue-l) - 30%), 0.25);

    .product-selector,
    .navigator-selector {
      display: flex;
      align-items: center;

      width: 100%;
      padding: 1rem;

      >._liner {
        width: 100%;
      }
    }

    .product-selector {
      .product {
        display: block;

        padding: 0.5rem 1rem;
        margin-bottom: 0.5rem;
        font-weight: bold;
        color: var(--color);
        background-color: color-mix(in oklch, var(--white), transparent 90%);
        border-radius: 0.5rem;
        transition: all var(--transition-fast) linear;

        &:hover {
          color: var(--pure-blue);
          background-color: white;
          box-shadow: var(--box-shadow-100);
        }

        .input {
          position: absolute;

          visibility: hidden;
        }
      }
    }

    .navigator-selector {
      ._liner {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        padding-top: 1rem;
      }

      .maintenance,
      .sale,
      .profile,
      .manage {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;

        padding: 1rem;
        background-color: color-mix(in oklch, var(--white), transparent 90%);

        border-radius: 0.5rem;

        .avatar {
          font-size: var(--font-size-h4);
        }
      }

      .app {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: end;
        gap: 1rem;

        .version {
          opacity: 0.66;
        }
      }

      .profile {
        .company {
          position: relative;

          select {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;

            opacity: 0;
          }
        }
      }

      .manage {
        display: flex;
        gap: 1rem;

        >* {
          flex: 1;
        }
      }

      .technical-details {
        padding: 1rem;
        background-color: color-mix(in oklch, var(--white), transparent 90%);
        border-radius: 0.5rem;

        .attributions {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem 1rem;
          letter-spacing: 0.1em;
        }

        [data-test-mode] {
          font-weight: bold;
          color: lightsalmon;
          opacity: 1;
        }
      }
    }

    .dialog {
      font-size: var(--font-size-h5);
    }
  }

  >.backdrop {
    grid-area: tool-panel;

    visibility: visible;
  }

  .heading {
    font-weight: bold;
  }
}
</style>
