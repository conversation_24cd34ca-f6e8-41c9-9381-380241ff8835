<template>
  <transition
    :css="false"
    @enter="enter"
    @leave="leave">
    <div v-if="show" class="modal is-active">
      <div @click.prevent="$emit('close')" class="modal-background"></div>

      <div class="modal-card">
        <header class="modal-card-head">
          <div class="modal-title">{{ title }}</div>
          <div v-if="titleAppend" class="modal-title-append">{{ titleAppend }}</div>
        </header>

        <section :class="bodyClasses">
          <slot></slot>
        </section>

        <!-- <footer class="modal-card-foot">
          <slot name="footer"></slot>
        </footer> -->
      </div>

      <button @click.prevent="$emit('close')" class="modal-close is-large"></button>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'modal-card',

  props: {
    show: { type: Boolean, required: true },
    title: { type: String, required: false },
    titleAppend: { type: String, required: false },
    pad: { type: Boolean, required: false, default: true }
  },

  computed: {
    bodyClasses () {
      return {
        'modal-card-body': true,
        'is-padded': this.pad
      };
    }
  },

  methods: {
    enter (element, done) {
      let timeline = this.$gsap.timeline();

      timeline.from(element, {
        duration: 0.2,
        ease: 'linear',
        opacity: 0
      })
      .from(element.querySelector('.modal-card'), {
        duration: 0.6,
        ease: 'back.out(1.7)',
        opacity: 0,
        scale: 0.9
      })
      .from(element.querySelector('.modal-close'), {
        duration: 0.4,
        ease: 'power2.out',
        opacity: 0,
        scale: 0
      })
      .call(() => {
        done();
      });
    },

    leave (element, done) {
      let timeline = this.$gsap.timeline();

      timeline.to(element.querySelector('.modal-card'), {
        duration: 0.4,
        ease: 'sine.in',
        opacity: 0,
        scale: 0.9
      })
      .to(element.querySelector('.modal-close'), {
        duration: 0.2,
        ease: 'sine.in',
        opacity: 0,
        scale: 0
      }, '<')
      .to(element, {
        duration: 0.2,
        ease: 'linear',
        opacity: 0
      })
      .call(() => {
        done();
      });
    }
  }
};
</script>

<style scoped>
.modal-card {
  width: 100%;
  max-width: var(--font-line-length);
  max-height: 90vh;
  box-shadow: var(--box-shadow-100);
}

.modal-background {
  background: var(--modal-bg);
}

.modal-close {
  &::before {
    background: var(--danger);
  }

  &::after {
    background: var(--danger);
  }

  &:hover {
    background-color: var(--body-bg);
  }
}

.modal-card-head {
  display: flex;
  justify-content: space-between;

  padding: 1rem;
  height: 4rem;
  color: var(--accent);
  background: var(--titlebar-bg);
  border-bottom: 0;
  border-top-left-radius: var(--border-radius-100);
  border-top-right-radius: var(--border-radius-100);
}

.modal-title {
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: var(--font-letter-spacing);
  font-size: 1rem;
}

.modal-card-body {
  display: grid;

  padding: 0;
  background: var(--body-bg);

  &.is-padded {
    padding: var(--padding, 0.5rem);
  }
}
</style>
