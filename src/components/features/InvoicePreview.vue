<template>
  <app-modal
    title="Invoice"
    class="invoice-preview"
    :show="true"
    @close="$emit('close')"
    :pad="true">

    <section class="items">
      <template v-for="item in reviewItems">
        <div class="_label" :data-subset="isArray(item.Value)" :key="`label${item.Key}`">{{ item.Name }}</div>

        <div class="_value" :key="`value${item.Key}`" v-if="!isArray(item.Value)">{{ item.Value || '&mdash;' }}</div>
        <div class="_value" data-subset :key="`value${item.Key}`" v-if="isArray(item.Value)">
          <table>
            <thead>
              <tr>
                <th>Type</th>
                <th>Qty</th>
                <th>Subtotal</th>
                <th>Tax</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, index) in servicesAsTabular" :key="`service-${index}-${row.serviceType}`">
                <td>{{ row.serviceType }}</td>
                <td>{{ row.units }}</td>
                <td>{{ row.serviceSubTotal }}</td>
                <td>{{ row.tax }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>

      <div class="_label"></div>
      <div class="_value">
        <wizard-button
          v-if="!invoiceKey"
          flavor="primary"
          :icon="false"
          :disabled="isLoading"
          @click="sendInvoice">
          Send invoice
        </wizard-button>
      </div>
    </section>

  </app-modal>
</template>

<script>
import { forEach, isArray } from 'lodash-es';
import { mapActions } from 'vuex';
import WizardButton from '@/components/inputs/WizardButton.vue';

export default {
  name: 'invoice-preview',

  props: {
    callKey: { required: true },
    invoiceKey: { default: null }
  },

  components: {
    WizardButton
  },

  data () {
    return {
      reviewItems: [],
      isLoading: false
    };
  },

  computed: {
    servicesAsTabular () {
      let services = [];
      const serviceContainers = (this.reviewItems.find(item => item.Key === 'services')).Value;

      forEach(serviceContainers, container => {
        const service = container.Value;

        const type = (service.find(detail => detail.Key === 'serviceType'));
        const quantity = (service.find(detail => detail.Key === 'units'));
        const subtotal = (service.find(detail => detail.Key === 'serviceSubTotal'));
        const tax = (service.find(detail => detail.Key === 'tax'));

        const refactoredService = {};
        refactoredService[type.Key] = type.Value;
        refactoredService[quantity.Key] = quantity.Value;
        refactoredService[subtotal.Key] = subtotal.Value;
        refactoredService[tax.Key] = tax.Value;

        services.push(refactoredService);
      });

      return services;
    }
  },

  mounted () {
    if (this.invoiceKey) {
      this.getReview();
    } else {
      this.getPreview();
    }
  },

  methods: {
    ...mapActions([
      'MCBILLING__sendInvoice',
      'MCBILLING__reviewInvoice',
      'MCBILLING__previewInvoice'
    ]),

    getPreview () {
      if (!this.callKey) return;

      this.MCBILLING__previewInvoice({
        key: this.callKey,
        success: response => {
          this.reviewItems = response;
        }
      });
    },

    getReview () {
      if (!this.invoiceKey) return;

      this.MCBILLING__reviewInvoice({
        key: this.invoiceKey,
        success: response => {
          this.reviewItems = response;
        }
      });
    },

    sendInvoice () {
      if (!this.callKey) return;

      this.isLoading = true;

      this.MCBILLING__sendInvoice({
        key: this.callKey,
        success: () => {
          this.isLoading = false;
          this.$emit('close');
          this.$emit('sent');
        }
      });
    },

    isArray (value) {
      return isArray(value);
    }
  }
};
</script>

<style scoped>
.invoice-preview {
  --padding: 1rlh;

  .items {
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-template-rows: auto;
    gap: 0.5rlh;

    > ._label {
      text-align: right;
    }

    ._value {
      font-weight: bold;

      &[data-subset] {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 1rlh;

        padding: 1rlh;
        background-color: color-mix(in oklch, var(--blue), var(--white) 95%);
        border-radius: 0.5rlh;

        > ._label {
          font-weight: 400;
        }
      }
    }
  }

  .wizard__button {
    padding: 1rlh 2rlh;
    margin: 0 auto 2rlh auto;
    width: max-content;
  }
}
</style>
