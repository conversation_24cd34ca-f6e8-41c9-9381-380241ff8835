<template>
  <form class="request-pane" @submit="$emit('on-get-position')">
    <ActionText v-model="$value" placeholder="1234567890">
      Phone
    </ActionText>
    <ActionButton :variant="$value ? 'blue' : ''" @click="$emit('on-get-position')" :disabled="!$value">
      Request location
    </ActionButton>

    <div class="v-space"></div>

    <AppTip>
      <p class="is-small">Optionally send a link to view real-time tow truck&nbsp;location.</p>
      <ActionButton @click="onRequestTowTracker" :disabled="!canRequestTowTracker">
        <i :class="towTrackerIconCss"></i> Tow tracker
      </ActionButton>
    </AppTip>
  </form>
</template>

<script>
import { EVENT_INFO } from '@/config';
import ActionText from '@/tower/liens/inputs/Text.vue';
import ActionButton from '@/tower/liens/inputs/Button.vue';

export default {
  name: 'RequestPane',

  props: {
    value: {
      type: String,
      default: ''
    },
    subcompanyKey: {
      type: [String, Number],
      default: ''
    },
    callKey: {
      type: [String, Number],
      default: ''
    }
  },

  components: {
    ActionText,
    ActionButton
  },

  data () {
    return {
      isTowTrackerRequested: false
    };
  },

  computed: {
    $value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    },

    towTrackerIconCss () {
      return {
        'fas fa-circle-check pure-green': this.isTowTrackerRequested,
        'far fa-circle-check pure-silver': !this.isTowTrackerRequested
      };
    },

    canRequestTowTracker () {
      return this.$store.state.topsCompany.settings.bAllowCustomerUpdate === '1' &&
        this.callKey &&
        this.$value;
    }
  },

  methods: {
    onRequestTowTracker () {
      this.$store.dispatch('TOWSTATUS__sendUpdateRequest', {
        companyKey: this.$store.state.orgUnitKey,
        subcompanyKey: this.subcompanyKey,
        callKey: this.callKey,
        phone: this.$value,
        success: () => {
          this.$hub.$emit(EVENT_INFO, 'Live view link was sent.');
          this.isTowTrackerRequested = true;
        }
      });
    }
  },

  beforeDestroy () {
    this.isTowTrackerRequested = false;
  }
};
</script>

<style scoped>
.request-pane {
  display: grid;
  gap: 0.5rem;
  justify-content: stretch;
}
</style>
