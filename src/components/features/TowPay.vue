<template>
  <app-modal title="Add Payment" @close="$emit('close')" :pad="false" :show="true">

    <div id="towpay">
      <section id="loader-section" v-show="!state.isReady">
        <app-loader></app-loader>
      </section>

      <ul class="towpay-tabs" v-show="state.isReady && state.tab.barIsEnabled">
        <li class="tab" v-show="state.tab.swipeIsEnabled" @click="state.tab.active = 'swipe'"
          :data-active="state.tab.active === 'swipe'">
          Reader
        </li>
        <li class="tab" @click="state.tab.active = 'entry'" :data-active="state.tab.active === 'entry'">
          Entry
        </li>
        <li class="tab" v-show="state.tab.linkIsEnabled" @click="state.tab.active = 'link'"
          :data-active="state.tab.active === 'link'">
          Link
        </li>
      </ul>

      <section id="swipe-section" v-show="state.isReady && state.tab.active === 'swipe'">
        <div class="input-panel" :data-show="!state.payWithCardReader.isPending">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-select v-model="selectedReaderKey" :options="readers" keyAlias="Key" valueAlias="Name"
                  :required="true">
                  Reader
                </app-select>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <app-button type="primary" @click="payWithCardReader" :disabled="!canPayWithCardReader">
            Send to reader
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithCardReader.isPending">
          <div class="_card">
            <i class="_icon fal fa-credit-card"></i>

            <div class="_instruction">
              Insert the card.
            </div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
              <app-button @click="cancelPayWithCardReader">
                Cancel
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <section id="entry-section" v-show="state.isReady && state.tab.active === 'entry'">
        <div class="input-panel" :data-show="!state.payWithCardNotPresent.isComplete">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-text v-model="cardholderName" :required="true">
                  Cardholder Name
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <div id="card-input"></div>

          <app-button type="primary" @click="payWithCardNotPresent" :disabled="!canPayWithCardNotPresent">
            Pay now
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithCardNotPresent.isComplete">
          <div class="_card">
            <i class="_icon fal fa-badge-check"></i>

            <div class="_instruction">
              Payment complete.
              <div class="_payment-number">Confirmation: {{ state.payWithCardNotPresent.payment.key }}</div>
            </div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <section id="link-section" v-if="state.isReady && state.tab.active === 'link'">
        <div class="input-panel" :data-show="!state.payWithLink2Pay.isSent">
          <app-grid-form context="inline">
            <div class="columns is-multiline">
              <div class="column is-6">
                <app-number v-model="amount" :required="true" :min="0" :max="maxAmountProxy">
                  Amount
                </app-number>
              </div>
              <div class="column is-6">
                <app-select v-model="receiptTypeKey" :options="receiptTypes" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Receipt Type
                </app-select>
              </div>
              <div class="column is-6 is-left">
                <app-text v-model="emailPhone" :required="true">
                  Email or Phone
                </app-text>
              </div>
              <div class="column is-6">
                <app-select v-model="receivedByKey" :options="employees" keyAlias="Key" valueAlias="Value"
                  :required="true">
                  Received By
                </app-select>
              </div>
              <div class="column is-12 is-left is-bottom">
                <app-textarea v-model="note" maxlength="255">
                  Note
                </app-textarea>
              </div>
            </div>
          </app-grid-form>

          <app-button type="primary" @click="payWithLink2Pay" :disabled="!canPayWithLink2Pay">
            Send link
          </app-button>
        </div>

        <div class="feedback-panel" :data-show="state.payWithLink2Pay.isSent">
          <div class="_card">
            <i class="_icon fal fa-paper-plane"></i>

            <div class="_instruction">Link is sent.</div>

            <div class="_actions">
              <app-button type="primary" @click="$emit('close')">
                Done
              </app-button>
            </div>
          </div>
        </div>
      </section>

      <img class="powered-by" src="/static/towpay-logo.webp" alt="Powered by TowPay">
    </div>
  </app-modal>
</template>

<script>
import { find, get } from 'lodash-es';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'towpay',

  props: {
    callKey: { type: [Number, String], required: false },
    customerKey: { type: [Number, String], default: null },
    initialAmount: { type: [Number, String], default: null },
    isTowPayment: { type: Boolean, default: true }
  },

  data() {
    return {
      state: {
        isReady: false,
        isBusy: false,

        tab: {
          active: 'entry',
          barIsEnabled: false,
          swipeIsEnabled: false,
          entryIsEnabled: true,
          linkIsEnabled: false
        },

        payWithCardReader: {
          isPending: false,
          readerId: '',
          intentId: ''
        },

        payWithCardNotPresent: {
          isComplete: false,
          payment: {
            key: '',
            intentId: ''
          }
        },

        payWithLink2Pay: {
          isSent: false
        }
      },

      selectedReaderKey: '',
      cardholderName: '',
      receiptTypeKey: '',
      receivedByKey: '',
      emailPhone: '',
      note: '',
      amount: '',
      maxAmount: '',
      receiptTypes: [],
      employees: [],
      readers: [],

      stripe: null,
      elements: null,
      cardElement: null
    };
  },

  computed: {
    ...mapGetters([
      '__state'
    ]),

    canPayWithCardReader() {
      return !this.state.isBusy &&
        this.amount &&
        this.selectedReaderKey &&
        this.receiptTypeKey &&
        this.receivedByKey;
    },

    canPayWithCardNotPresent() {
      return !this.state.isBusy &&
        this.amount &&
        this.cardholderName &&
        this.receiptTypeKey &&
        this.receivedByKey;
    },

    canPayWithLink2Pay() {
      return !this.state.isBusy &&
        this.amount &&
        this.emailPhone &&
        this.receiptTypeKey &&
        this.receivedByKey;
    },

    reader() {
      return find(this.readers, ['Key', this.selectedReaderKey]);
    },

    defaultSubterminalKey() {
      return this.subterminals[0].Key;
    },

    maxAmountProxy() {
      return this.maxAmount.length > 0 ? Number(this.maxAmount) : null;
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getEmployees',

      'PAYMENT__sendLinkToPay',
      'PAYMENT__getReceiptTypes',
      'PAYMENT__getSubterminals',
      'PAYMENT__getCCPaymentInfo',
      'PAYMENT__sendTowPayIntentToReader',
      'PAYMENT__cancelTowPayReaderIntent',
      'PAYMENT__createTowPayNotPresentPayment',

      'TOPSCALL__sendLinkToPay',
      'TOPSCALL__getCCPaymentInfo',
      'TOPSCALL__sendTowPayIntentToReader',
      'TOPSCALL__cancelTowPayReaderIntent',
      'TOPSCALL__createTowPayNotPresentPayment'
    ]),

    getPaymentInfo() {
      if (this.callKey) {
        return new Promise(resolve => {
          this.TOPSCALL__getCCPaymentInfo({
            callKey: this.callKey,
            isTowPayment: this.isTowPayment,
            success: response => {
              resolve(response);
            }
          });
        });
      } else {
        return new Promise(resolve => {
          this.PAYMENT__getCCPaymentInfo({
            subterminalKey: this.defaultSubterminalKey,
            success: response => {
              resolve(response);
            }
          });
        });
      }
    },

    getSubterminals() {
      return new Promise(resolve => {
        this.PAYMENT__getSubterminals({
          success: response => {
            resolve(response);
          }
        });
      });
    },

    populateReceiptTypes() {
      this.PAYMENT__getReceiptTypes({
        callback: response => {
          this.receiptTypes = response;
        }
      });
    },

    populateEmployees() {
      this.TOPSCOMPANY__getEmployees({
        callback: response => {
          this.employees = response;
        }
      });
    },

    populateAmount() {
      if (!this.callKey) { return; }
      if (this.amount) { return; }

      this.$store.dispatch('TOPSCALL__read', {
        callKey: this.callKey,
        success: response => {
          this.maxAmount = Number(response.tcBalance).toFixed(2);
          this.amount = this.maxAmount;
        }
      });
    },

    payWithCardReader() {
      this.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__sendTowPayIntentToReader({
          callKey: this.callKey,
          readerID: this.reader.Key,
          readerType: this.reader.Type,
          isTowPayment: this.isTowPayment,
          amount: this.amount,
          note: this.note,
          receiptType: this.receiptTypeKey,
          employeeKey: this.receivedByKey,
          success: response => {
            this.state.payWithCardReader.intentId = response.IntentID;
            this.state.payWithCardReader.readerId = this.reader.Key;
            this.state.payWithCardReader.isPending = true;
            this.isBusy = false;
          }
        });
      } else {
        this.PAYMENT__sendTowPayIntentToReader({
          subterminalKey: this.defaultSubterminalKey,
          customerKey: this.customerKey,
          readerID: this.reader.Key,
          readerType: this.reader.Type,
          isTowPayment: this.isTowPayment,
          amount: this.amount,
          note: this.note,
          receiptType: this.receiptTypeKey,
          employeeKey: this.receivedByKey,
          success: response => {
            this.state.payWithCardReader.intentId = response.IntentID;
            this.state.payWithCardReader.readerId = this.reader.Key;
            this.state.payWithCardReader.isPending = true;
            this.isBusy = false;
          }
        });
      }
    },

    cancelPayWithCardReader() {
      this.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__cancelTowPayReaderIntent({
          callKey: this.callKey,
          intentId: this.state.payWithCardReader.intentId,
          readerId: this.state.payWithCardReader.readerId,
          success: () => {
            this.state.payWithCardReader.intentId = '';
            this.state.payWithCardReader.readerId = '';
          },
          always: () => {
            this.state.payWithCardReader.isPending = false;
            this.isBusy = false;
          }
        });
      } else {
        this.PAYMENT__cancelTowPayReaderIntent({
          intentId: this.state.payWithCardReader.intentId,
          readerId: this.state.payWithCardReader.readerId,
          success: () => {
            this.state.payWithCardReader.intentId = '';
            this.state.payWithCardReader.readerId = '';
          },
          always: () => {
            this.state.payWithCardReader.isPending = false;
            this.isBusy = false;
          }
        });
      }
    },

    payWithLink2Pay() {
      this.isBusy = true;

      if (this.callKey) {
        this.TOPSCALL__sendLinkToPay({
          callKey: this.callKey,
          emailPhone: this.emailPhone,
          isTowPayment: this.isTowPayment,
          amount: this.amount,
          note: this.note,
          receiptType: this.receiptTypeKey,
          employeeKey: this.receivedByKey,
          success: response => {
            this.state.payWithLink2Pay.isSent = true;
            this.isBusy = false;

            this.$emit('created', response);
          }
        });
      } else {
        this.PAYMENT__sendLinkToPay({
          subterminalKey: this.defaultSubterminalKey,
          customerKey: this.customerKey,
          emailPhone: this.emailPhone,
          amount: this.amount,
          note: this.note,
          receiptType: this.receiptTypeKey,
          employeeKey: this.receivedByKey,
          success: response => {
            this.state.payWithLink2Pay.isSent = true;
            this.isBusy = false;

            this.$emit('created', response);
          }
        });
      }
    },

    async payWithCardNotPresent() {
      this.state.isBusy = true;

      const paymentMethod = await this.createStripePaymentMethod();

      const payment = await this.createTopsPayment({
        methodId: paymentMethod.id,
        name: paymentMethod.name,
        brand: paymentMethod.brand,
        expiry: paymentMethod.expiry,
        lastFour: paymentMethod.lastFour
      });

      this.$emit('created', payment);

      this.state.payWithCardNotPresent.payment.key = payment.key;
      this.state.payWithCardNotPresent.payment.intentId = payment.intentId;
      this.state.payWithCardNotPresent.isComplete = true;
      this.state.isBusy = false;
    },

    async createStripePaymentMethod() {
      return new Promise(async (resolve, reject) => {
        const response = await this.stripe.createPaymentMethod({
          type: 'card',
          card: this.cardElement,
          billing_details: {
            name: this.cardholderName
          }
        });

        if (!('paymentMethod' in response)) {
          reject('Unable to create payment method.');
        }

        const expiryMonth = response.paymentMethod.card.exp_month || '';
        const expiryYear = response.paymentMethod.card.exp_year.toString().substring(2, 4) || '';
        const expiry = `${expiryMonth}${expiryYear}`;

        resolve({
          id: response.paymentMethod.id || '',
          name: response.paymentMethod.billing_details.name || '',
          brand: response.paymentMethod.card.brand || '',
          expiry: expiry,
          lastFour: response.paymentMethod.card.last4 || ''
        });
      });
    },

    async createTopsPayment({ methodId, name, brand, expiry, lastFour }) {
      if (this.callKey) {
        return new Promise(async (resolve, reject) => {
          this.TOPSCALL__createTowPayNotPresentPayment({
            callKey: this.callKey,
            isTowPayment: this.isTowPayment,
            amount: this.amount,
            paymentMethodId: methodId,
            name: name,
            brand: brand,
            expiry: expiry,
            lastFour: lastFour,
            note: this.note,
            receiptType: this.receiptTypeKey,
            employeeKey: this.receivedByKey,
            success: response => {
              resolve({
                intentId: response.IntentID,
                key: response.PaymentKey
              });
            }
          });
        });
      } else {
        return new Promise(async (resolve, reject) => {
          this.PAYMENT__createTowPayNotPresentPayment({
            subterminalKey: this.defaultSubterminalKey,
            customerKey: this.customerKey,
            amount: this.amount,
            paymentMethodId: methodId,
            name: name,
            brand: brand,
            expiry: expiry,
            lastFour: lastFour,
            note: this.note,
            receiptType: this.receiptTypeKey,
            employeeKey: this.receivedByKey,
            success: response => {
              resolve({
                intentId: response.IntentID,
                key: response.PaymentKey
              });
            }
          });
        });
      }
    }
  },

  async mounted() {
    this.amount = this.initialAmount;

    this.populateReceiptTypes();
    this.populateEmployees();
    this.subterminals = await this.getSubterminals();

    const paymentInfo = await this.getPaymentInfo();

    this.readers = paymentInfo.Readers;

    this.selectedReaderKey = paymentInfo.LastReaderKey || get(this.readers, '[0].Key', '');

    // Initialize tabs
    this.state.tab.swipeIsEnabled = this.readers.length;
    this.state.tab.linkIsEnabled = this.__state.topsCompany.settings.bAllowLinkToPay;

    if (this.state.tab.swipeIsEnabled) {
      this.state.tab.active = 'swipe';
    }

    if (this.state.tab.swipeIsEnabled || this.state.tab.linkIsEnabled) {
      this.state.tab.barIsEnabled = true;
    }

    this.state.isReady = true;

    // Initialize Stripe Elements
    this.stripe = window.Stripe(paymentInfo.PublicKey, {
      stripeAccount: paymentInfo.AccountID
    });
    this.elements = this.stripe.elements();

    const style = {
      base: {
        fontFamily: '"Ubuntu", sans-serif',
        fontSize: '16px',
        color: '#32325d',
        '::placeholder': {
          color: '#5081ED'
        }
      },
      invalid: {
        fontFamily: '"Ubuntu", sans-serif',
        color: '#fa755a',
        iconColor: '#fa755a'
      }
    };

    this.cardElement = this.elements.create('card', { style: style });
    this.cardElement.mount('#card-input');
  }
};
</script>
