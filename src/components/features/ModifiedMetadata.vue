<template>
  <div class="modified-metadata is-small" @click="toggleSummary">
    <transition name="flip" mode="out-in" appear>
      <div v-if="isSummarized" key="summary" class="summary">{{ actionAlias }} {{ record[modifiedAtAlias] | relativeDate }} ago</div>
      <div v-else key="detail" class="detail">{{ actionAlias }} on {{ record[modifiedAtAlias] | verbalDate }} at {{ record[modifiedAtAlias] | verbalTime }} <span v-if="modifiedBy">by {{ modifiedBy }}</span></div>
    </transition>
  </div>
</template>

<script>
import { get, isEmpty } from 'lodash-es';
import { mapActions } from 'vuex';

export default {
  name: 'modified-metadata',

  props: {
    record: { type: Object, required: true },
    config: { type: Object, required: true },
    actionAlias: { type: String, required: false, default: 'Modified' },
    modifiedByAlias: { type: String, required: true, default: 'lUser<PERSON>ey' },
    modifiedAtAlias: { type: String, required: true, default: 'dDateLastModified' },
    recordKeyAlias: { type: String, required: false, default: '' }
  },

  data () {
    return {
      metadata: {},
      isSummarized: true
    };
  },

  computed: {
    modifiedBy () {
      return get(this.metadata, 'LastModifiedBy', null);
    }
  },

  methods: {
    ...mapActions(['RECORD__getMetadata']),

    toggleSummary () {
      this.isSummarized = !this.isSummarized;

      this.getMetadata();
    },

    getMetadata () {
      if (!isEmpty(this.metadata)) return;

      let keyName = this.recordKeyAlias || this.config.recordKeyName;

      this.RECORD__getMetadata({
        noun: this.config.noun,
        key: this.record[keyName],
        callback: response => {
          this.metadata = response;
        }
      });
    }
  }
};
</script>

<style scoped>
.modified-metadata {
  color: var(--metadata-fg);
  cursor: pointer;
}
</style>
