<template>
  <label class="card-swipe" @click="swipedInput = ''">
    <div class="_icon-placeholder">
      <i class="_icon -processing fal fa-spinner-third fa-spin" v-if="state.processing" key="processing"></i>
      <i class="_icon -complete fal fa-check-circle" v-else-if="state.complete" key="complete"></i>
      <i class="_icon -error fal fa-exclamation-circle" v-else-if="state.error" key="error"></i>
      <i class="_icon -card fal fa-id-card" v-else key="card"></i>
    </div>

    <div class="_text-placeholder">
      <div class="_text -initialize" v-if="!state.ready" key="make-ready">Swipe license</div>
      <div class="_text -ready" v-else key="is-ready">
        <span v-if="state.processing">Processing&hellip;</span>
        <span v-else>Awaiting swipe</span>
      </div>
    </div>

    <input class="_input" type="text" v-model="swipedInput" @focus="state.ready = true" @blur="state.ready = false" ref="input"/>
  </label>
</template>

<script>
import { debounce } from 'lodash-es';
import { mapActions } from 'vuex';
import { EVENT_WARNING } from '@/config.js';

export default {
  name: 'card-swipe',

  data () {
    return {
      state: {
        ready: false,
        processing: false,
        complete: false,
        error: false
      },

      swipedInput: '',

      parsedInput: {
        type: '',
        state: '',
        city: '',
        lastname: '',
        firstname: '',
        middlename: '',
        fullName: '',
        address1: '',
        address2: '',
        licenseNumber: '',
        zip: '',
        dob: '',
        expiry: '',
        otherInfo: ''
      }
    };
  },

  watch: {
    swipedInput () {
      if (this.swipedInput) {
        this.state.processing = true;

        this.parseInput();
      }
    }
  },

  methods: {
    parseInput: debounce(function () {
      this.swipedInput.replace(/\\/g, '');
      this.swipedInput.replace(/"/g, '');

      this.$store.dispatch('LICENSE__parse', {
        value: this.swipedInput,
        callback: response => {
          this.parsedInput.type = response.Type;
          this.parsedInput.state = response.State;
          this.parsedInput.city = response.City;
          this.parsedInput.lastName = response.LastName;
          this.parsedInput.firstName = response.FirstName;
          this.parsedInput.middleName = response.MiddleName;
          this.parsedInput.address1 = response.Address1;
          this.parsedInput.address2 = response.Address2;
          this.parsedInput.licenseNumber = response.LicenseNum;
          this.parsedInput.zip = response.Zip;
          this.parsedInput.dob = response.DOB;
          this.parsedInput.expiry = response.Expiry;
          this.parsedInput.otherInfo = response.OtherInfo;

          this.parsedInput.fullName = [
            this.parsedInput.firstName,
            this.parsedInput.middleName,
            this.parsedInput.lastName
          ].join(' ');

          this.state.complete = true;

          setTimeout(() => {
            this.state.complete = false;
          }, 3000);

          this.$emit('swipe', this.parsedInput);

          if (this.parsedInput.otherInfo) {
            this.$hub.$emit(EVENT_WARNING, this.parsedInput.otherInfo);
          }
        },
        fail: () => {
          this.state.error = true;

          setTimeout(() => {
            this.state.error = false;
          }, 3000);
        },
        always: () => {
          this.$refs.input.blur();
          this.state.processing = false;
        }
      });
    }, 600)
  }
};
</script>

<style scoped>
.card-swipe {
  position: relative;

  display: grid;
  grid-template-columns: 3lh 1fr;

  block-size: 3lh;
  border: 1px solid var(--input-border);
  border-radius: var(--border-radius-100);
  cursor: pointer;
  user-select: none;

  &:focus-within {
    border-color: var(--information);
  }

  > ._icon-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;

    > ._icon {
      grid-area: 1 / 1;

      font-size: var(--font-size-h4);
      inline-size: 1em;
      block-size: 1em;

      &.-complete {
        color: var(--success);
      }

      &.-error {
        color: var(--danger);
      }
    }
  }

  > ._text-placeholder {
    position: relative;

    display: flex;
    align-items: center;

    ._text {
      font-size: var(--font-size-h4);

      &.-ready {
        animation: ready-pulse 1.5s infinite var(--ease-in-out-sine);
      }
    }
  }

  ._input {
    opacity: 0;
    position: absolute;
    pointer-events: none;
  }
}

@keyframes ready-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
