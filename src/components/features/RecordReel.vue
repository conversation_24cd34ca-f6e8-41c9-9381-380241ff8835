<template>
<div class="reel field has-addons">
  <p class="control"><button @click="jumpTo(firstCall)" :disabled="!isFirstCallEnabled" class="button is-small">
    <i class="fal fa-arrow-to-left"></i>
  </button></p>
  <p class="control"><button @click="jumpTo(previousCall)" :disabled="!isPreviousCallEnabled" class="button is-small">
    <i class="fal fa-arrow-left"></i>
  </button></p>

  <p class="control">
    <span class="select is-small">
      <select v-model="recordKeyProxy" @change="jumpTo()">
        <option v-for="(call, index) in cachedRecords" :key="index">{{ call }}</option>
      </select>
    </span>
  </p>

  <p class="control"><button @click="jumpTo(nextCall)" :disabled="!isNextCallEnabled" class="button is-small">
    <i class="fal fa-arrow-right"></i>
  </button></p>
  <p class="control"><button @click="jumpTo(lastCall)" :disabled="!isLastCallEnabled" class="button is-small">
    <i class="fal fa-arrow-to-right"></i>
  </button></p>
</div>
</template>

<script>import { map, indexOf, first, nth, last, isFunction } from 'lodash-es';


import { mapGetters } from 'vuex';

export default {
  name: 'record-reel',

  props: {
    keyAlias: {},
    recordKey: {},
    exitSpeedBumps: { type: Array, default: () => [] }
  },

  inject: ['interruptExit'],

  data () {
    return {
      intendedRecordKey: null
    };
  },

  computed: {
    ...mapGetters([
      '__cachedRecords',
      '__selectedRecords'
    ]),

    recordKeyProxy: {
      get () {
        return this.recordKey;
      },
      set (value) {
        this.intendedRecordKey = value;
      }
    },

    cachedRecords () {
      if (this.__selectedRecords.length > 1) {
        return map(this.__selectedRecords, record => {
          return record[this.keyAlias];
        });
      }

      return this.__cachedRecords;
    },

    currentCallPosition () {
      return indexOf(this.cachedRecords, this.recordKeyProxy);
    },

    firstCall () {
      return first(this.cachedRecords);
    },

    previousCall () {
      let recordKey = nth(this.cachedRecords, this.currentCallPosition - 1);

      if (this.recordKey === this.firstCall) recordKey = this.firstCall;

      return recordKey;
    },

    nextCall () {
      let recordKey = nth(this.cachedRecords, this.currentCallPosition + 1);

      if (this.recordKey === this.lastCall) recordKey = this.lastCall;

      return recordKey;
    },

    lastCall () {
      return last(this.cachedRecords);
    },

    isFirstCallEnabled () {
      return this.$store.state.call.isCallReelEnabled && this.firstCall !== this.recordKey;
    },

    isPreviousCallEnabled () {
      return this.$store.state.call.isCallReelEnabled && this.previousCall !== this.recordKey;
    },

    isNextCallEnabled () {
      return this.$store.state.call.isCallReelEnabled && this.nextCall !== this.recordKey;
    },

    isLastCallEnabled () {
      return this.$store.state.call.isCallReelEnabled && this.lastCall !== this.recordKey;
    }
  },

  methods: {
    async jumpTo (recordKey = null) {
      if (isFunction(this.interruptExit)) {
        await this.interruptExit();
      }

      this.$emit('jump', recordKey || this.intendedRecordKey);
    }
  }
};
</script>

<style scoped>
.control {
  width: auto;
}

.reel {
  margin: 0;
}
</style>
