<template>
  <div id="settings-view">
      <app-titlebar class="title-bar" title="Columns"></app-titlebar>

      <section class="visible-columns">
        <header class="_title">Visible Columns</header>
        <draggable class="draggable" v-model="currentGrid.Columns" @start="drag=true" @end="drag=false">
          <transition-group class="_list" name="smooth-list" tag="ul" :css="false">
            <li class="_item" v-for="(column, index) in currentGrid.Columns" :key="column.ID">
              <div>{{ column.Name }}</div>
              <button class="button is-small" @click="removeColumn(index)">Remove</button>
            </li>
          </transition-group>
        </draggable>
      </section>

      <section class="hidden-columns">
        <header class="_title">Hidden Columns</header>
        <transition-group class="_list" name="smooth-list" tag="ul" :css="false">
          <li class="_item" v-for="column in filteredHiddenColumns" :key="column.ID">
            <div>{{ column.Name }}</div>
            <button class="button is-small" @click="addColumn(column)">Add</button>
          </li>
        </transition-group>
      </section>

      <app-footerbar class="footer-bar">
        <template slot="left">
          <app-checkbox v-model="advancedColumnsVisible" orient="row">
            Advanced Columns
          </app-checkbox>
        </template>

        <app-button @click="navigateBack" type="default">Cancel</app-button>
        <app-button @click="saveSettings" type="primary">Save</app-button>
      </app-footerbar>
  </div>
</template>

<script>import { find, differenceBy, reject } from 'lodash-es';


import { mapGetters } from 'vuex';
import draggable from 'vuedraggable';
import { FIELD_DATA_TYPE } from '@/config';

export default {
  name: 'GridColumns',

  components: {
    draggable
  },

  props: {
    routeKey: {
      type: String,
      required: false
    }
  },

  data () {
    return {
      columnPool: [],
      noun: this.$route.query.noun,
      advancedColumnsVisible: false,
      drag: false
    };
  },

  computed: {
    ...mapGetters(['RECORDS__settings']),

    currentGrid () {
      return find(this.RECORDS__settings.Grids, ['Key', this.routeKey || this.$route.params.key]);
    },

    hiddenColumns () {
      return differenceBy(this.columnPool, this.currentGrid.Columns, 'Key');
    },

    filteredHiddenColumns () {
      if (this.advancedColumnsVisible) {
        return this.hiddenColumns;
      }

      return reject(this.hiddenColumns, ['Type', FIELD_DATA_TYPE.FOREIGN_KEY]);
    }
  },

  methods: {
    navigateBack () {
      this.$router.go(-1);
    },

    saveSettings () {
      this.$store.dispatch('RECORDS__saveSettings', {
        noun: this.noun,
        data: this.RECORDS__settings,
        callback: () => this.navigateBack()
      });
    },

    fetchAvailableColumns () {
      this.$store.dispatch('USERVIEW__getAvaliableColumns', {
        viewKey: this.RECORDS__settings.Key,
        gridKey: this.routeKey || this.$route.params.key,
        callback: response => {
          this.columnPool = response;
        }
      });
    },

    addColumn (column) {
      const updatedColumn = this.getColumnDefaults(column);
      this.currentGrid.Columns.push(updatedColumn);
    },

    getColumnDefaults (column) {
      return {
        ...column,
        OrderPriority: '0',
        Width: '200'
      };
    },

    removeColumn (index) {
      this.currentGrid.Columns.splice(index, 1);
    }
  },

  mounted () {
    this.fetchAvailableColumns();
  }
};
</script>
