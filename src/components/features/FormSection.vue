<template>
  <div class="form-section"
    :data-expanded="expandedProxy"
    :id="idProxy"
    @keydown.tab.exact.capture="tabForward"
    @keydown.shift.tab.exact.capture="tabBack"
    @keydown.alt.down.exact.capture="altForward"
    @keydown.alt.up.exact.capture="altBack">
    <div class="_title" @click="toggle" :title="title">
      <transition name="flip-horizontal" mode="out-in">
        <div class="_value" v-if="expandedProxy" key="value" v-html="title"></div>
        <div class="_expander" v-else key="expander"><i class="fal fa-chevron-right"></i></div>
      </transition>
    </div>

    <section class="_placeholder" @click="toggle">
      <span class="_title" v-html="title"></span>

      <span class="_glance">
        <slot name="glance"></slot>
      </span>
    </section>

    <section class="_body">
      <slot></slot>
    </section>
  </div>
</template>

<script>
import { trim, uniqueId } from 'lodash-es';

import {
  EVENT_SECTION_EXPANDED,
  EVENT_JUMP_BACK_SECTION,
  EVENT_JUMP_FORWARD_SECTION
} from '@/config.js';

export default {
  name: 'form-section',

  props: {
    id: { type: String, default: null },
    title: { type: String, default: '' },
    fixed: { type: Boolean, default: false },
    expanded: { type: Boolean, default: false },
    landingPoint: { type: String, default: null },
    jumpBackPoint: { type: String, default: null },
    jumpForwardPoint: { type: String, default: null }
  },

  data () {
    return {
      idProxy: this.id || uniqueId('form-section-'),
      expandedProxy: null
    };
  },

  computed: {
    jumpBackPointProxy () {
      return this.jumpBackPoint || this.landingPoint;
    }
  },

  methods: {
    toggle () {
      if (this.fixed) return;

      this.expandedProxy = !this.expandedProxy;

      if (!this.expandedProxy) return;

      this.setFocus();

      this.$hub.$emit(EVENT_SECTION_EXPANDED, this.idProxy);
    },

    setFocus () {
      this.$nextTick(() => {
        if (!this.landingPoint) return;

        document.getElementById(this.landingPoint).focus();
      });
    },

    tabForward (event) {
      if (this.fixed) return;

      if (event.target.id === this.jumpForwardPoint) {
        event.preventDefault();
        this.$hub.$emit(EVENT_JUMP_FORWARD_SECTION);
      }
    },

    tabBack (event) {
      if (this.fixed) return;

      if (event.target.id === this.jumpBackPointProxy) {
        event.preventDefault();
        this.$hub.$emit(EVENT_JUMP_BACK_SECTION);
      }
    },

    altForward () {
      if (this.fixed) return;

      this.$hub.$emit(EVENT_JUMP_FORWARD_SECTION);
    },

    altBack () {
      if (this.fixed) return;

      this.$hub.$emit(EVENT_JUMP_BACK_SECTION);
    }
  },

  mounted () {
    if (this.fixed) {
      this.expandedProxy = true;
    }

    if (this.expanded) {
      this.$nextTick(() => {
        this.toggle();
      });
    }

    this.$hub.$on(EVENT_SECTION_EXPANDED, elementId => {
      if (elementId[0] === '*') {
        this.expandedProxy = trim(elementId, '*') === 'true';
        return;
      }

      if (this.fixed) return;

      this.expandedProxy = elementId === this.idProxy;

      if (!this.expandedProxy) return;

      this.setFocus();
    });
  }
};
</script>

<style scoped>
.form-section {
  --collapsed-height: 4rem;
  --local-padding: 0.8rem;

  display: flex;
  flex-direction: column;

  padding: 0;
  margin: 0;
  max-width: var(--font-line-length);
  max-height: auto;
  background-color: var(--body-bg);
  border-radius: var(--border-radius-100);
  box-shadow: var(--box-shadow-100);
  transition: all var(--transition-fast) ease-in-out;

  &:focus-within {
    box-shadow: var(--box-shadow-200);

    > ._title {
      background-color: var(--primary-light-200);
    }
  }

  > ._title {
    color: white;
    height: var(--collapsed-height);
    background: var(--primary);
    letter-spacing: var(--font-letter-spacing);
    border-top-left-radius: var(--border-radius-100);
    border-top-right-radius: var(--border-radius-100);
    overflow: hidden;
    transition: all var(--transition-fast) ease-in-out;

    ._value {
      text-transform: capitalize;
      font-weight: bold;
      padding: 0.5rem 0.75rem;
      position: sticky;
      top: -1rem;
      white-space: nowrap;
    }

    ._expander {
      display: flex;
      justify-content: center;
      align-items: center;
      height: var(--collapsed-height);
    }
  }

  > ._placeholder {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-variant: all-small-caps;
    font-weight: bold;
    padding: 0 var(--local-padding);
    cursor: pointer;

    ._title {
      letter-spacing: var(--font-letter-spacing);
    }

    ._glance {
      font-variant: none;
      font-weight: normal;
    }
  }

  > ._body {
    flex: 1;
    display: none;
  }

  &[data-expanded] {
    > ._title {
      height: auto;
    }

    > ._placeholder {
      display: none;
    }

    > ._body {
      display: inline-block;
    }
  }
}
</style>
