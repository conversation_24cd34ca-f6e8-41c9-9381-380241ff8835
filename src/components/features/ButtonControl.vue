<template>
<button
  :id="id"
  :class="buttonClasses"
  @click="$emit('click', $event)"
  @mouseenter="$emit('mouseenter', $event)"
  @mouseleave="$emit('mouseleave', $event)"
  :disabled="disabled">
  <slot></slot>
</button>
</template>

<script>
import { set } from 'lodash-es';

export default {
  name: 'button-control',

  props: {
    id: { type: String, required: false, default: '' },
    type: { type: String, required: false, default: '' },
    size: { type: String, required: false, default: 'small' },
    inverted: { type: Boolean, required: false, default: false },
    outlined: { type: Boolean, required: false, default: false },
    disabled: { type: [String, Boolean], required: false, default: false }
  },

  data () {
    return {
      typeClasses: {
        'primary': 'is-primary',
        'info': 'is-info',
        'success': 'is-success',
        'warning': 'is-warning',
        'danger': 'is-danger',
        'white': 'is-white',
        'light': 'is-light',
        'dark': 'is-dark',
        'black': 'is-black',
        'text': 'is-text'
      },
      sizeClasses: {
        'small': 'is-small',
        'normal': '',
        'medium': 'is-medium',
        'large': 'is-large'
      }
    };
  },

  computed: {
    buttonClasses () {
      let classes = {};

      set(classes, 'button', true);

      const typeClass = this.typeClasses[this.type];
      if (typeClass) {
        set(classes, typeClass, true);
      }

      const sizeClass = this.sizeClasses[this.size];
      if (sizeClass) {
        set(classes, sizeClass, true);
      }

      set(classes, 'is-inverted', this.inverted);
      set(classes, 'is-outlined', this.outlined);

      return classes;
    }
  }
};
</script>

<style scoped>
.is-primary {
  background: var(--button-primary-bg);
  font-weight: bold;

  &[disabled] {
    background: var(--button-primary-bg);
  }

  &:hover {
    background-color: var(--button-primary-bg-hover);
  }

  &:active {
    background-color: var(--button-primary-bg-press);
  }
}
</style>
