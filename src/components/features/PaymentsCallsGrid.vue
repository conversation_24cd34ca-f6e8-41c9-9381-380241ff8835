<template>
  <div class="data-grid" :id="gridId">
    <app-titlebar :title="title">
      <template slot="center">
        <div class="is-hidden-mobile">
          <app-button type="text" :disabled="true" style="opacity: 1">
            {{ data.length }} Records
          </app-button>
          <app-button
            type="text"
            @click="autoSizeColumns"
            :disabled="!grid.Key || data.length === 0 || isAutoSizing"
            title="Auto-size columns to fit content">
            {{ isAutoSizing ? 'Sizing...' : 'Fit' }}
          </app-button>
          <app-button type="text" @click="refresh" :title="'Refreshed at ' + refreshedAt | verbalTime">
            Refresh
          </app-button>
          <app-button
            class="-filters"
            type="text"
            v-if="filtersButtonVisible"
            @click="editFilters('filters')">
            Filters&nbsp;<i class="fas fa-circle-small pure-yellow" v-if="filters.length" :title="filters.length + ' filters applied'"></i>
          </app-button>
          <app-button
            @click="editFilters('form')"
            type="text"
            v-if="findButtonVisible">
            Find
          </app-button>
          <!-- <filters-glance
            :filters="filters"
            @editFilters="editFilters">
          </filters-glance> -->
          <app-button
            type="text"
            v-if="columnsButtonVisible"
            @click="editSettings"
            :disabled="!grid.Key">
            Columns
          </app-button>
          <app-dropdown v-if="canExport">
            <app-button type="text" :disabled="!grid.Key">
              Export
            </app-button>
            <template slot="menu">
              <a href="#" class="dropdown-item" @click.prevent="handleExport('CSVFile')">CSV</a>
              <a href="#" class="dropdown-item" @click.prevent="handleExport('PDF')">PDF</a>
            </template>
          </app-dropdown>
        </div>
      </template>
      <slot name="context-tools"></slot>
    </app-titlebar>

    <transition name="fade" mode="out-in">
      <section class="viewport" v-if="!showLoader">
        <div class="_liner">
          <div class="-column -action">
            <div class="-row -header">
              <button class="distribute"
                title="Distribute this payment across calls."
                :disabled="!hasSelectedPayment"
                @click="$emit('distributePaymentBalance')">
                Auto Apply
              </button>
            </div>
            <!-- 2021-6-30 -->
            <!-- Disabling paging for now. Unsure if it will come back or take a different form. -->
            <!-- <div :class="rowClasses(record)" v-for="record in book.page.records" :key="record[config.recordKeyName]"> -->
            <div :class="rowClasses(record, { allow_overflow: true })" v-for="record in data" :key="record[config.recordKeyName]">
              <div id="application-controls">
                <button class="_assign"
                  v-show="record.pendingApplicationBalance === 0"
                  title="Set to maximum possible dollars."
                  :disabled="!hasSelectedPayment"
                  @click="$emit('fillPendingApplicationBalance', record)">
                  <i class="far fa-chevron-right"></i>
                </button>
                <button class="_assign"
                  v-show="record.pendingApplicationBalance !== 0"
                  title="Reset to zero dollars."
                  @click="$emit('clearPendingApplicationBalance', record)">
                  <i class="far fa-xmark"></i>
                </button>

                <input class="_amount"
                  type="text"
                  v-model.number="record.pendingApplicationBalance"
                  :data-active="record.pendingApplicationBalance > 0"
                  :disabled="!hasSelectedPayment"
                  @keyup="$emit('changePendingPayment', record)" />

                <label class="_short"
                  :data-active="record.isShort"
                  :data-disabled="Number(record.pendingApplicationBalance) === Number(record.tcPrice)"
                  title="Mark as a short payment.">
                  SP
                  <input type="checkbox"
                    v-model="record.isShort"
                    :disabled="Number(record.pendingApplicationBalance) === Number(record.tcPrice)"
                    @change="$emit('changePendingPayment', record)">
                </label>

                <a class="-button -error button is-small" v-if="hasErrors(record)">
                  <i class="-icon fas fa-exclamation-circle"></i>
                  <ul class="-detail">
                    <li v-for="(error, index) in record.Errors" :key="index">{{ error.Message[0] }}</li>
                  </ul>
                </a>
              </div>
            </div> <!-- /-row -->
          </div> <!-- /-column -->

          <div class="-column" v-for="column in columns" :style="getColumnStyle(column)" :key="column.ID">
            <div class="-row -header">
              <span class="-sort" v-if="sortIcon(column)">
                <span class="-priority" v-show="sortableColumns.length > 1">{{ humanizeSortPriority(column) }}</span>
                <i class="-orientation fas" :class="sortIcon(column)"></i>
              </span>
              <span class="-name" @click="sortBy(column)" :title="column.Name">{{ column.Name }}</span>
              <div class="-resizer" :data-id="column.ID" :data-grid-id="gridId"></div>
            </div>
            <!-- <div :class="rowClasses(record)" v-for="record in book.page.records" @click.capture="openRecord(record)" :title="cellValue(record, column)" :key="record[config.recordKeyName]"> -->
            <div :class="rowClasses(record)" v-for="record in data" @click.capture="openRecord(record)" :title="cellValue(record, column)" :key="record[config.recordKeyName]">
              <span :class="cellClasses(record, column)">
                {{ cellValue(record, column) }}
              </span>
            </div> <!-- /-row -->
          </div> <!-- /-column -->

          <div class="-column -flexer">
            <div class="-row -header"></div>
            <!-- <div class="-row" v-for="(record, index) in book.page.records" :key="index"></div> -->
            <div class="-row" v-for="(record, index) in data" :key="index"></div>
          </div>
        </div>

        <div class="_floating-tools">
          <div class="pill">
            <slot name="floating-tools"></slot>
            <!-- <template v-if="pageResults && book.length">
              <div class="divider"></div>
              <app-button class="button is-white" @click="pagePrevious">
                <i class="far fa-arrow-left"></i>
              </app-button>
              <app-button class="button is-white" @click="pageNext">
                <i class="far fa-arrow-right"></i>
              </app-button>
            </template> -->
          </div>
        </div>
      </section> <!-- /viewport -->
      <div class="loadport" v-else key="loader">
        <app-loader class="_indicator"></app-loader>
      </div>
    </transition>
  </div>
</template>

<script>
import { 
  findIndex, forEach, range, reject, find, has, get, max, filter, 
  orderBy, uniqueId, toNumber, debounce, toString 
} from 'lodash-es';
import is from 'is_js';
import Access from '@/utils/access.js';
import { affirmative, usd } from '@/utils/filters.js';
import { mapGetters } from 'vuex';
import FiltersGlance from '../features/FiltersGlance.vue';

export default {
  name: 'data-grid',

  props: {
    data: { type: Array },
    grid: { type: Object },
    title: { type: String, required: false },
    config: { type: Object },
    access: { type: Object, required: false },
    pageResults: { type: Boolean, default: true },
    showLoader: { type: Boolean, default: false },
    refreshedAt: { type: String, required: false },
    selectedPayment: { type: Object, required: false },
    findButtonVisible: { type: Boolean, default: true },
    actions: { type: [String, Boolean], required: false, default: false },
    filtersButtonVisible: { type: Boolean, required: false, default: true },
    columnsButtonVisible: { type: Boolean, required: false, default: true },
    multiselect: { type: [String, Boolean], required: false, default: false }
  },

  components: {
    FiltersGlance
  },

  data () {
    return {
      gridId: uniqueId('grid-'),
      clickedRow: {},

      // Flag to indicate when auto-sizing is in progress
      isAutoSizing: false,

      // Cache for column styles to ensure consistency
      columnStyleCache: new Map(),

      book: {
        length: 0,
        page: {
          records: [],
          number: 0,
          size: 50
        }
      },

      workingColumn: {
        record: {},
        endX: null,
        startX: null,
        minWidth: 75,
        startWidth: 0,
        isResizing: false
      }
    };
  },

  computed: {
    ...mapGetters([
      'USER__state',
      '__selectedRecords',
      'TOPSCOMPANY__settings'
    ]),

    hasSelectedPayment () {
      return !!(this.selectedPayment && typeof this.selectedPayment === 'object' && Object.keys(this.selectedPayment).length > 0);
    },

    canExport () {
      return get(this.access, 'export', true);
    },

    restrictedColumns () {
      // Note: Restricted by default
      let columns = [
        'fCommission1',
        'fCommission2',
        'vc255AccountingNotes'
      ];

      if (Access.has('drivers.commission')) {
        columns = filter(columns, ['fCommission1', 'fCommission2']);
      }

      if (Access.has('calls.accountingNotes')) {
        columns = filter(columns, 'vc255AccountingNotes');
      }

      return columns;
    },

    computedWidth () {
      // Ensure we have valid numbers for all values
      const startWidth = parseInt(this.workingColumn.startWidth) || 100;
      const startX = parseInt(this.workingColumn.startX) || 0;
      const endX = parseInt(this.workingColumn.endX) || 0;
      const delta = endX && startX ? (endX - startX) : 0;
      const value = startWidth + delta;
      const minWidth = this.workingColumn.minWidth || 75;

      return Math.max(value, minWidth);
    },

    columns: {
      get () {
        if (!has(this.grid, 'Columns')) return [];

        return filter(this.grid.Columns, column => {
          return !is.inArray(column.ID, this.restrictedColumns);
        });
      },
      set (columns) {
        if (has(this.grid, 'Columns')) {
          this.grid.Columns = columns;
        }
      }
    },

    sortableColumns () {
      let columns = [];

      columns = filter(this.columns, column => toString(column.Ordered) === 'true');
      columns = orderBy(columns, 'OrderPriority', 'asc');

      return columns;
    },

    filters () {
      return get(this.grid, 'Filters', []);
    }
  },

  watch: {
    'data' () {
      if (this.data.length > 0) {
        this.setBookLength();
        this.setPageData();
      }
    },
    // Clear the column style cache when the grid changes
    grid: {
      handler() {
        if (this.columnStyleCache) {
          this.columnStyleCache.clear();
        }
      },
      deep: true
    }
  },

  methods: {
    pagePrevious () {
      if (this.book.page.number > 0) {
        --this.book.page.number;
      }

      this.setPageData();
    },

    pageNext () {
      if (this.book.page.number >= this.book.length) return;

      ++this.book.page.number;

      this.setPageData();
    },

    setPageData () {
      if (!this.pageResults) {
        this.book.page.records = this.data;
        return;
      }

      let start = this.book.page.number * this.book.page.size;
      let end = start + this.book.page.size;

      this.book.page.records = this.data.slice(start, end);
    },

    setBookLength () {
      let fullPages = Math.floor(this.data.length / this.book.page.size) - 1;
      let overflowPages = this.data.length % this.book.page.size > 0 ? 1 : 0;

      this.book.length = fullPages + overflowPages;
    },

    handleExport (format) {
      this.exportData(format);
    },

    toggleSelectionAll () {
      let selectedRecords = this.$store.state.payment.selectedCalls;

      if (selectedRecords.length === 0) {
        this.data.forEach(record => {
          selectedRecords.push(record);
        });
      } else {
        selectedRecords = [];
      }

      this.$store.state.payment.selectedCalls = selectedRecords;
    },

    toggleSelection (event, record) {
      let selectedRecords = this.$store.state.payment.selectedCalls;

      if (event.shiftKey) {
        // Select range
        if (!this.clickedRow) return;

        let start = findIndex(this.data, this.clickedRow);
        let end = findIndex(this.data, record);

        start += (start < end) ? 1 : -1;
        end += (start < end) ? 1 : -1;

        forEach(range(start, end), index => {
          selectedRecords.push(this.data[index]);
        });
      } else {
        // Select single record
        if (this.isSelected(record)) {
          selectedRecords = reject(selectedRecords, [this.config.recordKeyName, record[this.config.recordKeyName]]);
        } else {
          selectedRecords.push(record);
        }
      }

      this.clickedRow = record;
      this.$store.state.payment.selectedCalls = selectedRecords;
    },

    openRecord (record) {
      this.$emit('openRecord', record);
    },

    isSelected (record) {
      const keyName = this.config.recordKeyName;
      const target = find(this.$store.state.payment.selectedCalls, [keyName, record[keyName]]);

      if (!target) return;

      // Clarify multiple dispatches on a single call
      const dispatchKeyName = has(target, 'CAL_lDispatchKey') ? 'CAL_lDispatchKey' : 'lDispatchKey';

      return target[keyName] === record[keyName] &&
        get(target, dispatchKeyName, '') === get(record, dispatchKeyName, '');
    },

    refresh () {
      this.$emit('refresh');
    },

    editFilters (mode = 'form') {
      this.$router.push({
        name: 'GridSearch',
        params: { key: this.grid.Key },
        query: {
          noun: this.config.noun,
          mode: mode
        }
      });
    },

    editSettings () {
      this.$router.push({
        name: 'GridColumns',
        params: { key: this.grid.Key },
        query: { noun: this.config.noun }
      });
    },

    exportData (format) {
      this.$emit('exportData', { format: format, gridKey: this.grid.Key });
    },

    formatData (record, column) {
      // if (column.ID === 'dLastModified') {
      //   return relativeDate(record[column.ID]);
      // }

      if (is.startWith(column.ID, 'b')) {
        return affirmative(record[column.ID]);
      }

      return record[column.ID];
    },

    sortBy (column) {
      if (column.ID === 'lAppointmentETAMinutes') return;

      let sortBoolean = this.getSortBoolean(column);
      let ascendingBoolean = this.getAscendingBoolean(column);
      let sortPriority = this.getSortPriority(column);

      column.Ordered = toString(sortBoolean);
      column.Ascending = toString(ascendingBoolean);
      column.OrderPriority = toString(sortPriority);

      this.optimizeSortPriority();

      this.$emit('save', this.grid);
    },

    getSortBoolean (column) {
      return this.getSortOrientation(column) !== 'desc';
    },

    getAscendingBoolean (column) {
      return this.getSortOrientation(column) === '';
    },

    getSortPriority (column) {
      if (this.getSortOrientation(column) === 'asc') return column.OrderPriority;

      if (this.getSortOrientation(column) === 'desc') return 0;

      let highestPriority = max(this.sortableColumns, 'OrderPriority') || { OrderPriority: 0 };
      let priority = toNumber(highestPriority.OrderPriority);

      return priority + 1;
    },

    getSortOrientation ({ Ordered, Ascending }) {
      if (toString(Ordered) === 'false') return '';

      return toString(Ascending) === 'false' ? 'desc' : 'asc';
    },

    humanizeSortPriority ({ Ordered, OrderPriority }) {
      if (Ordered !== 'true') return '';

      return toString(OrderPriority) !== '0' ? OrderPriority : '';
    },

    sortIcon (column) {
      let icons = {
        '': '',
        asc: 'fa-sort-down',
        desc: 'fa-sort-up'
      };

      return icons[this.getSortOrientation(column)];
    },

    optimizeSortPriority () {
      forEach(this.sortableColumns, (column, index) => {
        column.OrderPriority = index + 1;
      });
    },

    beginResize (event) {
      if (event.target.dataset.gridId !== this.gridId) return;
      if (event.target.className !== '-resizer') return;

      this.workingColumn.record = find(this.columns, ['ID', event.target.dataset.id]);

      if (!this.workingColumn.record) return;

      this.workingColumn.startWidth = this.workingColumn.record.Width || 100;
      this.workingColumn.isResizing = true;
      this.workingColumn.startX = event.x;
    },

    isResizing (event) {
      if (!this.workingColumn.isResizing || !this.workingColumn.record) return;

      event.preventDefault();
      this.workingColumn.endX = event.x;

      if (this.workingColumn.record) {
        this.workingColumn.record.Width = this.computedWidth;
      }
    },

    endResize (event) {
      if (!this.workingColumn.isResizing) return;

      const isGridElement = event.target.closest(`#${this.gridId}`);
      if (!isGridElement && event.target.className !== '-resizer') {
        // Still end the resize operation even if clicked elsewhere
      }

      this.workingColumn.isResizing = false;

      if (this.workingColumn.record && this.workingColumn.record.ID) {
        this.columnStyleCache.delete(this.workingColumn.record.ID);
        this.saveColumnSize();
      }
    },

    getColumnStyle(column) {
      // If column is being resized, don't use cache
      if (this.workingColumn.isResizing && this.workingColumn.record && this.workingColumn.record.ID === column.ID) {
        const width = column.Width || 100;
        return {
          width: `${width}px`,
          minWidth: `${width}px`,
          maxWidth: `${width}px`,
          '--width': `${width}px`
        };
      }

      // Use cached style if available to ensure consistency during scrolling
      const columnId = column.ID || '';
      if (!this.columnStyleCache.has(columnId) || this.columnStyleCache.get(columnId).width !== column.Width) {
        const width = column.Width || 100;
        const style = {
          width: `${width}px`,
          minWidth: `${width}px`,
          maxWidth: `${width}px`,
          '--width': `${width}px`
        };

        // Cache the style object
        this.columnStyleCache.set(columnId, {
          style,
          width: column.Width
        });
      }

      // Return the cached style
      return this.columnStyleCache.get(columnId).style;
    },

    saveColumnSize: debounce(function () {
      this.$emit('save', this.grid);
    }, 1000),

    autoSizeColumns() {
      if (!this.data || this.data.length === 0 || !this.columns || this.columns.length === 0 || this.isAutoSizing) {
        return;
      }

      // Set loading state
      this.isAutoSizing = true;

      // Use setTimeout to allow the UI to update before starting the calculation
      setTimeout(() => {
        try {
          // Create a temporary canvas to measure text width
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');

          // Get the computed style of the grid cells to use the same font
          const gridElement = document.getElementById(this.gridId);
          if (!gridElement) {
            this.isAutoSizing = false;
            return;
          }

          const cellStyle = window.getComputedStyle(gridElement.querySelector('.-row') || document.body);
          context.font = `${cellStyle.fontWeight} ${cellStyle.fontSize} ${cellStyle.fontFamily}`;

          // For large datasets, sample a subset of the data to improve performance
          const sampleSize = Math.min(this.data.length, 1000);
          const sampleStep = Math.max(1, Math.floor(this.data.length / sampleSize));
          const sampledData = [];

          for (let i = 0; i < this.data.length; i += sampleStep) {
            sampledData.push(this.data[i]);
          }

          // Calculate optimal width for each column
          const updatedColumns = this.columns.map(column => {
            // Start with the column header width
            let maxWidth = context.measureText(column.Name || '').width + 40; // Add padding

            // Check sampled data for this column
            sampledData.forEach(record => {
              const cellContent = this.cellValue(record, column) || '';
              const cellWidth = context.measureText(String(cellContent)).width + 40; // Add padding
              maxWidth = Math.max(maxWidth, cellWidth);
            });

            // Respect minimum width
            const minWidth = this.workingColumn.minWidth;
            const optimalWidth = Math.max(Math.ceil(maxWidth), minWidth);

            // Create a new column object to avoid mutating the original
            return {
              ...column,
              Width: optimalWidth
            };
          });

          // Update the columns
          if (has(this.grid, 'Columns')) {
            // Find the columns in the grid that match our visible columns
            const gridColumns = this.grid.Columns.map(gridCol => {
              const matchingCol = updatedColumns.find(col => col.ID === gridCol.ID);
              if (matchingCol) {
                return {
                  ...gridCol,
                  Width: matchingCol.Width
                };
              }
              return gridCol;
            });

            // Update the grid columns
            this.grid.Columns = gridColumns;

            // Clear the column style cache
            this.columnStyleCache.clear();

            // Save the changes
            this.saveColumnSize();
          }
        } catch (error) {
          console.error('Error auto-sizing columns:', error);
        } finally {
          // Reset loading state
          this.isAutoSizing = false;
        }
      }, 50); // Small delay to allow UI update
    },

    cellValue (record, column) {
      if (is.startWith(column.ID, 'b')) { return affirmative(record[column.ID]); }
      if (column.ID === 'tcBalance') { return usd(record.realTimeBalance); }

      return record[column.ID];
    },

    selectorClasses (record) {
      return {
        'far': true,
        'fa-square': !this.isSelected(record),
        'fa-check-square': this.isSelected(record)
      };
    },

    rowClasses (record, options = {}) {
      return {
        '-row': true,
        '-highlighted': this.isSelected(record),
        '-allow-overflow': options.allow_overflow || false,
        '-ghost': is.inArray(toString(get(record, 'bActive', '1')), ['0', 'false']),
        '-hold': this.isOnHold(record)
      };
    },

    isOnHold (record) {
      let isOnHold1 = Number(get(record, 'HLD_bHoldOn', 0));
      let isOnHold2 = get(record, 'HLD_sHoldOn', 0);
      let isOnHold3 = Number(get(record, 'bHold', ''));

      return isOnHold1 === 1 || isOnHold2 === 'On' || isOnHold3 === 1;
    },

    cellClasses (record, column) {
      // ...
    },

    hasErrors (record) {
      return has(record, 'Errors');
    }
  },

  created () {
    document.addEventListener('mousedown', this.beginResize, { passive: true });
    document.addEventListener('mouseup', this.endResize, { passive: true });
    document.addEventListener('mousemove', this.isResizing);
  },

  destroyed () {
    document.removeEventListener('mousedown', this.beginResize, { passive: true });
    document.removeEventListener('mouseup', this.endResize, { passive: true });
    document.removeEventListener('mousemove', this.isResizing);
  }
};
</script>
