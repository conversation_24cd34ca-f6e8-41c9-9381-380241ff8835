<template>
  <app-button
    class="-filters"
    :class="{ '-active': filtersProxy.length }"
    type="text"
    @click="editFilters"
    @mouseenter="filtersGlanceVisible = true"
    @mouseleave="filtersGlanceVisible = false">
      Filters <span v-if="filtersProxy.length">&nbsp;&mdash; {{ filtersProxy.length }}</span>
      <transition name="drop-down">
        <ul class="filters-glance" v-if="filtersGlanceVisible && filtersProxy.length > 0 && availableFields.length > 0">
          <li v-for="(clause, index) in filtersProxy" :key="index">
            <span class="_and-or">{{ resolveAndOr(clause, index) }}</span>
            <span class="_not" v-if="clause.Not">exclude</span>
            <span class="_open-paren" v-if="clause.OpenParen">(</span>
            <span class="_field-name">{{ resolveFieldName(clause.FieldID) }}</span>
            <span class="_operator">{{ clause.Operator }}</span>
            <span class="_display-value">{{ clause.DisplayValue }}</span>
            <span class="_close-paren" v-if="clause.CloseParen">)</span>
          </li>
        </ul>
      </transition>
  </app-button>
</template>

<script>import { forEach, find, get } from 'lodash-es';


import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'filters-glance',

  props: {
    filters: { type: Array, required: true }
  },

  data () {
    return {
      availableFields: [],
      filtersGlanceVisible: false
    };
  },

  computed: {
    ...mapGetters([
      'RECORDS__settings'
    ]),

    filtersProxy () {
      return forEach(this.filters, clause => {
        if ('And' in clause) clause.And = this.toBoolean(clause.And);
        if ('Or' in clause) clause.Or = this.toBoolean(clause.Or);
        if ('Not' in clause) clause.Not = this.toBoolean(clause.Not);
        if ('OpenParen' in clause) clause.OpenParen = this.toBoolean(clause.OpenParen);
        if ('CloseParen' in clause) clause.CloseParen = this.toBoolean(clause.CloseParen);
      });
    }
  },

  methods: {
    ...mapActions([
      'USERVIEW__getAvaliableFields'
    ]),

    toBoolean (value) {
      if (value.toString() === 'true') return true;
      if (value.toString() === '1') return true;

      return false;
    },

    resolveFieldName (fieldID) {
      let targetField = find(this.availableFields, ['ID', fieldID]);

      return get(targetField, 'Name', '');
    },

    resolveAndOr (clause, index) {
      if (index === 0) return '';
      if (clause.And) return 'and';
      if (clause.Or) return 'or';

      return '';
    },

    getAvailableFields () {
      if (this.availableFields.length > 0) return;

      this.USERVIEW__getAvaliableFields({
        viewKey: this.RECORDS__settings.Key,
        gridKey: this.RECORDS__settings.Grids[0].Key,
        callback: response => {
          this.availableFields = response;
        }
      });
    },

    editFilters () {
      this.$emit('editFilters');
    }
  },

  mounted () {
    this.getAvailableFields();
  }
};
</script>
