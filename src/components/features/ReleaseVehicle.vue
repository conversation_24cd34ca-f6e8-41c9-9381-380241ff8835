<template>
  <div>
    <app-modal
      :title="`Release Vehicle on Call #${callKey}`"
      @close="$emit('close')"
      :pad="false"
      :show="true">

      <div id="sell-vehicle">
        <section id="confirmation-section" v-if="!confirmationsSatisfied">
          <div class="confirmation" v-if="askToTerminateLien && !lienPromptAcknowledged">
            <i class="icon fad fa-warning"></i>
            <p class="message">I understand that continuing will terminate the lien.</p>

            <tab-group v-model="shouldRemoveLienPricing" v-if="askToRemoveLienPricing">
              <tab-item value="No">Keep lien pricing</tab-item>
              <tab-item value="Yes">Remove lien pricing</tab-item>
            </tab-group>

            <app-button type="primary" @click="acknowledgeLienPrompt">
              Terminate lien
            </app-button>
          </div>

          <div class="confirmation" v-if="askToReleaseHolds && !holdsPromptAcknowledged">
            <i class="icon fad fa-warning"></i>
            <p class="message">I understand that continuing will release all holds.</p>
            <app-button @click="holdsPromptAcknowledged = true">
              Release holds
            </app-button>
          </div>
        </section>

        <template v-if="confirmationsSatisfied">
          <section id="release-type-section">
            <tab-group v-model="releaseType" v-if="releaseTypeVisible">
              <tab-item value="owner">Released to Owner</tab-item>
              <tab-item value="auctioned-etc" :disabled="!canViewAuctionScrappedSold">Auctioned, Scrapped, Sold</tab-item>
            </tab-group>
          </section>

          <section id="services-section" v-if="releaseType === 'auctioned-etc'">
            <h4 class="_title">Services</h4>
            <app-grid-form context="inline">
              <div class="columns is-multiline">
                <div class="column is-6">
                  <app-sale-customer v-model="saleCustomerKey" :required="true" @change="setCallInformation({ callKey, saleCustomerKey })" style="width: 100%">
                    Sale Customer
                  </app-sale-customer>
                </div>
                <div class="column is-3">
                  <app-number v-model="saleTaxRate">
                    Tax Rate
                  </app-number>
                </div>
                <div class="column is-3">
                  <app-select v-model="finalDispositionKey" :options="finalDispositions" keyAlias="Key" valueAlias="Value" :required="true">
                    Release
                  </app-select>
                </div>
              </div>
            </app-grid-form>

            <ul class="services">
              <li class="service" data-shape="header">
                <div class="_name is-upper is-small is-bold is-quiet">Service</div>
                <div class="_price is-upper is-small is-bold is-quiet">Price</div>
                <div class="_taxable is-upper is-small is-bold is-quiet">Taxable</div>
                <div class="_action">
                  <div class="_add">
                    <app-button type="success">
                      <i class="fas fa-plus"></i>
                    </app-button>
                    <select v-model="appliedServiceKeyPlaceholder" @change="addToAppliedServices">
                      <optgroup label="Add a service">
                        <option v-for="saleService in saleServices" :value="saleService.Key" :key="saleService.Key">
                          {{ saleService.Name }}
                        </option>
                      </optgroup>
                    </select>
                  </div>
                </div>
              </li>

              <li class="service" v-for="(service, index) in appliedServices" :key="index">
                <div class="_name">
                  <select v-model="service.Key">
                    <option v-for="saleService in saleServices" :value="saleService.Key" :key="saleService.Key">
                      {{ saleService.Name }}
                    </option>
                  </select>
                </div>
                <div class="_price">
                  <input type="number" v-model.number="service.Price" />
                </div>
                <div class="_taxable">
                  <input type="checkbox" v-model="service.Taxable" />
                </div>
                <div class="_action">
                  <button class="_remove" @click="removeFromAppliedServices(index)">
                    <i class="far fa-xmark"></i>
                  </button>
                </div>
              </li>

              <li class="service" data-shape="tax-balance" v-if="appliedServices.length && taxBalance">
                <div class="_math">
                  <div class="_label is-uppercase is-small is-bold">Taxable Amount</div>
                  <div class="_value is-bold">{{ saleSubtotal | usd }} &cross; {{ saleTaxRate }}%</div>
                </div>
                <div class="_result">
                  <div class="_label is-uppercase is-small is-bold">Tax</div>
                  <div class="_value is-bold">{{ taxBalance | usd }}</div>
                </div>
              </li>

              <li class="service" data-shape="placeholder" v-if="!appliedServices.length">
                No services to show.
              </li>
            </ul>
          </section>

          <section id="payments-section">
            <header class="header">
              <h4 class="_title">Payments</h4>
              <div class="_add">
                <app-button type="success">
                  <i class="fas fa-plus"></i>
                </app-button>
                <select v-model="addPaymentMethod" @change="addPayment">
                  <optgroup label="Select a method">
                    <option value="manual">Manual...</option>
                    <option value="towpay" :disabled="!isTowPayAvailable">TowPay...</option>
                  </optgroup>
                </select>
              </div>
            </header>

            <ul class="payments">
              <details class="payment"
                v-for="(payment, index) in paymentsProxy"
                :open="payment.isExpanded"
                :key="index">

                <summary>
                  <app-data-point class="amount" label="Amount">
                    {{ payment.tcAmount | usd }}
                  </app-data-point>
                  <app-data-point label="Type">
                    <CardType class="payment-type"
                      v-if="isPaymentTypeCreditCard(payment) && payment.lCreditCardTypeKey"
                      :id="payment.lCreditCardTypeKey" />
                    <PaymentType class="payment-type" v-else :id="payment.lPaymentTypeKey" />
                  </app-data-point>
                  <div class="received is-small is-quiet">
                    <EmployeeName class="payment-received-by" :id="payment.lReceivedBy" /><br />
                    {{ payment.dReceived }}
                  </div>

                  <button class="remove-button" @click="removeFromPayments(index)" :disabled="payment.lPaymentKey">
                    <i class="far fa-trash-alt"></i>
                  </button>
                </summary>

                <app-grid-form class="payment-form" context="inline">
                  <div class="columns is-multiline">
                    <div class="column is-6">
                      <app-number v-model.number="payment.tcAmount" :disabled="payment.lPaymentKey">
                        Amount
                      </app-number>
                    </div>
                    <div class="column is-6">
                      <InputPaymentType v-model="payment.lPaymentTypeKey" :required="true" :disabled="payment.lPaymentKey" />
                    </div>
                    <div class="column is-6 is-left">
                      <app-text v-model="payment.vc20PaymentInfo" maxlength="20" @change="maskPaymentInfo(payment)" :disabled="payment.lPaymentKey">
                        Check / Card Number
                      </app-text>
                    </div>
                    <div class="column is-6">
                      <InputCardType v-model="payment.lCreditCardTypeKey" />
                    </div>
                    <div class="column is-12 is-left">
                      <app-text v-model="payment.vc30CardholderName" maxlength="30">
                        Cardholder Name
                      </app-text>
                    </div>
                    <div class="column is-6 is-left is-bottom">
                      <InputEmployee v-model="payment.lReceivedBy" :required="true" :disabled="payment.lPaymentKey" />
                    </div>
                    <div class="column is-6 is-bottom">
                      <app-text v-model="payment.vc20AuthorizationInfo" maxlength="20">
                        Authorization Number
                      </app-text>
                    </div>
                  </div>
                </app-grid-form>
              </details>

              <li class="payment" data-shape="placeholder" v-if="!paymentsProxy.length">
                No payments to show.
              </li>

              <li class="payment" data-shape="refresh">
                <a class="is-small" href="#" @click.prevent="setCallInformation({ callKey, saleCustomerKey })">
                  Not seeing a payment? Tap to refresh.
                </a>
              </li>
            </ul>
          </section>

          <section id="post-release-section">
            <card-swipe @swipe="processSwipe" style="width: 16rem; margin: 0 auto"></card-swipe>

            <app-grid-form context="inline">
              <div class="columns is-multiline">
                <div class="column is-6">
                  <app-date-time v-model="dateOut" :required="true">
                    Date out
                  </app-date-time>
                </div>
                <div class="column is-6">
                  <app-text v-model="Name">
                    Name
                  </app-text>
                </div>
                <div class="column is-12 is-left">
                  <app-text v-model="Address1">
                    Address
                  </app-text>
                </div>
                <div class="column is-12 is-left">
                  <app-text v-model="Address2">
                    Address 2
                  </app-text>
                </div>
                <div class="column is-6 is-left">
                  <app-text v-model="City">
                    City
                  </app-text>
                </div>
                <div class="column is-3">
                  <app-select-state v-model="State">
                    State
                  </app-select-state>
                </div>
                <div class="column is-3">
                  <app-text v-model="Zip">
                    Zip
                  </app-text>
                </div>
                <div class="column is-6 is-left">
                  <app-text v-model="LicenseNum">
                    License Number
                  </app-text>
                </div>
                <div class="column is-6">
                  <app-select-state v-model="LicenseState">
                    License State
                  </app-select-state>
                </div>
                <div class="column is-12 is-left">
                  <app-text v-model="Phone">
                    Phone
                  </app-text>
                </div>
              </div>
            </app-grid-form>
          </section>

          <footer class="footer">
            <app-button type="primary" @click="releaseVehicle" :disabled="!canReleaseVehicle">
              Release Vehicle
            </app-button>

            <div class="final-balance">
              Balance: {{ finalBalance | usd }}
            </div>
          </footer>
        </template>
      </div>
    </app-modal>

    <tow-pay
      :call-key="callKey"
      :customer-key="saleCustomerKey"
      :is-tow-payment="isTowPayment"
      :initial-amount="finalBalance"
      v-if="towPayModal.isModalVisible"
      @close="closeTowPayModal"
      @created="onTowPayPaymentAdded">
    </tow-pay>
  </div>
</template>

<script>
import { get } from 'lodash-es';
import TowPay from './TowPay.vue';
import Access from '@/utils/access.js';
import CardSwipe from '../features/CardSwipe.vue';
import { VALUE_ID } from '@/config.js';

export default {
  name: 'release-vehicle',

  props: {
    callKey: { type: [Number, String], required: false },
    subterminalKey: { type: [Number, String], required: false }
  },

  components: {
    TowPay,
    CardSwipe
  },

  data () {
    return {
      dateOut: '',
      Name: '',
      Address1: '',
      Address2: '',
      City: '',
      State: '',
      Zip: '',
      LicenseNum: '',
      LicenseState: '',
      Phone: '',

      isTowPaySupported: false,

      askToTerminateLien: false,
      lienPromptAcknowledged: false,
      askToReleaseHolds: false,
      holdsPromptAcknowledged: false,
      askToRemoveLienPricing: false,
      shouldRemoveLienPricing: 'No',
      releaseToOwnerOnly: false,
      releasePaymentRequired: true,
      releaseType: 'owner',
      releaseTypeVisible: true,

      appliedServiceKeyPlaceholder: null,
      saleCustomerKey: null,
      salePayments: [],
      saleServices: [],
      appliedServices: [],
      addPaymentMethod: null,

      saleTaxRate: null,
      towBalance: null,
      towPayments: [],
      finalDispositionKey: null,
      finalDispositions: [
        { Key: VALUE_ID.disposition.auctioned, Value: 'Auctioned' },
        { Key: VALUE_ID.disposition.scrapped, Value: 'Scrapped' },
        { Key: VALUE_ID.disposition.sold, Value: 'Sold' }
      ],

      towPay: {},
      towPayModal: {
        isModalVisible: false,
        isComponentActive: false
      }
    };
  },

  computed: {
    confirmationsSatisfied () {
      if (this.askToTerminateLien && !this.lienPromptAcknowledged) { return false; }
      if (this.askToReleaseHolds && !this.holdsPromptAcknowledged) { return false; }

      return true;
    },

    isTowPayment () {
      return this.releaseType === 'owner';
    },

    isTowPayAvailable () {
      return this.isTowPayment && this.isTowPaySupported;
    },

    taxableBalance () {
      return this.appliedServices.reduce((total, service) => total + (service.Taxable ? service.Price : 0), 0);
    },

    taxBalance () {
      return this.taxableBalance * (this.saleTaxRate / 100);
    },

    saleSubtotal () {
      return this.appliedServices.reduce((total, service) => total + service.Price, 0);
    },

    finalBalance () {
      let balance = 0;

      if (this.isTowPayment) {
        balance = this.towBalance;
      } else {
        balance = this.saleSubtotal + this.taxBalance;
      }

      return balance - this.paymentsProxy.reduce((total, payment) => {
        return payment.isLocal ? total + payment.tcAmount : total;
      }, 0);
    },

    canReleaseVehicle () {
      if (this.paymentsProxy.some(payment => payment.isLocal && !payment.lReceivedBy)) {
        return false;
      }

      if (this.releasePaymentRequired) {
        return this.finalBalance === 0;
      }

      return true;
    },

    paymentsProxy: {
      get () {
        return this.isTowPayment ? this.towPayments : this.salePayments;
      },
      set (value) {
        if (this.isTowPayment) {
          this.towPayments = value;
        } else {
          this.salePayments = value;
        }
      }
    },

    canViewAuctionScrappedSold () {
      return Access.has('inventory.allowFinalDisposition');
    }
  },

  methods: {
    isPaymentTypeCreditCard ({ lPaymentTypeKey }) {
      return lPaymentTypeKey === VALUE_ID.paymentType.creditCard;
    },

    async addPayment () {
      switch (this.addPaymentMethod) {
        case 'towpay':
          this.addPaymentMethod = null;
          this.openTowPayModal();
          break;

        case 'manual':
        default:
          this.addPaymentMethod = null;
          let newPayment = {
            isLocal: true,
            isExpanded: true,
            tcAmount: this.finalBalance,
            lPaymentTypeKey: '',
            lCreditCardTypeKey: '',
            vc30CardholderName: '',
            vc20AuthorizationInfo: '',
            vc20PaymentInfo: '',
            lReceivedBy: ''
          };

          if (this.callKey) {
            const response = await this.getDefaultsForNewPayment(this.callKey);
            newPayment = await this.applyDefaults(newPayment, response);
          }

          this.paymentsProxy.push(newPayment);
          break;
      }
    },

    getDefaultsForNewPayment (callKey) {
      return new Promise(resolve => {
        this.$store.dispatch('TOPSCALL__getDefaultsForNewPayment', {
          callKey,
          success: response => { resolve(response); }
        });
      });
    },

    async applyDefaults (payment, defaults) {
      Object.keys(defaults).forEach(prop => {
        if (prop !== 'tcAmount') {
          payment[prop] = defaults[prop];
        }
      });

      return payment;
    },

    onTowPayPaymentAdded () {
      this.setCallInformation({ callKey: this.callKey, saleCustomerKey: this.saleCustomerKey });
    },

    openTowPayModal () {
      this.towPayModal.isModalVisible = true;
      this.towPayModal.isComponentActive = true;
    },

    closeTowPayModal () {
      this.towPayModal.isModalVisible = false;

      this.setCallInformation({ callKey: this.callKey, saleCustomerKey: this.saleCustomerKey });
    },

    getCallInformation ({ callKey, saleCustomerKey }) {
      return new Promise(resolve => {
        this.$store.dispatch('TOPSCALL__getCallInfoForReleasing', {
          callKey,
          saleCustomerKey,
          success: response => { resolve(response); }
        });
      });
    },

    async setCallInformation ({ callKey, saleCustomerKey }) {
      const localTowPayments = this.towPayments.filter(payment => payment.isLocal);
      const localSalePayments = this.salePayments.filter(payment => payment.isLocal);

      const response = await this.getCallInformation({ callKey, saleCustomerKey });

      this.towBalance = response.TowBalance;
      this.saleTaxRate = response.SaleTaxRate;
      this.releasePaymentRequired = response.ReleasePaymentRequired;
      this.askToReleaseHolds = response.AskToReleaseHolds;
      this.askToTerminateLien = response.AskToTerminateLien;
      this.askToRemoveLienPricing = response.AskToRemoveLienPricing;
      this.dateOut = response.DateOut;
      this.releaseToOwnerOnly = response.ReleaseToOwnerOnly;
      this.towPayments = [...response.TowPayments, ...localTowPayments];
      this.salePayments = [...response.SalePayments, ...localSalePayments];
      this.saleServices = response.SaleServices;
      this.towPay = response.TowPay;

      if (this.releaseToOwnerOnly) {
        this.releaseType = 'owner';
        this.releaseTypeVisible = false;
      }
    },

    addToAppliedServices () {
      const originalService = this.saleServices.find(saleService => saleService.Key === this.appliedServiceKeyPlaceholder);

      let service = { ...originalService };
      delete service.Name;

      this.appliedServices.push({ ...service, Price: 0 });

      this.appliedServiceKeyPlaceholder = null;
    },

    removeFromAppliedServices (index) {
      this.appliedServices.splice(index, 1);
    },

    removeFromPayments (index) {
      this.paymentsProxy.splice(index, 1);
    },

    processSwipe (swipe) {
      this.Name = get(swipe, 'fullName', '');
      this.Address1 = get(swipe, 'address1', '');
      this.Address2 = get(swipe, 'address2', '');
      this.City = get(swipe, 'city', '');
      this.State = get(swipe, 'state', '');
      this.Zip = get(swipe, 'zip', '');
      this.LicenseNum = get(swipe, 'licenseNumber', '');
      this.Phone = get(swipe, 'phone', '');
    },

    maskPaymentInfo (payment) {
      if (payment.vc20PaymentInfo.length < 15) return;

      let digits = payment.vc20PaymentInfo.split('');
      let lastFour = '';

      digits.reverse();

      lastFour = digits.splice(0, 4);
      lastFour.reverse();

      payment.vc20PaymentInfo = '*'.repeat(digits.length) + lastFour.join('');
    },

    async releaseVehicle () {
      const response = await new Promise(resolve => {
        this.$store.dispatch('TOPSCALL__release', {
          callKey: this.callKey,
          finalDispositionTypeKey: this.releaseType === 'owner' ? VALUE_ID.disposition.released : this.finalDispositionKey,
          dateOut: this.dateOut,
          name: this.Name,
          address: this.Address1,
          address2: this.Address2,
          city: this.City,
          state: this.State,
          zip: this.Zip,
          licenseNum: this.LicenseNum,
          licenseState: '',
          phone: this.Phone,
          removeLienPricing: this.shouldRemoveLienPricing === 'Yes',
          saleCustomerKey: this.saleCustomerKey,
          saleTaxRate: this.saleTaxRate,
          towPayments: this.towPayments.filter(payment => payment.isLocal),
          salePayments: this.salePayments.filter(payment => payment.isLocal),
          appliedServices: this.appliedServices,
          success: response => { resolve(response); }
        });
      });

      this.$emit('close', response);
    },

    acknowledgeLienPrompt () {
      this.lienPromptAcknowledged = true;

      if (this.askToRemoveLienPricing && this.shouldRemoveLienPricing === 'Yes') {
        this.setCallInformation({ callKey: this.callKey, saleCustomerKey: this.saleCustomerKey });
      }
    },

    async verifyTowPaySupport () {
      if (!this.subterminalKey) return;

      const subcompany = await this.getSubcompanyDetails(this.subterminalKey);
      const processingTypeKey = get(subcompany, 'lCreditCardProcessingTypeKey', null);

      this.isTowPaySupported = processingTypeKey === VALUE_ID.creditCardProcessingType.towpay;
    },

    getSubcompanyDetails (orgUnitKey) {
      return new Promise(resolve => {
        this.$store.dispatch('TOPSCALL__getSubcompanyDetails', {
          subterminalKey: orgUnitKey,
          success: response => resolve(response)
        });
      });
    }
  },

  async mounted () {
    this.setCallInformation({ callKey: this.callKey, saleCustomerKey: this.saleCustomerKey });
    this.verifyTowPaySupport();
  }
};
</script>
