import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ReleaseVehicle from '../ReleaseVehicle.vue';

// Mock the store
const mockStore = {
  dispatch: vi.fn(() => Promise.resolve({}))
};

// Mock the global components and utilities
const mockComponents = {
  'app-modal': { template: '<div><slot /></div>' },
  'app-button': { template: '<button><slot /></button>' },
  'app-grid-form': { template: '<div><slot /></div>' },
  'app-sale-customer': { template: '<input />' },
  'app-number': { template: '<input />' },
  'app-select': { template: '<select><slot /></select>' },
  'app-text': { template: '<input />' },
  'app-date-time': { template: '<input />' },
  'app-select-state': { template: '<select><slot /></select>' },
  'app-data-point': { template: '<div><slot /></div>' },
  'tab-group': { template: '<div><slot /></div>' },
  'tab-item': { template: '<div><slot /></div>' },
  'card-swipe': { template: '<div />' },
  'tow-pay': { template: '<div />' },
  'CardType': { template: '<span />' },
  'PaymentType': { template: '<span />' },
  'EmployeeName': { template: '<span />' },
  'InputPaymentType': { template: '<input />' },
  'InputCardType': { template: '<input />' },
  'InputEmployee': { template: '<input />' }
};

describe('ReleaseVehicle', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(ReleaseVehicle, {
      propsData: {
        callKey: 12345,
        subterminalKey: 1
      },
      mocks: {
        $store: mockStore,
        $emit: vi.fn(),
        $_: {
          get: vi.fn((obj, path, defaultValue) => {
            // Simple lodash get mock
            const keys = path.split('.');
            let result = obj;
            for (const key of keys) {
              if (result && typeof result === 'object' && key in result) {
                result = result[key];
              } else {
                return defaultValue;
              }
            }
            return result;
          })
        }
      },
      stubs: mockComponents,
      filters: {
        usd: (value) => `$${value?.toFixed(2) || '0.00'}`
      }
    });
  });

  describe('finalBalance calculation', () => {
    it('should correctly calculate balance when releasing to owner with existing tow payments', async () => {
      // Set up the component state to simulate a call with existing tow payments
      await wrapper.setData({
        releaseType: 'owner', // This makes isTowPayment true
        towBalance: 50.00, // Server returns balance that already accounts for existing payments
        towPayments: [
          // Existing payment from server (already applied to balance)
          { tcAmount: 100.00, lPaymentKey: 1, isLocal: false },
          // New local payment being added
          { tcAmount: 25.00, isLocal: true }
        ],
        salePayments: []
      });

      // The finalBalance should be: towBalance (50.00) - only local payments (25.00) = 25.00
      // NOT: towBalance (50.00) - all payments (125.00) = -75.00 (which would be the bug)
      expect(wrapper.vm.finalBalance).toBe(25.00);
    });

    it('should correctly calculate balance when releasing to owner with only existing payments', async () => {
      // Set up the component state with only existing payments (no new local payments)
      await wrapper.setData({
        releaseType: 'owner',
        towBalance: 25.00, // Server balance already accounts for existing payments
        towPayments: [
          // Only existing payments from server
          { tcAmount: 75.00, lPaymentKey: 1, isLocal: false }
        ],
        salePayments: []
      });

      // The finalBalance should be: towBalance (25.00) - no local payments (0.00) = 25.00
      expect(wrapper.vm.finalBalance).toBe(25.00);
    });

    it('should correctly calculate balance when releasing to owner with only local payments', async () => {
      // Set up the component state with only new local payments
      await wrapper.setData({
        releaseType: 'owner',
        towBalance: 100.00, // Full balance (no existing payments)
        towPayments: [
          // Only new local payments
          { tcAmount: 30.00, isLocal: true },
          { tcAmount: 20.00, isLocal: true }
        ],
        salePayments: []
      });

      // The finalBalance should be: towBalance (100.00) - local payments (50.00) = 50.00
      expect(wrapper.vm.finalBalance).toBe(50.00);
    });

    it('should correctly calculate balance for sale disposition with services and tax', async () => {
      // Set up the component state for sale disposition
      await wrapper.setData({
        releaseType: 'auctioned-etc', // This makes isTowPayment false
        appliedServices: [
          { Price: 100.00, Taxable: true },
          { Price: 50.00, Taxable: false }
        ],
        saleTaxRate: 10, // 10%
        salePayments: [
          // Existing sale payment
          { tcAmount: 80.00, lPaymentKey: 1, isLocal: false },
          // New local payment
          { tcAmount: 30.00, isLocal: true }
        ],
        towPayments: []
      });

      // Sale subtotal: 150.00
      // Tax on taxable amount (100.00): 10.00
      // Total: 160.00
      // Final balance should be: 160.00 - only local payments (30.00) = 130.00
      expect(wrapper.vm.finalBalance).toBe(130.00);
    });
  });

  describe('paymentsProxy', () => {
    it('should return towPayments when isTowPayment is true', async () => {
      await wrapper.setData({
        releaseType: 'owner',
        towPayments: [{ tcAmount: 100.00 }],
        salePayments: [{ tcAmount: 50.00 }]
      });

      expect(wrapper.vm.paymentsProxy).toEqual([{ tcAmount: 100.00 }]);
    });

    it('should return salePayments when isTowPayment is false', async () => {
      await wrapper.setData({
        releaseType: 'auctioned-etc',
        towPayments: [{ tcAmount: 100.00 }],
        salePayments: [{ tcAmount: 50.00 }]
      });

      expect(wrapper.vm.paymentsProxy).toEqual([{ tcAmount: 50.00 }]);
    });
  });
});
