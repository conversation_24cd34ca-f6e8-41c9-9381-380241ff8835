<template>
  <modal-card-footer
    id="dmv-communications"
    title="DMV Communications"
    :show="showModal"
    @close="onCloseModal">

    <template slot="body">
      <i v-if="showDataSpinner" class="fas fa-spinner fa-spin"></i>
      <div v-if="message">{{ message }}</div>

      <div class="entry" v-for="(entry, index) in displayData" :key="index" >
        <div class="request-label is-small is-quiet">Requested at</div>
        <div class="date-entry">{{ entry.dRequested }}</div>

        <div v-if="entry.ResponseMarkup" v-html="entry.ResponseMarkup"></div>
        <div v-else-if="entry.ResponseRaw" v-html="entry.ResponseRaw"></div>
        <div v-else>Awaiting response.</div>

        <hr v-if="index < displayData.length - 1">
      </div>

      <div v-if="!displayData.length && !showDataSpinner">
        No DMV Data has been requested.</div>

      <div class="print-backdrop"></div>
    </template>

    <template slot="footer">
      <!-- left -->
      <div class="left-container">
        <i v-if="showRequestSpinner" class="fas fa-spinner fa-spin"></i>
        <div v-else-if="requestMessage">{{ requestMessage }}</div>
        <div v-else-if="!hideForm" class="action-container">
          <select-control
              v-model="selectedState"
              :options="formattedStates">
          </select-control>
          <button @click="onRequestNewDMVRecord" class="button" :disabled="!selectedState">Request New DMV Record</button>
        </div>
      </div>
      <!-- right -->
      <div class="right-container">
        <app-button @click="printData" class="toggle-btn">
          <i class="far fa-print"></i>
        </app-button>
        <app-button @click="refreshData" class="toggle-btn">
          <i class="far fa-refresh"></i>
        </app-button>
        <app-button @click="toggleData" class="toggle-btn">
          <i class="far fa-eye"></i>
        </app-button>
      </div>
    </template>

  </modal-card-footer>
</template>

<script>import { get } from 'lodash-es';


import {mapActions} from 'vuex';
import ModalCardFooter from './ModalCardFooter.vue';
import SelectControl from '../inputs/Select.vue';

export default {
  components: {
    ModalCardFooter,
    SelectControl
  },

  props: {
    callKey: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      isFormatted: true,
      message: null,
      showDataSpinner: false,
      showRequestSpinner: false,
      defaultStateKey: null,
      states: [],
      selectedState: null,
      requestMessage: null,
      hideForm: false,
      showModal: true,
      unformatedData: [],
      formattedData: [],
      displayData: []
    };
  },

  mounted () {
    this.refreshData();
  },

  computed: {
    formattedStates () {
      // Transform states to the format expected by the select-control
      return this.states.map(state => ({
        value: state,
        description: state
      }));
    }
  },

  methods: {
    ...mapActions([
      'TOPSCALL__getDMVCommunications',
      'TOPSCALL__getDMVStates',
      'TOPSCALL__requestDMV'
    ]),

    toggleData () {
      this.isFormatted = !this.isFormatted; // toggle the state
      this.onChooseDisplayData();
    },

    onDataReady () {
      this.formatData();
      this.onChooseDisplayData();
    },

    onChooseDisplayData () {
      if (this.isFormatted) {
        this.displayData = this.formattedData;
      } else {
        this.displayData = this.unformatedData;
      }
    },

    onCloseModal () {
      this.$emit('close');
      this.showModal = false;
    },

    formatData () {
      this.formattedData = this.formattedData.map(itemObj => {
        let item = itemObj.ResponseMarkup; // Extracting the ResponseMarkup for formatting

        // Normalize spaces on each line
        item = item.split('\n').map(line => line.replace(/&nbsp;/g, ' ').replace(/\s+/g, ' ')).join('\n');

        // Remove multiple consecutive newlines
        item = item.replace(/\n{2,}/g, '\n');

        // Remove any empty lines
        item = item.replace(/(<br\s*\/?>\s*){2,}/gi, '<br>');

        // Remove empty [: :] tags
        item = item.replace(/\[:\s*:\]/g, '');

        // Create regex to find text that starts with [: and ends with :] and replace it with a span tag color red
        const regex = /\[:([\s\S]*?):\]/g; // The [\s\S] part is used to match any character, including newline
        item = item.replace(regex, '<span style="color: red;">$1</span>');

        return {
          ...itemObj, // Keeping the other properties of itemObj intact
          ResponseMarkup: item // Overwriting the ResponseMarkup with the formatted version
        };
      });
    },

    refreshData () {
      this.onGetDMVComm();
      this.onGetDMVStates();
    },

    printData () {
      window.print();
    },

    onGetDMVComm () {
      this.showDataSpinner = true;
      this.TOPSCALL__getDMVCommunications({
        callKey: this.callKey,
        success: response => {
          this.showDataSpinner = false;
          if (response.length <= 0) {
            this.message = 'No DMV data found.';
            return;
          }

          this.formattedData = [];
          this.unformatedData = [];

          response.Requests.forEach(item => {
            this.formattedData.push({
              dRequested: item.dRequested || 'No Date Provided',
              ResponseMarkup: get(item, 'ResponseMarkup', '')
            });

            this.unformatedData.push({
              dRequested: item.dRequested || 'No Date Provided',
              ResponseRaw: get(item, 'ResponseRaw', '')
            });
          });

          this.onDataReady();
        },
        fail: error => {
          console.log('Failed to retrieve DMV data.' + error);
          this.showDataSpinner = false;
          this.message = error.message;
        }
      });
    },

    onGetDMVStates () {
      this.showRequestSpinner = true;
      this.TOPSCALL__getDMVStates({
        callKey: this.callKey,
        success: response => {
          this.showRequestSpinner = false;
          this.defaultStateKey = response.DefaultStateKey;
          // Extract two-letter state keys from the response and assign to states
          this.states = response.States.map(state => {
            return Object.keys(state)[0];
          });
        },
        fail: error => {
          console.log('Failed to retrieve DMV States.' + error);
          this.showRequestSpinner = false;
          this.message = error.message;
        }
      });
    },

    onRequestNewDMVRecord () {
      this.showRequestSpinner = true;

      this.TOPSCALL__requestDMV({
        CallKey: this.callKey,
        State: this.selectedState,

        success: response => {
          this.showRequestSpinner = false;
          this.requestMessage = 'Your request has been submitted.';  // Show success message
          this.hideForm = true;            // Hide form
          this.selectedState = null;
          setTimeout(() => {
            this.requestMessage = null; // Hide success message
            this.hideForm = false;           // Show form after 3 seconds
            this.refreshData();              // Refresh data
          }, 3000);
        },
        fail: error => {
          console.log('Failed to retrieve DMV data.' + error);
          this.showRequestSpinner = false;
          this.message = error.message;
        }
      });
    }
  }
};
</script>

<style scoped>
.print-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;

  background-color: white;
  z-index: 1000;
  display: none;

  @media print {
    display: block;
  }
}

.entry {
  position: relative;

  z-index: 1001;
}

.date-entry {
  color: #4a4a4a;
  font-weight: bold;
  margin-bottom: 10px;
}

/* Main container styling */
[slot="footer"] {
  display: flex;
  justify-content: space-between;
}

/* Left container styling */
.left-container {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Right container styling */
.right-container {
  flex: 1;                /* This ensures it takes the available space to push content to the right */
  display: flex;
  align-items: center;
  justify-content: flex-end; /* This pushes its children (the button) to the right */
}


/* Additional padding for the toggleData button */
.toggle-btn {
  padding: 10px 15px;
}

.action-container {
  display: flex;         /* Convert the container into a flex container */
  align-items: center;   /* Align its children vertically in the center */
  gap: 10px;             /* Optional: Add some space between the select and the button */
}
</style>
