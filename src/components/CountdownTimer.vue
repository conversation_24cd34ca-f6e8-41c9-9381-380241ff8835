<template>
  <span class="countdown-timer number-tabular" :class="{ 'urgent': isUrgent }">{{ formattedTime }}</span>
</template>

<script>
export default {
  name: 'countdown-timer',

  props: {
    endTime: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      now: new Date(),
      timerId: null
    };
  },

  computed: {
    timeRemaining () {
      const end = new Date(this.endTime).getTime();
      return Math.max(end - this.now.getTime(), 0);
    },

    isUrgent () {
      return this.timeRemaining > 0 && this.timeRemaining < 60 * 1000; // Less than 1 minute
    },

    formattedTime () {
      const diff = this.timeRemaining;

      if (diff === 0) {
        return 'Expired';
      }

      const hours = Math.floor(diff / 1000 / 60 / 60);
      const minutes = Math.floor((diff / 1000 / 60) % 60);
      const seconds = Math.floor((diff / 1000) % 60);

      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`;
      } else {
        return `${seconds}s`;
      }
    }
  },

  methods: {
    updateTime () {
      this.now = new Date();
    },

    startTimer () {
      if (this.timerId) return;
      // Update every second for countdown
      this.timerId = setInterval(() => {
        this.updateTime();
      }, 1000);
      // Initial update
      this.updateTime();
    },

    stopTimer () {
      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }
    }
  },

  mounted () {
    this.startTimer();
  },

  beforeDestroy () {
    this.stopTimer();
  }
};
</script>

<style scoped>
.countdown-timer {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.countdown-timer.urgent {
  color: var(--pure-red);
}
</style>
