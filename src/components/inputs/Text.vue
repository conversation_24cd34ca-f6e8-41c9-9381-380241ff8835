<template>
<p class="control">
  <label>
    <div class="-label"><slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span></div>
    <input
      type="text"
      :id="id"
      :class="controlClasses"
      ref="control"
      v-model.trim="valueProxy"
      :placeholder="placeholder"
      :maxlength="maxlengthProxy"
      :disabled="disabled"
      :required="required"
      :readonly="readonly"
      :tabindex="tabindex"
      :autocomplete="autocomplete"
      @change="emit('change')"
      @keyup="emit('keyup')"
      @focus="emit('focus')"
      @blur="emit('blur')"
      @click="emit('click')"
      @paste.prevent="handlePaste"
      @keyup.ctrl.alt.e.prevent="setValue('Empty')"
      @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
  </label>
</p>
</template>

<script>
import Control from '@/components/ancestors/Control.vue';
import { newLineToCsv } from '@/utils/string.js';

export default {
  name: 'text-control',

  extends: Control,

  computed: {
    maxlengthProxy () {
      return this.maxlength || false;
    }
  },

  methods: {
    trimTrailingCommas(text) {
      return text.replace(/,+\s*$/, '');
    },

    handlePaste (event) {
      let value = event.clipboardData.getData('text');

      if (!value) return;
      if (this.maxlengthProxy && value.length > this.maxlengthProxy) {
        value = value.slice(0, this.maxlengthProxy);
      }

      const currentValue = this.valueProxy || '';
      let newValue = currentValue + newLineToCsv(value);

      newValue = this.trimTrailingCommas(newValue);

      this.valueProxy = newValue;

      this.$nextTick(() => {
        this.$hub.$emit('change', this.$refs.control);
      });
    }
  }
};
</script>
