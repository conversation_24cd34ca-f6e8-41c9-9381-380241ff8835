<template>
  <div>
    <input v-model="valueProxy" type="text" class="input" autocomplete="off" :disabled="disabled"
      :placeholder="placeholder" :list="uuid" @change="emit('change')" @focus="emit('focus')" @blur="emit('blur')"
      @click="emit('click')">

    <datalist :id="uuid">
      <option v-for="(suggestion, index) in suggestions" :value="suggestion" :key="index"></option>
    </datalist>
  </div>
</template>

<script>
import { get, uniqueId } from 'lodash-es';
import Control from '@/components/ancestors/Control.vue';
import { EVENT_ERROR } from '@/config.js';
import { levenshteinDistance } from '@/utils/string.js';

export default {
  name: 'address-selector-control',

  extends: Control,

  props: {
    subterminalKey: {
      required: false,
      default: null
    }
  },

  data() {
    return {
      uuid: uniqueId('suggestions_'),
      suggestions: [],
      subterminal: {},

      spacialAnchor: {
        latitude: null,
        longitude: null
      }
    };
  },

  computed: {
    cityState() {
      if (this.$store.state.topsCompany.settings.vc50MappingCity && this.$store.state.topsCompany.settings.ch2MappingState) {
        return this.$store.state.topsCompany.settings.vc50MappingCity + ', ' + this.$store.state.topsCompany.settings.ch2MappingState;
      }

      if (this.subterminal.vc30City && this.subterminal.ch2StateKey) {
        return this.subterminal.vc30City + ', ' + this.subterminal.ch2StateKey;
      }

      return '';
    }
  },

  watch: {
    async valueProxy() {
      this.suggestions = await this.getSuggestions();
    },

    cityState: {
      immediate: true,
      handler() {
        this.setSpacialAnchor();
      }
    }
  },

  methods: {
    getSubterminal() {
      return new Promise(resolve => {
        if (this.subterminalKey) {
          this.$store.dispatch('TOPSCALL__getSubcompanyDetails', {
            subterminalKey: this.subterminalKey,
            success: response => { resolve(response); }
          });
        }
      });
    },

    async setSpacialAnchor() {
      if (!this.cityState) { return; }

      const coordinates = await this.getCoordinates({ location: this.cityState });

      this.spacialAnchor = {
        latitude: coordinates.latitude,
        longitude: coordinates.longitude
      };
    },

    getCoordinates({ location, verbose = false }) {
      return new Promise(resolve => {
        if (location) {
          this.$store.dispatch('MAP__getCoordinates', {
            location: location,
            callback: response => {
              const approximate = get(response, 'Approximation', false);
              const latitude = get(response, 'Latitude', '');
              const longitude = get(response, 'Longitude', '');

              if (approximate && verbose) {
                this.$hub.$emit(EVENT_ERROR, 'Unable to pinpoint this location.');
              }

              resolve({
                latitude,
                longitude,
                approximate
              });
            }
          });
        }
      });
    },

    async getSuggestions() {
      try {
        if (this.valueProxy.length <= 3) { throw new Error('Search value is too short.'); }
        if (!this.spacialAnchor.latitude) { throw new Error('Latitude is required.'); }
        if (!this.spacialAnchor.longitude) { throw new Error('Longitude is required.'); }

        const params = new window.URLSearchParams({
          q: this.valueProxy,
          at: this.spacialAnchor.latitude + ',' + this.spacialAnchor.longitude,
          in: 'countryCode:USA,CAN',
          limit: 5,
          apiKey: import.meta.env.VITE_HERE_API_KEY
        });

        const response = await window.fetch(import.meta.env.VITE_HERE_API_URL + '?' + params.toString());
        const data = await response.json();

        return data.items.map(item => {
          const title = item.title || '';
          const houseNumber = item.address.houseNumber || '';
          const street = item.address.street || '';
          const city = item.address.city || '';
          const state = item.address.state || '';
          const citySeparator = city && state ? ',' : '';
          const postalCode = item.address.postalCode || '';

          const compositeAddress = `${houseNumber} ${street} ${city}${citySeparator} ${state} ${postalCode}`.trim();

          // For vanilla addresses, we only want to show the address.
          // For POIs, we want to show the title and the address.
          const titleMatchPercentage = levenshteinDistance(title, compositeAddress);

          return (titleMatchPercentage >= 40) ? title : `${title}, ${compositeAddress}`;
        });
      } catch (error) {
        return [];
      }
    }
  },

  async mounted() {
    this.subterminal = await this.getSubterminal();
  }
};
</script>
