<template>
  <p class="control">
    <label>
      <div class="-label">
        <slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
      </div>
      <input type="text" :id="id" :class="controlClasses" ref="control" v-model.trim="valueProxy"
        :placeholder="placeholder" :maxlength="maxlengthProxy" :disabled="disabled" :required="required"
        :readonly="readonly" :tabindex="tabindex" :autocomplete="autocomplete" :list="listId" @change="emit('change')"
        @keyup="emit('keyup')" @focus="emit('focus')" @blur="emit('blur')" @click="emit('click')"
        @keyup.ctrl.alt.e.prevent="setValue('Empty')" @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
      <datalist :id="listId">
        <option v-for="(option, index) in options" :value="option" :key="index">
          {{ option }}
        </option>
      </datalist>
    </label>
  </p>
</template>

<script>
import { uniqueId } from 'lodash-es';
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'suggestion-control',

  extends: Control,

  props: {
    list: { default: null },
    options: { default: [] }
  },

  data() {
    return {
      listId: uniqueId('list-id-')
    };
  },

  computed: {
    maxlengthProxy() {
      return this.maxlength || false;
    }
  }
};
</script>
