<template>
  <p class="control" ref="datetimepicker">
    <date-range-picker ref="picker" v-model="dateRange" :singleDatePicker="!isRange" :opens="opens" :ranges="false"
      :autoApply="true" :closeOnEsc="true" :appendToBody="true" :disabled="disabled" @toggle="togglePicker($event)"
      @update="updateValue">

      <template slot="input">
        <label class="app-datetime">
          <div class="-label">
            <slot></slot>
            <span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
          </div>
          <div class="date-time-control">
            <input type="text" data-role="date-picker" v-model.trim="valueProxy" :id="id" :class="controlClasses"
              :placeholder="placeholder" :disabled="disabled" :required="required" :readonly="readonly"
              :tabindex="tabindex" ref="control" autocomplete="off" @focus="emit('focus')" @blur="emit('blur')"
              @click="emit('click')" @keyup.ctrl.alt.e.prevent="setValue('Empty')"
              @keyup.ctrl.alt.n.prevent="setValue('Not Empty')">
            <a :class="iconClasses" @click.prevent.stop="getNow" tabindex="-1">
              <i class="far fa-clock"></i>
            </a>
          </div>
        </label>
      </template>

    </date-range-picker>
  </p>
</template>

<script>
import { mapActions } from 'vuex';
import { format } from 'date-fns';
import { set } from 'lodash-es';
import Control from '@/components/ancestors/Control.vue';
import DateRangePicker from 'vue2-daterange-picker';
import 'vue2-daterange-picker/dist/vue2-daterange-picker.css';

export default {
  name: 'date-time-control',

  extends: Control,

  components: {
    DateRangePicker
  },

  props: {
    formatter: { default: true },
    prepopulate: { default: false },
    value: { type: [Number, String], required: false },
    isRange: { type: Boolean, required: false, default: false },
    nowTimestamp: { type: Boolean, required: false, default: true }
  },

  data() {
    return {
      opens: 'right', // Default to right alignment
      isPositionCalculated: false,
      dateRange: {
        startDate: null,
        endDate: null
      }
    };
  },

  computed: {
    iconClasses() {
      let classes = {};

      set(classes, 'button', true);
      set(classes, 'is-transparent', true);
      set(classes, this.sizeClasses[this.size], true);

      return classes;
    },

    ranges() {
      if (!this.isRange) return false;

      let today = new Date();
      today.setHours(0, 0, 0, 0);

      let yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      return {
        'Today': [today, today],
        'Yesterday': [yesterday, yesterday],
        'This month': [new Date(today.getFullYear(), today.getMonth(), 1), new Date(today.getFullYear(), today.getMonth() + 1, 0)],
        'Last month': [new Date(today.getFullYear(), today.getMonth() - 1, 1), new Date(today.getFullYear(), today.getMonth(), 0)],
        'This year': [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear(), 11, 31)]
      };
    }
  },

  watch: {
    valueProxy() {
      this.emit('change');
    }
  },

  methods: {
    ...mapActions(['__getNow']),

    getNow() {
      if (this.disabled) return;
      if (this.valueProxy) return;

      this.__getNow({
        callback: response => {
          if (this.formatter) {
            this.valueProxy = this.formatValue(response.Now);
          } else {
            this.valueProxy = response.Now;
          }

          this.$nextTick(() => {
            this.$hub.$emit('change', this.$refs.control);
          });
        }
      });
    },

    formatValue(value) {
      return this.nowTimestamp
        ? format(value, 'MM/DD/YYYY HH:mm:ss')
        : format(value, 'MM/DD/YYYY');
    },

    updateValue({ startDate, endDate }) {
      let preservedDateSegments = null;
      let preservedDate = null;
      let preservedTime = null;

      if (this.valueProxy) {
        preservedDateSegments = this.valueProxy.split(' ');
        preservedDate = preservedDateSegments.shift();
        preservedTime = preservedDateSegments.join(' ');
      }

      let formattedStartDate = format(startDate, 'MM/DD/YYYY');
      let formattedEndDate = format(endDate, 'MM/DD/YYYY');

      this.valueProxy = formattedStartDate === formattedEndDate
        ? [formattedStartDate, preservedTime].join(' ').trim()
        : `${formattedStartDate}-${formattedEndDate}`;
    },

    togglePicker(isVisible) {
      if (!isVisible) return;
      if (this.isPositionCalculated) return;

      this.$nextTick(() => {
        if (!this.$refs.datetimepicker) return;

        let $element = this.$refs.datetimepicker.querySelector('.daterangepicker');

        if (!$element) return;

        try {
          let $elementBox = $element.getBoundingClientRect();
          let rightBorder = $elementBox.x + $elementBox.width;

          this.opens = rightBorder > window.innerWidth ? 'left' : 'right';

          this.isPositionCalculated = true;
        } catch (error) {
          console.warn('Error positioning date picker:', error);
        }
      });
    }
  },

  mounted() {
    if (this.prepopulate) {
      this.getNow();
    }
  }
};
</script>
