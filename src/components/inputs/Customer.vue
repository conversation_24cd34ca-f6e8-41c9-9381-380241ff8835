<template>
  <span class="customer__search-input">
    <app-text ref="input" v-model="sketch" @focus.once="setSearchVisibility(!sketch)" @click="setSearchVisibility(true)"
      @keyup.enter="setSearchVisibility(true)" :required="required" :disabled="disabled" autocomplete="off">
      <slot></slot>
    </app-text>

    <button v-if="sketch && !disabled" class="customer__clear-button" type="button" title="Clear customer selection"
      @click="clearSelection">
      <i class="far fa-xmark"></i>
    </button>

    <dialog ref="searchDialog" @click.capture="onClickOutside">
      <div class="customer__search-container">
        <app-text class="search-input" v-model="searchInput" ref="searchInput">
          Search for customer
        </app-text>

        <ul class="customers">
          <li class="customer" :disabled="!customerIsSelectable(customer)" v-for="(customer, index) in customersProxy"
            :key="index" @click="setCustomer(customer)">

            <div>
              <div><strong>{{ customer.Name || '--' }}</strong></div>
              <div class="small">{{ customer.Address || '--' }}<br>{{ customer.City || '--' }}, {{ customer.State ||
                '--' }}</div>
            </div>
            <div class="small">
              <div>Shortcode: {{ customer.ShortCode || '--' }}</div>
              <div>Status: {{ customer.Status || '--' }}<br></div>
              <div>Type: {{ customer.Type || '--' }}</div>
            </div>

          </li>
        </ul>

        <footer class="_footer small">
          <div><i class="far fa-lightbulb"></i> Enter name, phone, address, caller, or&nbsp;shortcode</div>

          <label class="_ppi-control" v-if="showPpiControl">
            <input type="checkbox" v-model="ppiOnly">
            <div>PPI Only</div>
          </label>
        </footer>
      </div>
    </dialog>
  </span>
</template>

<script>
import { mapActions } from 'vuex';
import { throttle, filter, reject, flatten, uniqBy } from 'lodash-es';
import ModalPlain from '../features/ModalPlain.vue';
import { EVENT_SET_CALL_PRICING_GUARD_ACTIVE } from '@/config.js';

export default {
  name: 'customer-control',

  components: { ModalPlain },

  props: {
    value: { type: [Number, String], default: null },
    disabled: { type: Boolean, default: false },
    required: { type: Boolean, default: false },
    emptyOption: { default: true },
    showPpiControl: { default: false }
  },

  data() {
    return {
      sketch: '',
      customer: {},
      customers: [],
      ppiOnly: this.initialPpiOnly,
      searchInput: '',
      oldValue: null
    };
  },

  computed: {
    valueProxy: {
      get() {
        return this.value;
      },
      set(newValue, oldValue) {
        this.oldValue = oldValue || newValue;
        this.$emit('input', newValue);
      }
    },

    customersProxy() {
      if (this.ppiOnly) {
        return filter(this.customers, ['Type', 'PPI']);
      }

      return reject(this.customers, ['Type', 'PPI']);
    }
  },

  watch: {
    searchInput() {
      if (this.searchInput.length < 3) {
        this.customers = [];
        return;
      }

      this.findCustomers();
    },

    valueProxy: {
      immediate: true,
      handler() {
        this.setSketch();
      }
    }
  },

  methods: {
    ...mapActions([
      'CUSTOMER__readBasic',
      'TOPSCALL__findCustomersForNew'
    ]),

    onClickOutside(event) {
      const dialog = this.$refs.searchDialog;
      const rect = dialog.getBoundingClientRect();

      if (
        event.clientX < rect.left ||
        event.clientX > rect.right ||
        event.clientY < rect.top ||
        event.clientY > rect.bottom
      ) {
        this.setSearchVisibility(false);
      }
    },

    async setSearchVisibility(toVisible) {
      if (toVisible) {
        this.$refs.searchDialog.showModal();

        this.$hub.$emit(EVENT_SET_CALL_PRICING_GUARD_ACTIVE, true);

        await this.$awaitNextTicks(2);

        document.querySelector('.search-input input').focus();
      } else {
        this.$refs.searchDialog.close();
      }
    },

    findCustomers: throttle(function () {
      Promise.all([
        this.searchCustomerFragment('Name'),
        this.searchCustomerFragment('Phone'),
        this.searchCustomerFragment('Caller'),
        this.searchCustomerFragment('Address'),
        this.searchCustomerFragment('ShortCode')
      ]).then(response => {
        response = flatten(response);
        response = reject(response, item => item === undefined);
        response = uniqBy(response, 'Key');

        this.customers = response;

        if (this.customersProxy.length === 1) {
          this.setCustomer(this.customersProxy[0]);
        }
      });
    }, 500),

    async searchCustomerFragment(fragment) {
      if (fragment === 'Phone' && !this.isNumeric(this.searchInput)) return;

      return new Promise(resolve => {
        this.TOPSCALL__findCustomersForNew({
          data: { [fragment]: this.searchInput },
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    isNumeric(value) {
      let isNumeric = /^[-+]?(\d+|\d+\.\d*|\d*\.\d+)$/;

      return isNumeric.test(value);
    },

    setCustomer(customer) {
      if (!this.customerIsSelectable(customer)) return;

      this.valueProxy = customer.Key;
      this.setSearchVisibility(false);

      this.$emit('change', customer);
      this.$hub.$emit(EVENT_SET_CALL_PRICING_GUARD_ACTIVE, false);
    },

    customerIsSelectable(customer) {
      return !['Denied', 'Cancelled'].includes(customer.Status);
    },

    async setSketch(customer = null) {
      if (!this.valueProxy) {
        this.sketch = '';
        return;
      }

      if (!customer) {
        customer = await this.getCustomerProfile(this.valueProxy);
      }

      this.customer = customer;
      this.sketch = customer.Name;

      if (this.showType) {
        this.sketch += ` (${customer.Type})`;
      }
    },

    getCustomerProfile(key) {
      return new Promise(resolve => {
        this.CUSTOMER__readBasic({
          customerKey: key,
          callback: response => {
            resolve(response);
          }
        });
      });
    },

    async getInternalState() {
      return {
        oldValue: this.oldValue,
        newValue: this.valueProxy
      };
    },

    clearSelection() {
      if (!this.sketch || this.disabled) return;

      this.valueProxy = null;
      this.sketch = '';
      this.customer = {};
      this.$emit('change', null);
    }
  }
};
</script>

<style scoped>
.customer__search-input {
  position: relative;
  display: inline-block;
  width: 100%;
}

.customer__clear-button {
  position: absolute;
  right: 0rlh;
  bottom: 0.25rlh;

  background: none;
  aspect-ratio: 1 / 1;
  border: none;
  color: var(--pure-gray);
  cursor: pointer;
  z-index: 1;
}

.customer__clear-button:hover {
  color: inherit;
}
</style>
