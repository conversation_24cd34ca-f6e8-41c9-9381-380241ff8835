import store from '@/store';

/**
 * Composable for working with tow types
 * @param {Object} options - Configuration options
 * @param {Boolean} options.loadOnMount - Whether to load tow types on mount
 * @returns {Object} - Tow type related methods and properties
 */
export default function useTowType (options = { loadOnMount: true }) {
  /**
   * Load tow types from the API
   * @returns {Promise} - Promise that resolves when tow types are loaded
   */
  const loadTowTypes = async () => {
    if (store.getters.TOWTYPE__isLoaded || store.getters.TOWTYPE__isLoading) {
      return store.getters.TOWTYPE__allOptions;
    }

    return new Promise(resolve => {
      store.dispatch('TOWTYPE__getAll', {
        callback: response => {
          resolve(response);
        }
      });
    });
  };

  /**
   * Check if a tow type is a dropoff type
   * @param {Number|String} towTypeKey - The tow type key to check
   * @returns {Boolean} - Whether the tow type is a dropoff type
   */
  const isDropoffTowType = (towTypeKey) => {
    return store.getters.TOWTYPE__isDropoff(towTypeKey);
  };

  if (options.loadOnMount) {
    loadTowTypes();
  }

  return {
    towTypes: () => store.getters.TOWTYPE__activeOptions,
    allTowTypes: () => store.getters.TOWTYPE__allOptions,
    isLoading: () => store.getters.TOWTYPE__isLoading,
    isLoaded: () => store.getters.TOWTYPE__isLoaded,
    loadTowTypes,
    isDropoffTowType
  };
}
