import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import useTowType from '../useTowType';

// Mock the store
vi.mock('@/store', () => ({
  default: {
    getters: {
      TOWTYPE__isLoaded: false,
      TOWTYPE__isLoading: false,
      TOWTYPE__activeOptions: [],
      TOWTYPE__allOptions: [],
      TOWTYPE__isDropoff: vi.fn()
    },
    dispatch: vi.fn()
  }
}));

// Import the mocked store to manipulate it in tests
import store from '@/store';

describe('useTowType', () => {
  // Reset mocks before each test
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset store getters to default values
    store.getters.TOWTYPE__isLoaded = false;
    store.getters.TOWTYPE__isLoading = false;
    store.getters.TOWTYPE__activeOptions = [];
    store.getters.TOWTYPE__allOptions = [];
    store.getters.TOWTYPE__isDropoff.mockReturnValue(false);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should load tow types on mount by default', () => {
    useTowType();
    expect(store.dispatch).toHaveBeenCalledWith('TOWTYPE__getAll', expect.any(Object));
  });

  it('should not load tow types on mount when loadOnMount is false', () => {
    useTowType({ loadOnMount: false });
    expect(store.dispatch).not.toHaveBeenCalled();
  });

  it('should return cached tow types when already loaded', async () => {
    // Set up store to indicate data is already loaded
    store.getters.TOWTYPE__isLoaded = true;
    store.getters.TOWTYPE__allOptions = [{ Key: 1, Value: 'Test' }];
    
    const { loadTowTypes } = useTowType({ loadOnMount: false });
    const result = await loadTowTypes();
    
    expect(result).toEqual([{ Key: 1, Value: 'Test' }]);
    expect(store.dispatch).not.toHaveBeenCalled();
  });

  it('should return cached tow types when loading is in progress', async () => {
    // Set up store to indicate data is being loaded
    store.getters.TOWTYPE__isLoading = true;
    store.getters.TOWTYPE__allOptions = [{ Key: 1, Value: 'Test' }];
    
    const { loadTowTypes } = useTowType({ loadOnMount: false });
    const result = await loadTowTypes();
    
    expect(result).toEqual([{ Key: 1, Value: 'Test' }]);
    expect(store.dispatch).not.toHaveBeenCalled();
  });

  it('should load tow types from API when not loaded or loading', async () => {
    // Mock the dispatch to simulate API response
    const mockResponse = [{ Key: 1, Value: 'Test' }];
    store.dispatch.mockImplementation((action, { callback }) => {
      callback(mockResponse);
      return Promise.resolve();
    });
    
    const { loadTowTypes } = useTowType({ loadOnMount: false });
    const result = await loadTowTypes();
    
    expect(result).toEqual(mockResponse);
    expect(store.dispatch).toHaveBeenCalledWith('TOWTYPE__getAll', expect.any(Object));
  });

  it('should check if a tow type is a dropoff type', () => {
    // Set up the mock to return true for a specific key
    store.getters.TOWTYPE__isDropoff.mockImplementation((key) => key === 5);
    
    const { isDropoffTowType } = useTowType({ loadOnMount: false });
    
    expect(isDropoffTowType(5)).toBe(true);
    expect(isDropoffTowType(1)).toBe(false);
    expect(store.getters.TOWTYPE__isDropoff).toHaveBeenCalledWith(5);
    expect(store.getters.TOWTYPE__isDropoff).toHaveBeenCalledWith(1);
  });

  it('should return active tow types', () => {
    const activeTowTypes = [
      { Key: 1, Value: 'Active1', Active: true },
      { Key: 2, Value: 'Active2', Active: true }
    ];
    store.getters.TOWTYPE__activeOptions = activeTowTypes;
    
    const { towTypes } = useTowType({ loadOnMount: false });
    
    expect(towTypes()).toEqual(activeTowTypes);
  });

  it('should return all tow types', () => {
    const allTowTypes = [
      { Key: 1, Value: 'Active1', Active: true },
      { Key: 2, Value: 'Active2', Active: true },
      { Key: 3, Value: 'Inactive', Active: false }
    ];
    store.getters.TOWTYPE__allOptions = allTowTypes;
    
    const { allTowTypes: getAllTowTypes } = useTowType({ loadOnMount: false });
    
    expect(getAllTowTypes()).toEqual(allTowTypes);
  });

  it('should return loading state', () => {
    store.getters.TOWTYPE__isLoading = true;
    
    const { isLoading } = useTowType({ loadOnMount: false });
    
    expect(isLoading()).toBe(true);
  });

  it('should return loaded state', () => {
    store.getters.TOWTYPE__isLoaded = true;
    
    const { isLoaded } = useTowType({ loadOnMount: false });
    
    expect(isLoaded()).toBe(true);
  });
});
