<template>
  <p class="control">
    <label class="app-select">
      <div class="-label">
        <slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
      </div>
      <span :class="controlClasses">
        <select :id="id" v-model="valueProxy" @change="emit('change')" @focus="emit('focus')" @blur="emit('blur')"
          @click="emit('click')" :readonly="readonly" :required="required" :disabled="disabled" :tabindex="tabindex"
          :autofocus="autofocus" autocomplete="off">
          <option v-if="emptyOption" value=""></option>
          <option v-if="placeholder" value="">{{ placeholder }}</option>
          <option v-for="(option, index) in optionsProxy" :value="option[keyAlias]" :key="index"
            :disabled="!isOptionEnabled(option)">
            {{ option[valueAlias] }}
          </option>
        </select>
      </span>
    </label>
  </p>
</template>

<script>
import { clone, has, find, toString, isEmpty, filter, get } from 'lodash-es';
import Control from '@/components/ancestors/Control.vue';

export default {
  name: 'select-control',

  extends: Control,

  props: {
    options: { type: Array, required: true },
    optionEnabledAlias: { type: String, required: false, default: 'isEnabled' },
    emptyOption: { type: Boolean, required: false, default: true },
    keyAlias: { type: [String, Number], required: false, default: 'value' },
    activeAlias: { type: [String, Number], required: false, default: 'Active' },
    valueAlias: { type: [String, Number], required: false, default: 'description' }
  },

  data() {
    return {
      controlType: 'select'
    };
  },

  computed: {
    optionsProxy() {
      let localOptions = clone(this.options);

      if (!has(localOptions[0], this.activeAlias)) return localOptions;

      // Temporarily reverse deactivated option for display
      let targetOption = find(localOptions, [this.keyAlias, toString(this.valueProxy)]);

      if (!isEmpty(targetOption)) targetOption[this.activeAlias] = true;

      return filter(localOptions, [this.activeAlias, true]);
    }
  },

  methods: {
    isOptionEnabled(option) {
      return get(option, this.optionEnabledAlias, true);
    }
  }
};
</script>
