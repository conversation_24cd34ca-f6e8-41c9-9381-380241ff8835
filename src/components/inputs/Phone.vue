<template>
  <p class="control">
    <label>
      <div class="-label">
        <slot></slot><span v-if="required" class="-required"><i class="fas fa-circle-small"></i></span>
      </div>
      <input type="phone" :id="id" :class="controlClasses" ref="control" v-model.trim="valueProxy"
        :placeholder="placeholder" :maxlength="maxlengthProxy" :disabled="disabled" :required="required"
        :readonly="readonly" :tabindex="tabindex" autocomplete="off" @keyup="emit('keyup')" @keypress="emit('keypress')"
        @focus="emit('focus')" @blur="emit('blur')" @click="emit('click')" @change="emit('change')">
    </label>
  </p>
</template>

<script>
import is from 'is_js';
import { split, filter, join } from 'lodash-es';
import Text from './Text.vue';

export default {
  name: 'phone-control',

  extends: Text,

  methods: {
    formatValue(value) {
      let characters = split(value, '');
      let cleanCharacters = filter(characters, character => is.alphaNumeric(character));
      let cleanValue = join(cleanCharacters, '');
      let formattedValue = cleanValue.replace(/(\d{3})(\d{3})(\d{4})([a-z0-9])?/, '$1-$2-$3 $4');

      return formattedValue;
    }
  }
};
</script>
