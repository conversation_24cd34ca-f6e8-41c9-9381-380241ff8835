<template>
  <div id="record-view" data-page="profile">
    <app-titlebar title="User Profile"></app-titlebar>

    <div class="content-section">
      <section class="form">
        <div class="columns is-multiline">
          <div class="column is-4">
            <app-data-point label="Name">
              {{ USER__state.Name }}
            </app-data-point>
          </div>
          <div class="column is-4">
            <app-data-point label="Username">
              {{ USER__state.UserID }}
            </app-data-point>
          </div>
          <div class="column is-4">
            <app-data-point label="User Key">
              {{ USER__state.Key }}
            </app-data-point>
          </div>
          <div class="column is-4">
            <app-data-point label="Organization">
              {{ organizationName }}
            </app-data-point>
          </div>
          <div class="column is-4">
            <app-data-point label="Organization Key">
              {{ __state.orgUnitKey }}
            </app-data-point>
          </div>

          <div class="column is-12">
            <hr>
          </div>

          <div class="column is-6">
            <label>Application Mode</label>
            <span class="select">
              <select v-model="appMode">
                <option value="DEBUG">Debug</option>
                <option value="NORMAL">Normal</option>
              </select>
            </span>
          </div>

          <div class="column is-12">
          </div>

          <div class="column is-6">
            <app-button @click.prevent="killInstance" type="danger"><i class="fal fa-bug"></i>&nbsp; Kill
              Instance</app-button>
          </div>
        </div>
      </section>
    </div>

    <app-footerbar>
      <app-button @click="save" type="primary">Save</app-button>
    </app-footerbar>
  </div>
</template>

<script>
import { find, get } from 'lodash-es';
import { EVENT_INFO } from '../config';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'profile',

  data() {
    return {
      appMode: '',
      statuses: [],
      noun: 'User'
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'USER__state'
    ]),

    organizations() {
      return this.__state.user.OrgUnits;
    },

    organizationName() {
      let organizationKey = Number(this.__state.orgUnitKey);
      let organization = find(this.__state.user.OrgUnits, organization => {
        return Number(organization.Key) === organizationKey;
      });

      return get(organization, 'Name', '--');
    }
  },

  mounted() {
    this.appMode = this.__state.appMode;
  },

  methods: {
    ...mapActions([
      '__setAppMode',
      'INSTANCE__kill'
    ]),

    killInstance() {
      this.INSTANCE__kill({
        callback: () => {
          this.$hub.$emit(EVENT_INFO, 'Instance killed.');
        }
      });
    },

    save() {
      this.__setAppMode(this.appMode);
      this.close();
    }
  }
};
</script>
