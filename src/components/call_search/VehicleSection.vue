<template>
  <app-grid-form class="vehicle-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-8">
        <app-text v-model="call.vc25VIN" :maxlength="17">
          VIN
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.bOwnerWithVehicle">
          Number of Passengers
        </app-text>
      </div>
      <div class="column is-left is-4">
        <app-text v-model="call.iYear" maxlength="4" >
          Year
        </app-text>
      </div>
      <div class="column is-4">
        <app-shortcode v-model="call.lMakeKey" :options="makes" context="search" keyAlias="value" valueAlias="description" shortCodeAlias="shortCode" @change="getModels">
          Make
        </app-shortcode>
      </div>
      <div class="column is-4">
        <app-shortcode v-model="call.lModelKey" :options="models" context="search" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" @change="getModels">
          Model
        </app-shortcode>
      </div>
      <div class="column is-left is-4">
        <app-shortcode v-model="call.lBodyTypeKey" :options="bodyTypes" context="search" keyAlias="value" valueAlias="description" shortCodeAlias="shortCode">
          Body
        </app-shortcode>
      </div>
      <div class="column is-4">
        <app-shortcode v-model="call.lColorTypeKey" :options="colors" context="search" keyAlias="value" valueAlias="description" shortCodeAlias="shortCode">
          Color
        </app-shortcode>
      </div>
      <div class="column is-4">
        <app-text v-model="call.vc10Odometer" maxlength="100">
          Odometer
        </app-text>
      </div>
      <div class="column is-left is-4">
        <app-text v-model="call.vc15LicenseNum" maxlength="15">
          Tag Number
        </app-text>
      </div>
      <div class="column is-4">
        <app-select-state v-model="call.ch2StateKey">
          Tag State
        </app-select-state>
      </div>
      <div class="column is-4">
        <app-date-time v-model="call.dTagExpiry" :is-range="true">
          Tag Expiry
        </app-date-time>
      </div>
    <hr>
      <div class="column is-4 is-bottom">
        <app-text v-model="call.vc30OwnerName" maxlength="30">
          Owner Name
        </app-text>
      </div>
      <div class="column is-4 is-bottom">
        <app-text v-model="call.vc20OwnerPhone" maxlength="20">
          Owner Phone
        </app-text>
      </div>
      <div class="column is-4 is-bottom">
        <app-text v-model="call.vc30ExtraVehicleInfo" maxlength="30">
          Other Information
        </app-text>
      </div>
    </div>
  </app-grid-form>
</template>

<script>

import { map, find } from 'lodash-es';
import { mapActions } from 'vuex';
import BaseSection from './BaseSection.vue';

export default {
  name: 'vehicle-search',

  extends: BaseSection,

  data () {
    return {
      makes: [],
      colors: [],
      models: [],
      bodyTypes: []
    };
  },

  mounted () {
    this.getFieldOptions('GetMakes', 'makes');
    this.getFieldOptions('GetBodyTypes', 'bodyTypes');
    this.getFieldOptions('GetColors', 'colors');
  },

  methods: {
    ...mapActions([
      'CALL__getModels',
      'CALL__getFieldOptions'
    ]),

    getFieldOptions (verb, prop, lMakeKey = '') {
      this.CALL__getFieldOptions({
        verb: verb,
        makeKey: lMakeKey,
        callback: response => {
          map(response, option => {
            this.$data[prop].push({
              value: option[0],
              description: option[1],
              shortCode: option[2]
            });
          });
        }
      });
    },

    getModels () {
      const make = find(this.makes, ['value', this.call.lMakeKey]);
      if (!make.value) return;

      this.CALL__getModels({
        makeKey: make.value,
        callback: response => {
          this.models = response;
        }
      });
    }
  }
};
</script>
