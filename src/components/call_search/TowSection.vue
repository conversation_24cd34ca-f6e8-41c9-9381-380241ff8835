<template>
  <app-grid-form class="tow-search" context="inline">
    <div class="columns is-multiline">
      <div class="column is-4">
        <app-date-time v-model="call.dCallTaken" :is-range="true" :now-timestamp="false">
          Call Taken
        </app-date-time>
      </div>
      <div class="column is-4">
        <app-text v-model="call.vc30ContactName">
          Caller Name
        </app-text>
      </div>
      <div class="column is-4">
        <app-text v-model="call.vc20ContactPhoneNum">
          Caller Phone
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-suggestion v-model="call['TCUS.CUSvc30Name']" :options="customerNames" @keyup="getCustomers">
          Customer
        </app-suggestion>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="call.vc100Location">
          Tow Location
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <p class="control">
          <label>Tow Destination</label>
        </p>
        <div class="field has-addons">
          <p class="control" style="width: 25px">
            <span class="select">
              <select v-model="destinationLot" @change="formatDestinationLot" tabindex="-1">
                <option></option>
                <option v-for="lot in lots" :value="lot.Key" :key="lot.Key">{{ lot.Value }}</option>
              </select>
            </span>
          </p>
          <p class="control">
            <input v-model="call.vc100Destination" type="text" class="input" ref="vc100Destination">
          </p>
        </div>
      </div>
      <div class="column is-4 is-left">
        <InputTowType v-model="call.lTowTypeKey" context="search" />
      </div>
      <div class="column is-4">
        <InputReason v-model="call.lReasonTypeKey" context="search" />
      </div>
      <div class="column is-4">
        <app-shortcode v-model="call.lTruckTypeKey" :options="truckTypes" context="search" keyAlias="value" valueAlias="description" shortCodeAlias="shortCode">
          Truck Type
        </app-shortcode>
      </div>
      <div class="column is-6 is-left">
        <app-date-time v-model="call.dETA" :is-range="true">
          ETA
        </app-date-time>
      </div>
      <div class="column is-6">
        <app-date-time v-model="call.dAppointment" :is-range="true">
          Appointment
        </app-date-time>
      </div>
      <div class="column is-12 is-left">
        <app-text v-model="call.vc50EquipmentRequired" maxlength="50">
          Equipment Required
        </app-text>
      </div>
      <div class="column is-12 is-left">
        <app-textarea v-model="call.vc255DispatchNotes" id="CAL_vc255DispatchNotes" maxlength="255">
          Dispatch Notes
        </app-textarea>
      </div>
      <div class="column is-12 is-left">
        <app-textarea v-model="call.vc255DriverNotes" id="CAL_vc255DriverNotes" maxlength="255">
          Driver Notes
        </app-textarea>
      </div>
      <div class="column is-4 is-left">
        <app-select-boolean v-model="call.bNoCharge" id="CAL_bNoCharge">
          No Charge
        </app-select-boolean>
      </div>
      <div class="column is-4">
        <app-select-boolean v-model="call.bPortalToPortal" id="CAL_bPortalToPortal">
          Portal to Portal
        </app-select-boolean>
      </div>
      <div class="column is-4">
        <app-text v-model="call.fDiscountPct" id="CAL_fDiscountPct" maxlength="3">
          Discount %
        </app-text>
      </div>
      <div class="column is-6 is-left is-bottom">
        <app-text v-model="call.TowTicketNum">
          Tow Ticket Number
        </app-text>
      </div>
      <div class="column is-6 is-bottom">
        <app-select-boolean v-model="call.bSecondCommission" id="CAL_bSecondCommission">
          Second Commission
        </app-select-boolean>
      </div>
    </div>
  </app-grid-form>
</template>

<script>
import { mapActions } from 'vuex';
import { map, concat, find, has } from 'lodash-es';
import BaseSection from './BaseSection.vue';

export default {
  name: 'tow-search',

  extends: BaseSection,

  data () {
    return {
      destinationLot: '',

      lots: [],
      customers: [],
      truckTypes: []
    };
  },

  computed: {
    customerNames () {
      return map(this.customers, customer => {
        return customer.Name;
      });
    }
  },

  watch: {
    'call.TCUS.CUSvc30Name' () {
      this.setCustomer();
    }
  },

  mounted () {
    this.createIfMissing('TCUS.CUSvc30Name', '');
    this.createIfMissing('SCUS.CUSvc30Name', '');
    this.createIfMissing('CCUS.CUSvc30Name', '');

    this.getFieldOptions('GetTruckTypes', 'truckTypes');
    this.getStorageLots();
  },

  methods: {
    ...mapActions([
      'TOPSCALL__findCustomersForNew',
      'TOPSCOMPANY__getLots',
      'CALL__getFieldOptions'
    ]),

    getFieldOptions (verb, prop, lMakeKey = '') {
      this.CALL__getFieldOptions({
        verb: verb,
        makeKey: lMakeKey,
        callback: response => {
          map(response, option => {
            this.$data[prop].push({
              value: option[0],
              description: option[1],
              shortCode: option[2]
            });
          });
        }
      });
    },

    getStorageLots () {
      this.TOPSCOMPANY__getLots({
        callback: response => {
          this.lots = concat(
            [
              { Key: 'Empty', Value: 'Empty' },
              { Key: 'Not Empty', Value: 'Not Empty' }
            ],
            response
          );
        }
      });
    },

    formatDestinationLot () {
      let lot = find(this.lots, ['Key', this.destinationLot]);
      this.destinationLot = '';

      if (lot) {
        this.call.vc100Destination = has(lot, 'ShortCode')
          ? `{${lot.ShortCode}}`
          : lot.Value;
      }
    },

    getCustomers () {
      if (this.call['TCUS.CUSvc30Name'].length < 3) return;

      this.TOPSCALL__findCustomersForNew({
        data: { Name: this.call['TCUS.CUSvc30Name'] },
        callback: response => {
          this.customers = response;
        }
      });
    },

    setCustomer () {
      let customer = find(this.customers, ['Name', this.call.sCustomer]);

      if (!customer) return;

      this.$set(this.call, 'lCustomerKey', customer.Key);
    }
  }
};
</script>
