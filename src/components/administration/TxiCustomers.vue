<template>
  <div class="txi-customers-view">
    <grid
      :grid="gridSettings"
      :data="gridData"
      :noun="viewConfig.noun"
      :show-loader="showLoader">
      <div slot="title">TXI Customers</div>
      <div slot="context-tools">
        <app-button type="success" @click="add">Add</app-button>
      </div> <!-- /context-tools -->
    </grid>
  </div> <!-- /txi-customers-view -->
</template>

<script>
import Grid from '@/components/features/Grid.vue';
import RecordsView from '@/components/ancestors/RecordsView.vue';

export default {
  name: 'txi-customers-view',

  extends: RecordsView,

  components: { Grid },

  data () {
    return {
      search: {
        term: '',
        target: 'customerNumber'
      },
      viewConfig: {
        uuid: 'txi-customers-screen',
        noun: 'TXICustomer',
        recordKeyName: 'lCustomerKey',
        readRouteName: 'TxiCustomerEdit'
      }
    };
  },

  methods: {
    add () {
      this.$router.push({ name: 'TxiCustomerCreate' });
    },

    search () {
      console.log('Search', this.search);
    }
  }
};
</script>

<style scoped>
</style>
