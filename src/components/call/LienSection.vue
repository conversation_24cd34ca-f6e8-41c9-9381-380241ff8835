<template>
<span id="lien-section">
  <ul class="-tabs">
    <li :class="tabClasses('process')" @click="selectSection('process')">Process</li>
    <li :class="tabClasses('steps')" @click="selectSection('steps')">Steps</li>
    <li :class="tabClasses('owners')" @click="selectSection('owners')">Owners</li>
    <li :class="tabClasses('letters')" @click="selectSection('letters')">Letters</li>
  </ul>

  <section class="-section -process" v-if="selection.section === 'process'">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-6">
          <app-select id="lien.lLienProcessKey" v-model="call.Lien.lLienProcessKey" :options="processes" keyAlias="Key" valueAlias="Value" :disabled="true">
            Process
          </app-select>
        </div>
        <div class="column is-6">
          <app-select id="lien.lLienStatusTypeKey" v-model="call.Lien.lLienStatusTypeKey" :options="statuses" keyAlias="Key" valueAlias="Value" :disabled="true">
            Status
          </app-select>
        </div>
        <div class="column is-4 is-left">
          <app-date-time id="lien.dDateOpened" v-model="call.Lien.dDateOpened" :disabled="true">
            Opened
          </app-date-time>
        </div>
        <div class="column is-4">
          <app-date-time id="lien.dDateClosed" v-model="call.Lien.dDateClosed" :disabled="true">
            Closed
          </app-date-time>
        </div>
        <div class="column is-4">
          <app-checkbox id="lien.bDontSendToCollections" v-model="call.Lien.bDontSendToCollections" :disabled="!canEditProperty()">
            No Collections
          </app-checkbox>
        </div>
        <div class="column is-4 is-left">
          <app-text id="lien.vc50UserDefined1" v-model="call.Lien.vc50UserDefined1" :disabled="!canEditProperty()">
            {{ TOPSCOMPANY__settings.vc15Label_LienProcess_UserDef1 }}
          </app-text>
        </div>
        <div class="column is-4">
          <app-text id="lien.vc50UserDefined2" v-model="call.Lien.vc50UserDefined2" :disabled="!canEditProperty()">
            {{ TOPSCOMPANY__settings.vc15Label_LienProcess_UserDef2 }}
          </app-text>
        </div>
        <div class="column is-4">
          <app-number id="lien.tcLienPrice" v-model="call.Lien.tcLienPrice" :disabled="!canEditProperty()">
            Price
          </app-number>
        </div>
        <div class="column is-12 is-left">
          <app-text id="lien.vc255Notes" v-model="call.Lien.vc255Notes" maxlength="255" :disabled="!canEditProperty()">
            Notes
          </app-text>
        </div>
      </div> <!-- /columns -->
    </app-grid-form>

    <div class="-actions">
      <transition name="flip" mode="out-in">
        <div class="-tools" data-context="terminate" v-if="subtoolVisible.terminater" key="lien-terminator">
          <app-checkbox class="-input" v-model="removePricingItems" orient="row" :disabled="!canEditProperty()">
            Remove Pricing Items
          </app-checkbox>
          <app-button type="primary" @click="terminate">
            Terminate
          </app-button>
          <app-button class="-cancel" type="default" @click="toggleTerminater" title="Cancel Terminate" :disabled="!canEditProperty()">
            <i class="fal fa-times"></i>
          </app-button>
        </div>
        <div class="-tools" v-else>
          <app-button @click="toggleTerminater" type="default" v-if="canTerminate" :disabled="!canEditProperty()">
            Terminate&hellip;
          </app-button>
        </div>
      </transition>
    </div>
  </section>

  <section class="-section -steps" v-if="selection.section === 'steps'">
    <div class="-global-note" v-if="call.Lien.vc255Notes">
      <i class="-icon fas fa-info-circle"></i>
      <div class="-text">{{ call.Lien.vc255Notes }}</div>
    </div>

    <app-accordian class="_step" v-for="step in call.Lien.Steps" :expand-on-mount="step.expandOnMount" :key="step.lCallLienStepKey">
      <div class="_thumbnail">
        <app-data-point label="Step">
          {{ step.Name }}
          <i class="_required fas fa-circle-small" v-if="step.Required === 'true'" title="Required"></i>
        </app-data-point>
        <app-data-point label="Status">{{ resolveStatusAttribute('Value', step) }}</app-data-point>
        <app-data-point class="_opened-at" label="Opened">{{ step.dDateOpened | simpleDateTime }}</app-data-point>
        <app-data-point class="_closed-at" label="Closed">
          <span v-if="step.dDateClosed">{{ step.dDateClosed | simpleDateTime }} by {{ step.sClosedBy }}</span>
          <span v-else>--</span>
        </app-data-point>
        <app-data-point class="_activated-at" label="Activated">
          <span v-if="step.ActivationDate">{{ step.ActivationDate | simpleDateTime }}</span>
          <span v-else>--</span>
        </app-data-point>
      </div>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns">
            <div class="column">
              <app-text v-model="step.vc255Notes" :maxlength="255" :disabled="!canEditProperty()">
                Notes
              </app-text>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-number v-model="step.iActivationQty" disabled>
                Quantity
              </app-number>
            </div>
            <div class="column">
              <app-select v-model="step.lActivationUnitsTypeKey" :options="activationUnits" keyAlias="Key" valueAlias="Value" disabled>
                Units
              </app-select>
            </div>
            <div class="column">
              <app-select v-model="step.lActivationBasisTypeKey" :options="activationBasises" keyAlias="Key" valueAlias="Value" disabled>
                Basis
              </app-select>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="step.vc50UserDefined1" :maxlength="50" :disabled="!canEditProperty()">
                {{ TOPSCOMPANY__settings.vc15Label_LienSteps_UserDef1 }}
              </app-text>
            </div>
          </div>
        </app-grid-form>

        <div class="_actions">
          <transition-group name="flip-horizontal" mode="out-in">
            <div class="-complete" v-if="stepActionIs(step, 'complete')" key="complete">
              <div class="-question">
                {{ step.CompletionQuestion }}
              </div>
              <div class="-controls">
                <app-button @click="complete(step)" type="primary" :disabled="!canEditProperty()">
                  Yes
                </app-button>
                <app-button @click="selectStepAction(step)" :disabled="!canEditProperty()">
                  <i class="fal fa-times"></i>
                </app-button>
              </div>
            </div>

            <div class="-undo" v-if="stepActionIs(step, 'undo')" key="undo">
              <div class="-question">
                <app-checkbox v-model="removePricingItems" v-if="askToRemovePricingItems(step)" :disabled="!canEditProperty()">
                  Remove pricing items
                </app-checkbox>
              </div>
              <div class="-controls">
                <app-button @click="undo(step)" type="primary" :disabled="!canEditProperty()">
                  Undo
                </app-button>
                <app-button @click="selectStepAction(step)" :disabled="!canEditProperty()">
                  <i class="fal fa-times"></i>
                </app-button>
              </div>
            </div>

            <div class="-hold" v-if="stepActionIs(step, 'hold')" key="hold">
              <app-number v-model="holdHours" size="small" @change="holdUntil = ''" :disabled="!canEditProperty()">
                Hours to Hold
              </app-number>
              <app-date-time v-model="holdUntil" size="small" @change="holdHours = ''" :disabled="!canEditProperty()">
                Hold Until
              </app-date-time>
              <div class="-controls">
                <app-button @click="hold" type="primary" :disabled="!holdHours && !holdUntil">
                  Hold
                </app-button>
                <app-button @click="selectStepAction(step)">
                  <i class="fal fa-times"></i>
                </app-button>
              </div>
            </div>

            <div class="-menu" v-if="stepActionIs(step, '')" key="menu">
              <app-button @click="selectStepAction(step, 'complete')" :disabled="!canComplete(step)">
                Complete&hellip;
              </app-button>
              <app-button @click="skip(step)" :disabled="!canSkip(step)">
                Skip
              </app-button>
              <app-button @click="selectStepAction(step, 'undo')" :disabled="!canUndo(step)">
                Undo&hellip;
              </app-button>
              <app-button @click="selectStepAction(step, 'hold')" :disabled="!canHold(step)">
                Hold&hellip;
              </app-button>
              <app-button @click="releaseHold(step)" :disabled="!canReleaseHold(step)">
                Release Hold
              </app-button>
            </div>
          </transition-group>
        </div>

        <section class="-tasks" v-if="stepTasksProxy(step).length > 0">
          <div class="-label">Tasks</div>
          <ul class="-list">
            <li class="-task" v-for="task in stepTasksProxy(step)" :key="task.lLienTaskKey" :data-expanded="task.prerequisitesVisible">
              <div class="-view" key="task-view">
                <div class="-name">{{ task.sLienTask }}</div>
                <div class="-description">
                  {{ task.vc255Description }}
                </div>
                <div class="-type">Type: {{ task.sLienTaskType }}</div>
                <div class="-copies" v-if="task.tIterations > 0">Copies: {{ task.tIterations }}</div>

                <app-button class="-action -toggle" :data-visible="!task.prerequisitesVisible" @click="task.prerequisitesVisible = true" :disabled="!canProcessTask">
                  Process&hellip;
                </app-button>
                <app-button class="-action -toggle" :data-visible="task.prerequisitesVisible" @click="task.prerequisitesVisible = false">
                  Close
                </app-button>

                <div class="-prerequisites" v-if="task.prerequisitesVisible" key="task-prerequisites">
                  <div class="-inputs">
                    <app-date-time v-model="lettersSentDate" v-if="taskHasLettersSent(task)" :date-picker="true">
                      Letters Sent Date
                    </app-date-time>
                    <app-select v-model="labelFormat" :options="labels" v-if="taskHasLabels(task)" keyAlias="Key" valueAlias="Value" :empty-option="true">
                      Label Format
                    </app-select>
                    <app-number v-model="startLabel" v-if="taskHasLabels(task)" min="1">
                      Start Label
                    </app-number>
                    <app-date-time v-model="setDate" v-if="taskHasSetDate(task)" :date-picker="true">
                      Date to Set
                    </app-date-time>
                    <app-text v-model="certificationNumber" v-if="task.bUsesCertificationNum">
                      Certification Number
                    </app-text>
                    <app-text v-model="certificationSeed" v-if="task.bUsesCertificationNum">
                      Certification Seed
                    </app-text>
                    <app-select v-model="targetProcessKey" v-if="taskIsChangeProcess(task)" :options="processes" keyAlias="Key" valueAlias="Value">
                      Switch to Process
                    </app-select>
                  </div>
                </div>

                <app-button class="-action -process" v-if="task.prerequisitesVisible" type="primary" @click="processTask(step, task)" :disabled="!taskPrerequisitesMet(task)">
                  Process
                </app-button>
              </div>
            </li>
          </ul>
        </section>
      </template>
    </app-accordian>
  </section>

  <section class="-section -owners" v-if="selection.section === 'owners'">
    <div class="-global-note" v-if="call.Lien.vc255Notes">
      <i class="-icon fas fa-info-circle"></i>
      <div class="-text">{{ call.Lien.vc255Notes }}</div>
    </div>

    <app-accordian class="_owner" v-for="owner in call.Lien.Owners" :expand-on-mount="owner.expandOnMount" :key="owner.vueKey || owner.lOwnerKey">
      <div class="_thumbnail">
        <app-data-point label="Name">{{ owner.vc50Name || 'Unnamed owner' }}</app-data-point>
        <app-data-point label="Type">{{ resolveOwnerTypeAttribute('Value', owner) }}</app-data-point>
      </div>

      <template slot="controls">
        <app-button class="-remove" @click="removeOwner(owner.vueKey)" type="default" :disabled="!canEdit">
          <i class="far fa-trash-alt"></i>
        </app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns">
            <div class="column">
              <app-select v-model="owner.lOwnerTypeKey" :options="ownerTypes" keyAlias="Key" valueAlias="Value" :disabled="!canEditProperty()">
                Type
              </app-select>
            </div>
            <div class="column">
              <app-text v-model="owner.vc50Name" :maxlength="50" :disabled="!canEditProperty()">
                Name
              </app-text>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="owner.vc30Address1" :maxlength="30" :disabled="!canEditProperty()">
                Address 1
              </app-text>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="owner.vc30Address2" :maxlength="30" :disabled="!canEditProperty()">
                Address 2
              </app-text>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="owner.vc30City" :maxlength="30" :disabled="!canEditProperty()">
                City
              </app-text>
            </div>
            <div class="column">
              <app-text v-model="owner.ch2StateKey" :maxlength="2" :disabled="!canEditProperty()">
                State
              </app-text>
            </div>
            <div class="column">
              <app-text v-model="owner.vc10ZipCode" :maxlength="10" :disabled="!canEditProperty()">
                Zip
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <div class="_actions">
      <app-button class="-add" @click="addOwner" type="default" :disabled="!canEdit">Add owner</app-button>
    </div>
  </section>

  <section class="-section -letters" v-if="selection.section === 'letters'">
    <div class="-global-note" v-if="call.Lien.vc255Notes">
      <i class="-icon fas fa-info-circle"></i>
      <div class="-text">{{ call.Lien.vc255Notes }}</div>
    </div>

    <app-accordian class="_letter" v-for="letter in call.Lien.Letters" :expand-on-mount="letter.expandOnMount" :key="letter.vueKey || letter.lLienLetterKey">
      <div class="_thumbnail">
        <app-data-point label="Owner">{{ resolveOwnerAttribute('vc50Name', letter) }}</app-data-point>
        <app-data-point label="Task Name">{{ resolveTaskAttribute('Name', letter) }}</app-data-point>
        <app-data-point label="Date Sent">{{ letter.dDateSent }}</app-data-point>
      </div>

      <template slot="controls">
        <app-button class="-remove" @click="removeLetter(letter.vueKey)" type="default" :disabled="!canEdit">
          <i class="far fa-trash-alt"></i>
        </app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns">
            <div class="column">
              <app-data-point class="_faux-input" label="Owner">
                {{ resolveOwnerAttribute('vc50Name', letter) }}
              </app-data-point>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-select v-model="letter.lLienTaskKey" :options="allTasks" keyAlias="Key" valueAlias="Name" :disabled="!canEditProperty()">
                Task Name
              </app-select>
            </div>
            <div class="column">
              <app-date-time v-model="letter.dDateSent" :disabled="!canEditProperty()">
                Date Sent
              </app-date-time>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="letter.vc20CertificationNum" :maxlength="20" :disabled="!canEditProperty()">
                Certification Number
              </app-text>
            </div>
            <div class="column">
              <app-date-time v-model="letter.dReturnReceiptDate" :disabled="!canEditProperty()">
                Return Receipt Date
              </app-date-time>
            </div>
          </div>
          <div class="columns">
            <div class="column">
              <app-text v-model="letter.vc50Notes" :maxlength="50" :disabled="!canEditProperty()">
                Notes
              </app-text>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <div class="_actions">
      <app-select v-model="selection.ownerSelectedForLetter" @change="addLetter" :options="addLetterOptions" keyAlias="lOwnerKey" valueAlias="vc50Name" :empty-option="false" placeholder="Add letter..." :disabled="!canEdit">
      </app-select>
    </div>
  </section>
</span>
</template>

<script>import { find, filter, concat, get, includes, has, forEach, uniqueId, reject } from 'lodash-es';


import datefns from 'date-fns';
import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';

import {
  VALUE_ID,
  EVENT_TRIGGER_CALL_READ,
  CALL_SECTION_LIEN
} from '@/config.js';

export default {
  name: 'lien-section',

  extends: BaseSection,

  data () {
    return {
      sectionName: CALL_SECTION_LIEN,

      labels: [],
      allTasks: [],
      statuses: [],
      processes: [],
      ownerTypes: [],
      stepStatuses: [],
      activationUnits: [],
      activationBasises: [],
      removePricingItems: true,
      callSectionDetails: 'lien',

      holdHours: '',
      holdUntil: '',

      setDate: datefns.format(new Date(), 'MM/DD/YYYY'),
      lettersSentDate: datefns.format(new Date(), 'MM/DD/YYYY'),
      labelFormat: '',
      startLabel: 0,
      certificationNumber: '',
      certificationSeed: '',
      targetProcessKey: 0,

      subtoolVisible: {
        terminater: false
      },

      selection: {
        section: '',
        ownerSelectedForLetter: ''
      },

      selectedLetter: {}
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    selectedOwner () {
      let owner = find(this.call.Lien.Owners, ['vueKey', this.selection.ownerKey]);

      return owner || {};
    },

    addLetterOptions () {
      let validOptions = filter(this.call.Lien.Owners, owner => {
        return owner.lOwnerKey;
      });

      return concat({ lOwnerKey: 'empty', vc50Name: 'Without Owner' }, validOptions);
    },

    canEdit () {
      return Access.has('liens.editOnCall');
    },

    canTerminate () {
      return this.canEdit &&
        Access.has('liens.terminate') &&
        this.call.Lien.lLienStatusTypeKey !== VALUE_ID.lienStatus.terminated;
    },

    canProcessTask () {
      if (import.meta.env.VITE_OBSERVE_LIEN_BATCH_RULE === 'true' &&
        Number(get(this.__state, 'topsCompany.settings.bWebTOPSLienAvailable', 0)) === 1) {
        return true;
      }

      return this.canEditProperty();
    }
  },

  watch: {
    'call.Lien.lLienProcessKey' () {
      this.getAllTasks();
    }
  },

  methods: {
    ...mapActions([
      'LABEL__getLayouts',
      'LIEN__getStatuses',
      'LIENSTEP__getTasks',
      'LIEN__getProcesses',
      'TOPSCALL__holdStep',
      'TOPSCALL__undoStep',
      'TOPSCALL__skipStep',
      'LIENSTEP__getStatuses',
      'TOPSCALL__processTask',
      'TOPSCALL__activateStep',
      'TOPSCALL__terminateLien',
      'TOPSCALL__lienCompleteStep',
      'LIENPROCESS__getAllTasks',
      'LIENSTEPTASK__getOwnerTypes',
      'LIENSTEP__getActivationUnits',
      'LIENSTEP__getActivationBasises'
    ]),

    stepTasksProxy (step) {
      return get(step, 'Tasks', []);
    },

    canComplete (step) {
      let statuses = [
        VALUE_ID.stepStatus.completed,
        VALUE_ID.stepStatus.skipped
      ];

      return this.canEdit && !includes(statuses, step.lLienStepStatusTypeKey);
    },

    canUndo (step) {
      let statuses = [
        VALUE_ID.stepStatus.completed
      ];

      return this.canEdit && !includes(statuses, step.lLienStepStatusTypeKey);
    },

    canSkip (step) {
      let statuses = [
        VALUE_ID.stepStatus.completed
      ];

      return this.canEdit &&
        !includes(statuses, step.lLienStepStatusTypeKey) &&
        !step.Required === 'true';
    },

    canHold (step) {
      let statuses = [
        VALUE_ID.stepStatus.onHold,
        VALUE_ID.stepStatus.completed
      ];

      return this.canEdit && !includes(statuses, step.lLienStepStatusTypeKey);
    },

    canReleaseHold (step) {
      let statuses = [
        VALUE_ID.stepStatus.onHold
      ];

      return this.canEdit && includes(statuses, step.lLienStepStatusTypeKey);
    },

    selectSection (value) {
      this.selection.section = value;
      if (value === 'steps') {
        this.getTasks();
      }
    },

    selectStepAction (step, value = '') {
      this.$set(step, 'ActionPanel', value);
    },

    stepActionIs (step, value = '') {
      return get(step, 'ActionPanel', '') === value;
    },

    tabClasses (value) {
      return {
        '-tab': true,
        '-focused': this.selection.section === value
      };
    },

    getStatuses () {
      this.LIEN__getStatuses({
        callback: response => {
          this.statuses = response;
        }
      });
    },

    getStepStatuses () {
      this.LIENSTEP__getStatuses({
        callback: response => {
          this.stepStatuses = response;
        }
      });
    },

    getProcesses () {
      this.LIEN__getProcesses({
        callback: response => {
          this.processes = response;
        }
      });
    },

    resolveStatusAttribute (attribute, { lLienStepStatusTypeKey }) {
      let status = find(this.stepStatuses, ['Key', lLienStepStatusTypeKey]);

      return get(status, attribute, '');
    },

    resolveOwnerAttribute (attribute, { lOwnerKey }) {
      let owner = find(this.call.Lien.Owners, ['lOwnerKey', lOwnerKey]);

      return get(owner, attribute, '');
    },

    resolveTaskAttribute (attribute, { lLienTaskKey }) {
      let task = find(this.allTasks, ['Key', lLienTaskKey]);

      return get(task, attribute, '');
    },

    resolveOwnerTypeAttribute (attribute, { lOwnerTypeKey }) {
      let owner = find(this.ownerTypes, ['Key', Number(lOwnerTypeKey)]);

      return get(owner, attribute, '');
    },

    getTasks () {
      let steps = get(this.call, 'Lien.Steps', []);

      for (let index = 0; index < steps.length; index++) {
        const step = steps[index];
        this.$set(step, 'Tasks', []);

        this.LIENSTEP__getTasks({
          key: step.lLienStepKey,
          callback: response => {
            let tasks = response;

            tasks.forEach(task => {
              this.$set(task, 'prerequisitesVisible', false);
            });

            this.$set(step, 'Tasks', tasks);
          }
        });
      }
    },

    getAllTasks () {
      if (!this.call.Lien.lLienProcessKey) return;

      this.LIENPROCESS__getAllTasks({
        key: this.call.Lien.lLienProcessKey,
        callback: response => {
          this.allTasks = response;
        }
      });
    },

    taskHasLettersSent ({ lLienTaskTypeKey }) {
      return lLienTaskTypeKey === VALUE_ID.lienTaskType.letter;
    },

    taskHasLabels ({ lLienTaskTypeKey }) {
      return lLienTaskTypeKey === VALUE_ID.lienTaskType.label;
    },

    taskHasSetDate ({ lLienTaskTypeKey }) {
      return lLienTaskTypeKey === VALUE_ID.lienTaskType.setDate;
    },

    taskIsChangeProcess ({ lLienTaskKey }) {
      return lLienTaskKey === VALUE_ID.lienTask.changeProcess;
    },

    taskPrerequisitesMet (task) {
      if (this.taskHasLettersSent(task) && !this.lettersSentDate) return false;
      if (this.taskHasLabels(task) && !this.labelFormat) return false;
      if (this.taskHasSetDate(task) && !this.setDate) return false;
      if (task.bUsesCertificationNum && !this.certificationNumber) return false;
      if (task.bUsesCertificationNum && !this.certificationSeed) return false;
      if (this.taskIsChangeProcess(task) && !this.targetProcessKey) return false;

      return true;
    },

    async processTask (step, task) {
      this.TOPSCALL__processTask({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        stepKey: step.lCallLienStepKey,
        taskKey: task.lLienTaskKey,
        setDate: this.setDate,
        lettersSentDate: this.lettersSentDate,
        labelFormat: this.labelFormat,
        startLabel: this.startLabel,
        certificationNumber: this.certificationNumber,
        certificationSeed: this.certificationSeed,
        targetProcessKey: this.targetProcessKey,
        callback: response => {
          task.prerequisitesVisible = false;

          if (!has(response, 'Documents')) {
            this.afterProcessTask(step);
            return;
          }

          forEach(response.Documents, document => {
            window.open(document.URL, '_blank');
            window.focus();
          });

          if (window.confirm('Were the letters printed correctly?')) {
            this.addLetter(task);
          }

          this.$hub.$emit(EVENT_TRIGGER_CALL_READ);
        }
      });
    },

    afterProcessTask (step) {
      this.resetTaskPrerequisites();

      this.$hub.$emit(EVENT_TRIGGER_CALL_READ);
    },

    afterCallRead () {
      this.setTemporaryOwnerKeys();
      this.setTemporaryLetterKeys();
    },

    setTemporaryOwnerKeys () {
      forEach(this.call.Lien.Owners, owner => {
        this.$set(owner, 'vueKey', uniqueId('owner-'));
      });
    },

    setTemporaryLetterKeys () {
      forEach(this.call.Lien.Letters, letter => {
        this.$set(letter, 'vueKey', uniqueId('letter-'));
      });
    },

    resetTaskPrerequisites () {
      this.certificationNumber = '';
      this.certificationSeed = '';
      this.targetProcessKey = '';
    },

    async skip (step) {
      this.TOPSCALL__skipStep({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        callback: response => {
          this.readCall();
          this.selectStepAction(step);
        }
      });
    },

    async undo (step) {
      this.TOPSCALL__undoStep({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        removePricingItems: this.removePricingItems,
        callback: response => {
          this.readCall();
          this.selectStepAction(step);
        }
      });
    },

    async hold (step) {
      this.TOPSCALL__holdStep({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        hours: this.holdHours,
        until: this.holdUntil,
        callback: response => {
          this.readCall();
          this.selectStepAction(step);
        }
      });
    },

    async releaseHold (step) {
      this.TOPSCALL__activateStep({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        callback: response => {
          this.readCall();
          this.selectStepAction(step);
        }
      });
    },

    getActivationUnits () {
      this.LIENSTEP__getActivationUnits({
        callback: response => {
          this.activationUnits = response;
        }
      });
    },

    getActivationBasises () {
      this.LIENSTEP__getActivationBasises({
        callback: response => {
          this.activationBasises = response;
        }
      });
    },

    async complete (step) {
      this.TOPSCALL__lienCompleteStep({
        callKey: this.call.lCallKey,
        lastRead: await this.getNow(),
        success: response => {
          this.readCall();
          this.selectStepAction(step);
        }
      });
    },

    getOwnerTypes () {
      this.LIENSTEPTASK__getOwnerTypes({
        callback: response => {
          this.ownerTypes = response;
        }
      });
    },

    addOwner () {
      let owner = {
        vueKey: uniqueId('owner-'),
        CallKey: this.call.lCallKey,
        lOwnerKey: '',
        vc50Name: '',
        lLienKey: this.call.Lien.lLienKey,
        lOwnerTypeKey: '',
        vc30Address1: '',
        vc30Address2: '',
        vc30City: '',
        ch2StateKey: '',
        vc10ZipCode: '',
        bActive: true,
        expandOnMount: true
      };

      this.call.Lien.Owners.push(owner);
    },

    removeOwner (vueKey) {
      this.call.Lien.Owners = reject(this.call.Lien.Owners, ['vueKey', vueKey]);
    },

    addLetter () {
      let ownerSelectedForLetter = this.selection.ownerSelectedForLetter === 'empty'
        ? ''
        : this.selection.ownerSelectedForLetter;

      let letter = {
        vueKey: uniqueId('letter-'),
        lLienLetterKey: '',
        lLienKey: this.call.Lien.lLienKey,
        lOwnerKey: ownerSelectedForLetter,
        lLienTaskKey: '',
        dDateSent: '',
        vc20CertificationNum: '',
        dReturnReceiptDate: '',
        vc50Notes: '',
        bActive: '',
        lBatchKey: '',
        expandOnMount: true
      };

      this.call.Lien.Letters.push(letter);

      this.$nextTick(() => {
        this.selection.ownerSelectedForLetter = '';
      });
    },

    removeLetter (vueKey) {
      this.call.Lien.Letters = reject(this.call.Lien.Letters, ['vueKey', vueKey]);
    },

    toggleTerminater () {
      this.subtoolVisible.terminater = !this.subtoolVisible.terminater;
    },

    async terminate () {
      this.TOPSCALL__terminateLien({
        lastRead: await this.getNow(),
        callKey: this.call.lCallKey,
        removePricingItems: this.removePricingItems,
        callback: response => {
          this.close();
        }
      });
    },

    getLabels () {
      this.LABEL__getLayouts({
        callback: response => {
          this.labels = response;
        }
      });
    },

    getPricingItemsForStep (step) {
      return filter(this.call.TowOrderLines, ['lCallLienStepKey', step.lCallLienStepKey]);
    },

    askToRemovePricingItems (step) {
      let priceItems = this.getPricingItemsForStep(step);

      return priceItems.length > 0;
    }
  },

  mounted () {
    this.selectSection('steps');
    this.getLabels();
    this.getStatuses();
    this.getAllTasks();
    this.getProcesses();
    this.getOwnerTypes();
    this.getStepStatuses();
    this.getActivationUnits();
    this.getActivationBasises();
  }
};
</script>
