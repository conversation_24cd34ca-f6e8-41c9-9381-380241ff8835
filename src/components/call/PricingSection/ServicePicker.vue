<template>
  <div id="service-picker">
    <button
      id="service-picker-trigger"
      class="button is-small"
      popovertarget="service-picker-popover"
      :disabled="!canAddPricing">
      Add services&hellip;
    </button>

    <dialog
      id="service-picker-popover"
      popover
      @toggle="onPopoverToggle">

      <section class="filters-panel">
        <select id="serviceFilterDispatch" v-model.number="dispatchKey">
          <option value="">Select a dispatch</option>
          <option v-for="dispatch in dispatches"
            :value="dispatch.lDispatchKey"
            :disabled="!dispatch.isEnabled"
            :key="dispatch.lDispatchKey">
            {{ dispatch.DriverTruckPair }}
          </option>
        </select>
        <input type="search" id="serviceFilterControl" v-model="filterTerm" placeholder="Filter services..." autofocus />
        <tab-group v-model="categoryTerm">
          <tab-item value="All Services" :disabled="!canSeeAllServices">All</tab-item>
          <tab-item value="Basic Services">Basic</tab-item>
          <tab-item value="Customer Services">Customer</tab-item>
        </tab-group>
      </section>

      <ServiceList
        :services="filteredServices"
        :dispatch-key="dispatchKey"
        :call="call"
        @on-add-service="$emit('on-add-service', $event)" />

    </dialog>
  </div>
</template>

<script>import { filter, includes, toLower, orderBy } from 'lodash-es';


import Access from '@/utils/access.js';
import ServiceList from './ServiceList.vue';

export default {
  name: 'ServicePicker',

  components: {
    ServiceList
  },

  props: {
    canAddPricing: {
      type: Boolean,
      required: true
    },
    call: {
      type: Object,
      required: true
    },
    dispatches: {
      type: Array,
      required: true
    }
  },

  inject: [
    'isNewCall',
    'isTowReconciled',
    'isRetowReconciled',
    'canAddService'
  ],

  data () {
    return {
      services: [],
      dispatchKey: '',
      filterTerm: '',
      categoryTerm: 'All Services'
    };
  },

  computed: {
    filteredServices () {
      let filteredServices = [];

      filteredServices = !this.filterTerm
        ? this.services
        : filter(this.services, service => includes(toLower(service.Description), toLower(this.filterTerm)));

      return orderBy(filteredServices, ['Description']);
    },

    canSeeAllServices () {
      return Access.has('prices.allServices');
    }
  },

  watch: {
    categoryTerm () {
      this.getAvailableServices();
    }
  },

  methods: {
    onPopoverToggle (event) {
      if (event.newState === 'open') {
        if (!this.categoryTerm) {
          this.categoryTerm = this.canSeeAllServices ? 'All Services' : 'Basic Services';
        }

        this.attemptSelectDispatch();
        this.getAvailableServices();
      }
    },

    attemptSelectDispatch () {
      if (!this.dispatchKey &&
        this.dispatches.length === 1 &&
        this.dispatches[0].isEnabled) {
        this.dispatchKey = this.dispatches[0].lDispatchKey;
      }
    },

    async getAvailableServices () {
      const fetchForNewCall = () => {
        return new Promise((resolve, reject) => {
          this.$store.dispatch('TOPSCALL__getAvailableServicesForNew', {
            towTypeKey: this.call.lTowTypeKey,
            subterminalKey: this.call.lSubterminalKey,
            customerKey: this.call.lCustomerKey,
            callback: response => { resolve(response); }
          });
        });
      };

      const fetchForExistingCall = () => {
        return new Promise((resolve, reject) => {
          this.$store.dispatch('TOPSCALL__getAvailableServices', {
            callKey: this.call.lCallKey,
            orderType: 'T',
            filter: this.categoryTerm,
            callback: response => { resolve(response); }
          });
        });
      };

      if (this.isNewCall()) {
        this.services = await fetchForNewCall();
      } else {
        this.services = await fetchForExistingCall();
      }
    }
  }
};
</script>

<style scoped>
#service-picker {

  #service-picker-popover {
    max-height: calc(100dvh - 2rem);
    overflow-y: auto;
  }

  .filters-panel {
    position: sticky;
    left: 0;
    top: 0;

    display: grid;
    grid-template-columns: 1fr 1fr max-content;
    gap: 0.5rem;

    padding: 0.25rem 1rem;
    width: 100%;
    background: hsla(0, 0%, 100%, 0.0);
    backdrop-filter: blur(0.5rem);
    z-index: 10;

    #serviceFilterDispatch,
    #serviceFilterControl {
      padding: 0.5rem;
      background-color: white;
      border: 0.2rem solid hsla(var(--blue-h), var(--blue-s), var(--blue-l), 0.1) !important;
      border-radius: 0.5rem;
    }
  }

}
</style>
