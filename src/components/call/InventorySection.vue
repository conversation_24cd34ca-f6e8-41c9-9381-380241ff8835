<template>
  <span id="inventory-section">
    <div class="_card-swipe">
      <card-swipe @swipe="processSwipe"></card-swipe>
    </div>

    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-6">
          <app-shortcode id="INV_lStorageLotKey" v-model="call.Inventory.lStorageLotKey" :options="lots" :tabindex="1" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
            Lot
          </app-shortcode>
        </div>
        <div class="column is-6">
          <app-select id="CAL_lFinalDispositionTypeKey" v-model="call.lFinalDispositionTypeKey" :options="dispositions" :tabindex="10" :disabled="true" keyAlias="Key" valueAlias="Value" shortCodeAlias="ShortCode">
            Release
          </app-select>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.Inventory.dDateIn" id="INV_dDateIn" :disabled="!canEditInventoryInOut" :tabindex="2">
            Date In
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.Inventory.vc30Name1" id="inventory.vc30Name1" maxlength="30" tabindex="11" :disabled="!canEditProperty()">
            Name
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.Inventory.vc15LotLocation" id="INV_vc15LotLocation" maxlength="15" tabindex="3" :disabled="!canEditProperty()">
            Location in Lot
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.Inventory.vc30Address1" id="inventory.vc30Address1" maxlength="30" tabindex="12" :disabled="!canEditProperty()">
            Address
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.Inventory.dPlannedAuction" id="INV_dPlannedAuction" :tabindex="4" :now-timestamp="false" :disabled="!canEditProperty()">
            Planned Auction
          </app-date-time>
        </div>
        <div class="column is-6">
          <app-text v-model="call.Inventory.vc30Address2" id="inventory.vc30Address2" maxlength="30" tabindex="13" :disabled="!canEditProperty()">
            Address 2
          </app-text>
        </div>
        <div class="column is-5 is-left">
          <app-date-time v-model="call.Inventory.dStopBillingDate" id="INV_dStopBillingDate" :tabindex="5" :is-range="true" :disabled="!canEditProperty()">
            Stop Billing Date
          </app-date-time>
        </div>
        <div class="column is-3">
          <app-text v-model="call.Inventory.vc30City" id="inventory.vc30City" maxlength="30" tabindex="14" :disabled="!canEditProperty()">
            City
          </app-text>
        </div>
        <div class="column is-2">
          <app-select-state v-model="call.Inventory.ch2StateKey" id="inventory.ch2StateKey" tabindex="15" :disabled="!canEditProperty()">
            State
          </app-select-state>
        </div>
        <div class="column is-2">
          <app-text v-model="call.Inventory.vc10Zip" id="inventory.vc10Zip" maxlength="10" tabindex="16" :disabled="!canEditProperty()">
            Zip
          </app-text>
        </div>
        <div class="column is-6 is-left">
          <app-date-time v-model="call.Inventory.dDateOut" id="INV_dDateOut" :disabled="!canEditInventoryInOut" :tabindex="6" @change="setDateOutBy">
            Date Out
          </app-date-time>
        </div>
        <div class="column is-3">
          <app-text v-model="call.Inventory.vc50UserDefined1" id="inventory.vc50UserDefined1" maxlength="50" tabindex="17" :disabled="!canEditProperty()">
            License Number
          </app-text>
        </div>
        <div class="column is-3">
          <app-select-state v-model="call.Inventory.vc50UserDefined2" id="inventory.vc50UserDefined2" tabindex="18" :disabled="!canEditProperty()">
            License State
          </app-select-state>
        </div>
        <div class="column is-6 is-left">
          <app-user-id v-model="call.Inventory.lUserKey_DateOut" id="INV_sDateOutBy" tabindex="7" :disabled="true">
            Date Out By
          </app-user-id>
        </div>
        <div class="column is-6">
          <app-phone v-model="call.Inventory.vc20Phone1" id="inventory.vc20Phone1" maxlength="20" tabindex="19" :disabled="!canEditProperty()">
            Phone
          </app-phone>
        </div>
        <div class="column is-6 is-left">
          <app-text v-model="call.Inventory.vc100UserDefined1" id="INV_vc100UserDefined1" maxlength="100" tabindex="8" :disabled="!canEditProperty()">
            {{ TOPSCOMPANY__settings.vc15Label_Inventory_UserDef1 }}
          </app-text>
        </div>
        <div class="column is-6">
          <app-text v-model="call.Inventory.lBarCode" id="INV_lBarCode" tabindex="20" :disabled="!canEditProperty()">
            Bar Code
          </app-text>
        </div>
        <div class="column is-12 is-left">
          <app-date-time v-model="call.Inventory.dLastVerified" id="INV_dLastVerified" :tabindex="21" :disabled="!canEditProperty()">
            Last Verified
          </app-date-time>
        </div>
        <div class="column is-12 is-left is-bottom">
          <app-textarea v-model="call.Inventory.vc255Notes" id="INV_vc255Notes" maxlength="255" tabindex="9" :disabled="!canEditProperty()">
            Notes
          </app-textarea>
        </div>
      </div> <!-- /columns -->
    </app-grid-form>
  </span> <!-- /inventory-section -->
</template>

<script>import { get } from 'lodash-es';


import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import cardSwipe from '../features/CardSwipe.vue';
import { mapGetters, mapActions } from 'vuex';
import { CALL_SECTION_INVENTORY } from '@/config.js';

export default {
  name: 'inventory-section',

  extends: BaseSection,

  components: {
    cardSwipe
  },

  data () {
    return {
      lots: [],
      dispositions: [],
      sectionName: CALL_SECTION_INVENTORY
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    canEditInventoryInOut () {
      return Access.has('calls.editInventoryInOut');
    }
  },

  methods: {
    ...mapActions([
      'TOPSCOMPANY__getLots',
      'CALL__getFinalDispositions'
    ]),

    setDateOutBy () {
      if (!!this.call.Inventory.dDateOut && !this.call.Inventory.lUserKey_DateOut) {
        this.call.Inventory.lUserKey_DateOut = this.__state.user.Key;
      }
    },

    processSwipe (swipe) {
      this.call.Inventory.vc30Name1 = get(swipe, 'fullName', '');
      this.call.Inventory.vc30Address1 = get(swipe, 'address1', '');
      this.call.Inventory.vc30City = get(swipe, 'city', '');
      this.call.Inventory.ch2StateKey = get(swipe, 'state', '');
      this.call.Inventory.vc10Zip = get(swipe, 'zip', '');
      this.call.Inventory.vc20Phone1 = get(swipe, 'phone', '');
    }
  },

  mounted () {
    this.TOPSCOMPANY__getLots({
      callback: response => {
        this.lots = response;
      }
    });

    this.CALL__getFinalDispositions({
      callback: response => {
        this.dispositions = response;
      }
    });
  }
};
</script>
