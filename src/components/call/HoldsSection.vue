<template>
  <div id="holds-section">
    <app-accordian v-for="(hold, index) in holdsProxy" :expand-on-mount="hold.expandOnMount"
      :key="hold.temporaryHoldKey || hold.lHoldKey">
      <div class="_thumbnail">
        <app-data-point label="Status">{{ hold.sHoldOn }}</app-data-point>
        <app-data-point label="Reason">{{ getReasonValue('Value', hold.lHoldReasonTypeKey) }}</app-data-point>
        <app-data-point label="Hold Until">{{ hold.dHoldUntil }}</app-data-point>
      </div>

      <template slot="controls">
        <app-button @click="release(index)" :disabled="!canReleaseHold(hold)" tabindex="-1">
          Release
        </app-button>
        <app-button @click="remove(index)" :disabled="!canDeleteHold(hold)" tabindex="-1">
          <i class="far fa-trash-alt"></i>
        </app-button>
      </template>

      <template slot="body">
        <app-grid-form context="inline">
          <div class="columns is-multiline">
            <div class="column is-2">
              <label>Status</label>
              <span class="select">
                <select v-model="hold.bHoldOn" disabled>
                  <option :value="true">On</option>
                  <option :value="false">Off</option>
                </select>
              </span>
            </div>
            <div class="column is-4">
              <app-date-time v-model="hold.dHoldUntil" :disabled="!canEditHoldUntil">
                Hold Until
              </app-date-time>
            </div>
            <div class="column is-6">
              <app-select id="CAL_lReasonTypeKey" v-model="hold.lHoldReasonTypeKey" :options="reasons" keyAlias="Key"
                valueAlias="Value" shortCodeAlias="ShortCode" :disabled="!canEditProperty()">
                Reason
              </app-select>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="hold.vc255Notes" maxlength="255" :disabled="!canEditProperty()">
                Notes
              </app-textarea>
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="hold.dAuthorizedOn" :disabled="!canEditProperty()">
                Authorized Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <label>Authorized By</label>
              <input v-model="hold.vc50AuthorizedBy" class="input" maxlength="255" autocomplete="off"
                :disabled="!canEditProperty()">
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="hold.dReleased" @change="fillReleasedBy(index)"
                :disabled="!canEditReleasedDate(hold)">
                Released Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <label>Released By</label>
              <input v-model="hold.sReleasedBy" class="input" disabled autocomplete="off">
            </div>
            <div class="column is-6 is-left">
              <app-date-time v-model="hold.dCreated" :disabled="true">
                Created Date
              </app-date-time>
            </div>
            <div class="column is-6">
              <label>Created By</label>
              <input v-model="hold.sCreatedBy" class="input" disabled autocomplete="off">
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="hold.vc255OtherInfo" maxlength="255" :disabled="!canEditProperty()">
                Other Hold Info
              </app-textarea>
            </div>
            <div class="column is-12 is-left">
              <app-textarea v-model="hold.vc50UserDefined1" maxlength="255" :disabled="!canEditProperty()">
                {{ TOPSCOMPANY__settings.vc15Label_Hold_UserDef1 }}
              </app-textarea>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>

    <section class="_controls">
      <app-button @click="add" id="hold.add" :disabled="!canEditProperty()">
        Add Hold
      </app-button>
    </section>
  </div>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from './BaseSection.vue';
import { mapGetters, mapActions } from 'vuex';
import { CALL_SECTION_HOLDS } from '@/config.js';
import { get, has, uniqueId, concat, find } from 'lodash-es';

export default {
  // NOTE:
  // When importing this component, the context may also require
  // the following dependency: mixins/holds_mixin

  name: CALL_SECTION_HOLDS,

  extends: BaseSection,

  data() {
    return {
      sectionName: CALL_SECTION_HOLDS,

      reasons: []
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      'TOPSCOMPANY__settings'
    ]),

    holdsProxy: {
      get() {
        return get(this.call, 'Holds', []);
      },
      set(value) {
        this.$set(this.call, 'Holds', value);

        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },

    canEditHoldUntil() {
      return Access.has('calls.editHoldUntil');
    }
  },

  methods: {
    ...mapActions([
      'HOLD__getReasons'
    ]),

    canDeleteHold(hold) {
      if (has(hold, 'temporaryHoldKey')) return true;

      return Access.has('calls.deleteHold');
    },

    canReleaseHold(hold) {
      return Access.has('calls.releaseHold') && !hold.dReleased;
    },

    canEditReleasedDate(hold) {
      /**
       * This is confusing. The idea is to keep it disabled by default
       * which forces the user to tap the release button. Then, they
       * can change the value if necessary.
       */
      return this.canEditProperty() && hold.dReleased;
    },

    async add() {
      this.createIfMissing('Holds', []);

      let hold = {
        temporaryHoldKey: uniqueId(),
        lHoldKey: '',
        lCallKey: this.call.lCallKey,
        bHoldOn: true,
        dHoldUntil: '',
        vc255Notes: '',
        lHoldReasonTypeKey: '',
        vc50AuthorizedBy: '',
        dReleased: '',
        sReleasedBy: '',
        lReleasedBy: '',
        dCreated: await this.getNow(),
        sCreatedBy: this.__state.user.UserID,
        lCreatedBy: this.__state.user.Key,
        vc255OtherInfo: '',
        bActive: true,
        tOrder: this.holdsProxy.length + 1,
        dAuthorizedOn: '',
        vc50UserDefined1: '',
        expandOnMount: true
      };

      this.$set(this.call, 'Holds', concat(this.holdsProxy, hold));
      this.$forceUpdate();
    },

    async release(index) {
      this.holdsProxy[index].bHoldOn = false;
      this.holdsProxy[index].sHoldOn = 'Off';
      this.holdsProxy[index].sReleasedBy = this.__state.user.UserID;
      this.holdsProxy[index].lReleasedBy = this.__state.user.Key;
      this.holdsProxy[index].dReleased = await this.getNow();

      this.$forceUpdate();
    },

    remove(index) {
      this.holdsProxy.splice(index, 1);
      this.$forceUpdate();
    },

    fillReleasedBy(index) {
      this.$set(this.holdsProxy[index], 'sReleasedBy', this.__state.user.UserID);
      this.$set(this.holdsProxy[index], 'lReleasedBy', this.__state.user.Key);
    },

    getReasonValue(property, id) {
      let reason = find(this.reasons, ['Key', Number(id)]);

      return get(reason, property, '');
    }
  },

  mounted() {
    this.HOLD__getReasons({
      callback: response => {
        this.reasons = response;
      }
    });
  }
};
</script>
