<template>
  <span id="system-tags-section">
    <SystemTagList
      v-model="call.vc255DelimitedInfo"
      :can-edit-system-tags="canEditSystemTags" />
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import BaseSection from '@/components/call/BaseSection.vue';
import SystemTagList from './SystemTagList.vue';
import { CALL_SECTION_SYSTEM_TAGS } from '@/config.js';

export default {
  name: CALL_SECTION_SYSTEM_TAGS,

  extends: BaseSection,

  components: {
    SystemTagList
  },

  data () {
    return {
      sectionName: CALL_SECTION_SYSTEM_TAGS
    };
  },

  computed: {
    canEditSystemTags () {
      return Access.has('systemTags.edit') && this.canEditProperty();
    }
  }
};
</script>
