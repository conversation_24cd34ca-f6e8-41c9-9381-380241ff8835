<template>
  <app-grid-form context="inline">
    <SystemTagItem
      v-for="(tag, index) in internalTags"
      :tag="tag"
      :index="index"
      :key="`${tag.key}-${index}`"
      :available-tags="getAvailableTagsForItem(index)"
      :can-edit-system-tags="canEditSystemTags"
      :is-newly-added="newlyAddedTags.includes(tag.key)"
      @remove-tag="removeTag"
      @update-tag="updateTag" />

    <button class="_add" @click="addTag" :disabled="!canAddTag">
      <i class="fas fa-plus"></i> Add
    </button>
  </app-grid-form>
</template>

<script>
// v-model compatibility for Vue 2.7
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  }
};
</script>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import useSystemTag from './useSystemTag.js';
import Access from '@/utils/access.js';
import SystemTagItem from './SystemTagItem.vue';
import { AFTER_CALL_READ } from '@/config.js';

const props = defineProps({
  modelValue: { type: String, default: '' },
  canEditSystemTags: { type: Boolean, default: false }
});
const emit = defineEmits(['update:modelValue']);

const internalTags = ref([]);
const availableSystemTags = ref([]);
const newlyAddedTags = ref([]);

const canAddTag = computed(() => {
  if (!props.canEditSystemTags) return false;

  const usedKeys = internalTags.value.map(tag => tag.key);
  const hasUnusedTags = availableSystemTags.value.some(tag => !usedKeys.includes(tag.ShortCode));

  return hasUnusedTags;
});

const getAvailableTagsForItem = (currentIndex) => {
  const usedKeys = internalTags.value
    .map((tag, index) => index !== currentIndex ? tag.key : null)
    .filter(Boolean);

  return availableSystemTags.value.filter(tag => !usedKeys.includes(tag.ShortCode));
};

const parseSystemTags = (value = '') =>
  value.length >= 3
    ? value
      .split(';')
      .filter(Boolean)
      .map(pair => {
        const [key = '', ...rest] = pair.split('=');
        return { key, value: rest.join('=') };
      })
    : [];

const flattenSystemTags = tags =>
  tags
    .filter(({ key, value }) => key || value)
    .map(({ key = '', value = '' }) => `${key}=${value}`)
    .join(';');

watch(
  () => parseSystemTags(props.modelValue),
  tags => {
    internalTags.value = tags;
  },
  { immediate: true }
);

watch(
  internalTags,
  tags => {
    const flattened = flattenSystemTags(tags);
    if (flattened !== props.modelValue) {
      emit('update:modelValue', flattened);
    }
  },
  { deep: true }
);

const addTag = () => {
  // Find the first unused tag from available system tags
  const usedKeys = internalTags.value.map(tag => tag.key);
  const unusedTag = availableSystemTags.value.find(tag => !usedKeys.includes(tag.ShortCode));
  const defaultKey = unusedTag ? unusedTag.ShortCode : '';

  if (defaultKey && !newlyAddedTags.value.includes(defaultKey)) {
    newlyAddedTags.value.push(defaultKey);
  }

  internalTags.value.push({
    key: defaultKey,
    value: ''
  });
};
const removeTag = index => {
  const removedTag = internalTags.value[index];
  if (removedTag && removedTag.key) {
    newlyAddedTags.value = newlyAddedTags.value.filter(key => key !== removedTag.key);
  }
  internalTags.value.splice(index, 1);
};
const updateTag = (index, tag) => internalTags.value.splice(index, 1, tag);

const systemTagComposable = useSystemTag();
const getAvailableSystemTags = async () => {
  availableSystemTags.value = await systemTagComposable.getAvailableSystemTags();
};

const handleAfterCallRead = () => {
  newlyAddedTags.value = [];
};

onMounted(() => {
  getAvailableSystemTags();
  document.addEventListener(AFTER_CALL_READ, handleAfterCallRead);
});

onBeforeUnmount(() => {
  document.removeEventListener(AFTER_CALL_READ, handleAfterCallRead);
});
</script>

<style scoped>
._add {
  position: absolute;
  right: 0.5rlh;
  top: -1.7rlh;

  padding: 0.25rem 0.75rem;
  font-size: var(--font-size-small1);
  font-weight: bold;
  appearance: none;
  color: var(--white);
  background-color: color-mix(in oklch, var(--white), transparent 80%);
  border: 0;
  border-radius: 0.2rem;

  &:hover {
    background-color: color-mix(in oklch, var(--white), transparent 70%);
  }

  &:disabled {
    opacity: 0.5;
  }
}
</style>
