<template>
  <span id="notes-section">
    <app-condensed-form context="inline">
      <template v-if="notesProxy.length > 0">

        <NoteItem v-for="(note, index) in notesProxy" :note="note" :index="index" :key="index + call.dLastModified"
          @on-remove="removeNote" />

      </template>

      <div class="compose">
        <input class="_input input" id="note.add" v-model="pendingNote" :disabled="!canAdd" @keyup.enter="addNote"
          maxlength="255" autocomplete="off" placeholder="Compose a note..." />
        <app-button class="_add" @click="addNote" size="normal">Add</app-button>
      </div>
    </app-condensed-form>
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import NoteItem from './NoteItem.vue';
import BaseSection from '../BaseSection.vue';
import { CALL_SECTION_NOTES } from '@/config.js';
import { get, isEmpty, without } from 'lodash-es';

/**
 * If a user has function key=1234, they can Read Notes.
 * If they have function key=6, they can Edit Calls.
 * If they have both, they can add notes to a call,
 * and they can edit or delete any notes they have added.
 */

export default {
  name: 'notes-section',

  extends: BaseSection,

  components: {
    NoteItem
  },

  data() {
    return {
      sectionName: CALL_SECTION_NOTES,

      pendingNote: ''
    };
  },

  computed: {
    notesProxy: {
      get() {
        return get(this.call, 'Notes', []);
      },
      set(value) {
        this.$set(this.call, 'Notes', value);
      }
    },

    canAdd() {
      if (this.isCallConfirmed) {
        return Access.has('calls.editNotesAfterConfirmed');
      }

      return Access.has('calls.edit');
    }
  },

  methods: {
    addNote() {
      if (isEmpty(this.pendingNote)) return;

      this.createIfMissing('Notes', []);
      this.notesProxy.push({ vc255Note: this.pendingNote });
      this.pendingNote = '';

      this.$nextTick(() => {
        document.getElementById('note.add').focus();
      });
    },

    removeNote(index) {
      let targetNote = this.notesProxy[index];
      let trimmedNotes = without(this.notesProxy, targetNote);

      this.notesProxy = trimmedNotes;
    }
  }
};
</script>
