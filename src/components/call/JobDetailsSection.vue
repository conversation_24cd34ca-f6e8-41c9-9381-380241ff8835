<template>
  <div id="job-details-section" v-if="call && call.lJobKey">
    <JobDetails :job-key="call.lJobKey" />
  </div>
</template>

<script>
import BaseSection from './BaseSection.vue';
import JobDetails from '@/tower/dispatch/JobDetails.vue';
import { CALL_SECTION_JOB_DETAILS } from '@/config';

export default {
  name: 'job-details-section',
  extends: BaseSection,
  components: { JobDetails },
  data() {
    return {
      sectionName: CALL_SECTION_JOB_DETAILS
    };
  }
};
</script>
