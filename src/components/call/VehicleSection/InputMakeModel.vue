<template>
  <app-shortcode
    id="CAL_MakeModel"
    v-model="valueProxy"
    :options="makeModelsOptions"
    keyAlias="key"
    valueAlias="description"
    shortCodeAlias="shortCode"
    :disabled="disabled">
    Make / Model
  </app-shortcode>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: 'vehicle-make-model-control',

  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },

  computed: {
    ...mapState('vehicle', ['isLoading', 'error']),
    ...mapGetters('vehicle', ['makeModels']),

    makeModelsOptions() {
      return this.makeModels || [];
    },

    valueProxy: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);

        // Parse the combined key and emit separate make/model selection
        if (value) {
          const [makeKey, modelKey] = value.split('-').map(Number);
          this.$emit('select', { makeKey, modelKey });
        } else {
          this.$emit('select', { makeKey: null, modelKey: null });
        }
      }
    }
  },

  mounted() {
    this.VEHICLE__hydrate().catch(() => {
      // Silently ignore errors - they will be handled by the store
    });
  },

  methods: {
    ...mapActions('vehicle', ['VEHICLE__hydrate'])
  }
};
</script>
