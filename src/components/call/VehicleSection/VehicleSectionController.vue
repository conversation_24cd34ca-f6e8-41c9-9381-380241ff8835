<template>
  <span class="vehicle-section">
    <app-grid-form context="inline">
      <div class="columns is-multiline">
        <div class="column is-8">
          <label>VIN</label>
          <div class="field has-addons">
            <p class="control">
              <input v-model="call.vc25VIN" v-upper="call.vc25VIN" type="text" class="input" id="CAL_vc25VIN"
                autocomplete="off" maxlength="17" :disabled="!canEditProperty()">
            </p>
            <p class="control width-auto">
              <button @click.prevent="decodeVin()" class="button" tabindex="-1" :disabled="!canDecodeVin"
                title="Fill year, make, model and body based on VIN.">
                <i class="far fa-wand-magic-sparkles"></i>
              </button>
            </p>
          </div>
        </div>
        <div class="column is-4">
          <template v-if="!quoteMode">
            <label>Number of Passengers</label>
            <input v-model="call.bOwnerWithVehicle" type="number" class="input" min="0" max="9"
              id="CAL_bOwnerWithVehicle" autocomplete="off" :disabled="!canEditProperty()">
          </template>
        </div>
        <div class="column is-left is-4">
          <label>Year</label>
          <input v-model="call.iYear" class="input" maxlength="4" id="CAL_iYear" autocomplete="off"
            :disabled="!canEditProperty()">
        </div>
        <div class="column is-4">
          <app-shortcode id="CAL_lMakeKey" v-model="call.lMakeKey" :options="makes" keyAlias="value"
            valueAlias="description" shortCodeAlias="shortCode" @change="onInputMakeModelChange"
            :disabled="!canEditProperty()">
            Make
          </app-shortcode>
          <!-- <InputMakeModel v-model="call.lMakeKey" :disabled="!canEditProperty()" /> -->
        </div>
        <div class="column is-4">
          <app-shortcode id="CAL_lModelKey" v-model="call.lModelKey" :options="models" :disabled="!canEditProperty()"
            :field-target="true">
            Model
          </app-shortcode>
        </div>
        <div class="column is-left is-4">
          <app-shortcode id="CAL_lBodyTypeKey" v-model="call.lBodyTypeKey" :options="bodyTypes" keyAlias="value"
            valueAlias="description" shortCodeAlias="shortCode" :disabled="!canEditProperty()">
            Body
          </app-shortcode>
        </div>
        <div class="column is-4">
          <app-shortcode id="CAL_lColorTypeKey" v-model="call.lColorTypeKey" :options="colors" keyAlias="value"
            valueAlias="description" shortCodeAlias="shortCode" :disabled="!canEditProperty()">
            Color
          </app-shortcode>
        </div>
        <div class="column is-4" v-if="!quoteMode">
          <label>Odometer</label>
          <input v-model="call.vc10Odometer" class="input" maxlength="100" id="CAL_vc10Odometer" autocomplete="off"
            :disabled="!canEditProperty()">
        </div>
        <div class="column is-left is-4" v-if="!quoteMode">
          <label>Tag Number</label>
          <div class="field has-addons">
            <p class="control">
              <input v-model="call.vc15LicenseNum" class="input" maxlength="15" id="CAL_vc15LicenseNum"
                autocomplete="off" :disabled="!canEditProperty()">
            </p>
            <p class="control width-auto">
              <button @click.prevent="fillVinFromTag" class="button" tabindex="-1" :disabled="!canFillVinFromTag"
                title="Fill VIN based on tag number and State.">
                <i class="far fa-wand-magic-sparkles"></i>
              </button>
            </p>
          </div>
        </div>
        <div class="column is-4" v-if="!quoteMode">
          <app-select-state v-model="call.ch2StateKey" id="CAL_ch2StateKey" :disabled="!canEditProperty()">
            Tag State
          </app-select-state>
        </div>
        <div class="column is-4" v-if="!quoteMode">
          <app-date-time v-model="call.dTagExpiry" id="other.dTagExpiry" :disabled="!canEditProperty()">
            Tag Expiry
          </app-date-time>
        </div>
        <!-- <hr> -->
        <div class="column is-4 is-bottom" v-if="!quoteMode">
          <label>Owner Name</label>
          <input v-model="call.vc30OwnerName" class="input" maxlength="30" id="CAL_vc30OwnerName" autocomplete="off"
            :disabled="!canEditProperty()">
        </div>
        <div class="column is-4 is-bottom" v-if="!quoteMode">
          <label>Owner Phone</label>
          <input v-model="call.vc20OwnerPhone" class="input" maxlength="20" id="CAL_vc20OwnerPhone" autocomplete="off"
            :disabled="!canEditProperty()">
        </div>
        <div class="column is-4 is-bottom">
          <label>Other Information</label>
          <input v-model="call.vc30ExtraVehicleInfo" class="input" maxlength="30" id="CAL_vc30ExtraVehicleInfo"
            autocomplete="off" :disabled="!canEditProperty()">
        </div>
      </div>
    </app-grid-form>
  </span>
</template>

<script>
import Access from '@/utils/access.js';
import { mapActions } from 'vuex';
import BaseSection from '@/components/call/BaseSection.vue';
import { CALL_SECTION_VEHICLE } from '@/config.js';
// import InputMakeModel from '@/components/call/VehicleSection/InputMakeModel.vue';
import { map } from 'lodash-es';

export default {
  name: 'vehicle-section',

  extends: BaseSection,

  components: {
    // InputMakeModel
  },

  data() {
    return {
      sectionName: CALL_SECTION_VEHICLE,

      makes: [],
      colors: [],
      models: [],
      bodyTypes: []
    };
  },

  computed: {
    canDecodeVin() {
      return Access.has('trucks.decodeVin');
    },

    canFillVinFromTag() {
      return this.canDecodeVin &&
        this.call.vc15LicenseNum &&
        this.call.ch2StateKey;
    }
  },

  watch: {
    'call.lMakeKey': {
      immediate: true,
      handler() {
        this.getModels();
      }
    }
  },

  methods: {
    ...mapActions([
      'CALL__getModels',
      'TOPSCALL__decodeVIN',
      'TOPSCALL__tagToVIN',
      'CALL__getFieldOptions'
    ]),

    decodeVin() {
      this.TOPSCALL__decodeVIN({
        vin: this.call.vc25VIN,
        callback: response => {
          this.$set(this.call, 'iYear', response.Year);
          this.$set(this.call, 'lMakeKey', response.MakeKey);
          this.$set(this.call, 'lModelKey', response.ModelKey);
          this.$set(this.call, 'lBodyTypeKey', response.BodyKey);

          this.getModels();
        }
      });
    },

    getFieldOptions(verb, prop, lMakeKey = '') {
      this.CALL__getFieldOptions({
        verb: verb,
        makeKey: lMakeKey,
        callback: response => {
          this.$data[prop] = response.map(option => ({
            value: option[0],
            description: option[1],
            shortCode: option[2]
          }));
        }
      });
    },

    fillVinFromTag() {
      if (!this.canFillVinFromTag) { return; }

      this.TOPSCALL__tagToVIN({
        state: this.call.ch2StateKey,
        tag: this.call.vc15LicenseNum,
        success: response => {
          this.call.vc25VIN = response.VIN;
        }
      });
    },

    onInputMakeModelChange() {
      this.$set(this.call, 'lModelKey', '');
    },

    getModels() {
      if (!this.call.lMakeKey) return;

      this.CALL__getModels({
        makeKey: this.call.lMakeKey,
        callback: response => {
          this.models = response;
        }
      });
    }
  },

  mounted() {
    this.getFieldOptions('GetMakes', 'makes');
    this.getFieldOptions('GetBodyTypes', 'bodyTypes');
    this.getFieldOptions('GetColors', 'colors');
  }
};
</script>
