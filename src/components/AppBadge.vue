<template>
  <span
    class="app-badge"
    :class="[
      `app-badge--${variant}`,
      `app-badge--${size}`,
      { 'app-badge--dot': isDot }
    ]"
    :style="customColorStyle">
    {{ displayText }}
  </span>
</template>

<script>
import { computed, defineComponent } from 'vue'

export default defineComponent({
  name: 'AppBadge',

  props: {
    count: {
      type: [Number, String],
      default: 0
    },
    max: {
      type: Number,
      default: 99
    },
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    size: {
      type: String,
      default: 'default',
      validator: (value) => ['small', 'default', 'large'].includes(value)
    },
    color: {
      type: String,
      default: null
    },
    dot: {
      type: Boolean,
      default: false
    },
    showZero: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    const isDot = computed(() => {
      return props.dot || props.count === ''
    })

    const displayText = computed(() => {
      if (props.dot || props.count === '') return ''

      const num = Number(props.count)
      if (isNaN(num)) return String(props.count)

      return num > props.max ? `${props.max}+` : String(num)
    })

    const customColorStyle = computed(() => {
      return props.color ? { backgroundColor: props.color } : null
    })

    return {
      isDot,
      displayText,
      customColorStyle
    }
  }
})
</script>

<style scoped>
.app-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  min-inline-size: 1.25rem;
  block-size: 1.25rem;
  padding: 0 0.375rem;
  border-radius: 50rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  white-space: nowrap;
  vertical-align: baseline;
  text-align: center;
}

.app-badge--primary {
  --color: var(--pure-blue);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--secondary {
  --color: var(--blue);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--success {
  --color: var(--pure-green);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--warning {
  --color: var(--pure-orange);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--danger {
  --color: var(--pure-red);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--info {
  --color: var(--pure-blue);
  color: var(--color);
  background-color: color-mix(in oklch, var(--color), transparent 80%);
}

.app-badge--small {
  min-inline-size: 1rem;
  block-size: 1rem;
  padding: 0 0.25rem;
  font-size: 0.625rem;
}

.app-badge--large {
  min-inline-size: 1.5rem;
  block-size: 1.5rem;
  padding: 0 0.5rem;
  font-size: 0.875rem;
}

.app-badge--dot {
  min-inline-size: 0.5rem;
  inline-size: 0.5rem;
  block-size: 0.5rem;
  background-color: var(--color);
  padding: 0;
  border-radius: 50%;
}

.app-badge--dot.app-badge--small {
  inline-size: 0.375rem;
  block-size: 0.375rem;
}

.app-badge--dot.app-badge--large {
  inline-size: 0.625rem;
  block-size: 0.625rem;
}
</style>
