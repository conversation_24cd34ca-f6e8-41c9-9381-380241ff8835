<template>
  <div class="pinia-example">
    <h2>Pinia Store Example</h2>
    
    <!-- Counter Section -->
    <div class="section">
      <h3>Counter: {{ counter }}</h3>
      <p>Double Count: {{ doubleCount }}</p>
      <el-button @click="increment" type="primary">+</el-button>
      <el-button @click="decrement" type="default">-</el-button>
    </div>

    <!-- Name Section -->
    <div class="section">
      <h3>{{ greeting }}</h3>
      <el-input 
        v-model="newName" 
        placeholder="Enter new name"
        style="width: 200px; margin-right: 10px;"
      />
      <el-button @click="updateName" type="success">Update Name</el-button>
      <el-button @click="fetchUser" :loading="loading" type="info">Fetch User</el-button>
    </div>

    <!-- Todos Section -->
    <div class="section">
      <h3>Todos</h3>
      <div style="margin-bottom: 10px;">
        <el-input 
          v-model="newTodo" 
          placeholder="Add new todo"
          style="width: 200px; margin-right: 10px;"
          @keyup.enter="addNewTodo"
        />
        <el-button @click="addNewTodo" type="primary">Add Todo</el-button>
      </div>
      
      <div class="todo-list">
        <div v-for="todo in todos" :key="todo.id" class="todo-item">
          <el-checkbox 
            :value="todo.completed" 
            @change="toggleTodo(todo.id)"
          >
            {{ todo.text }}
          </el-checkbox>
          <el-button 
            @click="removeTodo(todo.id)" 
            type="danger" 
            size="mini"
            style="margin-left: 10px;"
          >
            Remove
          </el-button>
        </div>
      </div>
      
      <div class="todo-stats">
        <p>Active: {{ activeTodos.length }} | Completed: {{ completedTodos.length }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { useExampleStore } from '@/stores/example'
import { storeToRefs } from 'pinia'

export default {
  name: 'PiniaExample',
  
  data() {
    return {
      newName: '',
      newTodo: '',
      loading: false
    }
  },
  
  computed: {
    store() {
      return useExampleStore()
    },
    
    // Use storeToRefs to make state reactive
    ...storeToRefs(useExampleStore()),
    
    // Or access individual pieces
    counter() {
      return this.store.counter
    },
    
    doubleCount() {
      return this.store.doubleCount
    },
    
    greeting() {
      return this.store.greeting
    },
    
    todos() {
      return this.store.todos
    },
    
    activeTodos() {
      return this.store.activeTodos
    },
    
    completedTodos() {
      return this.store.completedTodos
    }
  },
  
  methods: {
    increment() {
      this.store.increment()
    },
    
    decrement() {
      this.store.decrement()
    },
    
    updateName() {
      if (this.newName.trim()) {
        this.store.setName(this.newName)
        this.newName = ''
      }
    },
    
    async fetchUser() {
      this.loading = true
      await this.store.fetchUserData()
      this.loading = false
    },
    
    addNewTodo() {
      if (this.newTodo.trim()) {
        this.store.addTodo(this.newTodo)
        this.newTodo = ''
      }
    },
    
    toggleTodo(id) {
      this.store.toggleTodo(id)
    },
    
    removeTodo(id) {
      this.store.removeTodo(id)
    }
  }
}
</script>

<style scoped>
.pinia-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.todo-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.todo-stats {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}
</style>
