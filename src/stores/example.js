import { defineStore } from 'pinia'

export const useExampleStore = defineStore('example', {
  state: () => ({
    counter: 0,
    name: '<PERSON>',
    todos: [
      { id: 1, text: 'Learn Pinia', completed: false },
      { id: 2, text: 'Build something awesome', completed: false }
    ]
  }),

  getters: {
    doubleCount: (state) => state.counter * 2,
    completedTodos: (state) => state.todos.filter(todo => todo.completed),
    activeTodos: (state) => state.todos.filter(todo => !todo.completed),
    greeting: (state) => `Hello, ${state.name}!`
  },

  actions: {
    increment() {
      this.counter++
    },
    
    decrement() {
      this.counter--
    },
    
    setName(newName) {
      this.name = newName
    },
    
    addTodo(text) {
      const id = Math.max(...this.todos.map(t => t.id), 0) + 1
      this.todos.push({ id, text, completed: false })
    },
    
    toggleTodo(id) {
      const todo = this.todos.find(t => t.id === id)
      if (todo) {
        todo.completed = !todo.completed
      }
    },
    
    removeTodo(id) {
      const index = this.todos.findIndex(t => t.id === id)
      if (index > -1) {
        this.todos.splice(index, 1)
      }
    },
    
    async fetchUserData() {
      // Simulate API call
      return new Promise((resolve) => {
        setTimeout(() => {
          this.name = 'Fetched User'
          resolve()
        }, 1000)
      })
    }
  }
})
