<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>

import { includes, find, toUpper } from 'lodash-es';
import projectPackage from '../package.json';
import { mapGetters, mapActions } from 'vuex';
import tops from '@/utils/tops';

import {
  VERSION,
  PRODUCT,
  ASSET_PATH,
  EVENT_INFO,
  EVENT_ERROR,
  EVENT_WARNING,
  EVENT_SUCCESS,
  EVENT_LOG_OUT,
  EVENT_GET_VIEWS,
  EVENT_LOGGED_OUT,
  EVENT_ROLE_CHANGED,
  EVENT_USER_CHANGED,
  PRODUCTKEY_WEB_PORTAL,
  EVENT_PRODUCT_CHANGED,
  EVENT_PRODUCTS_CHANGED,
  EVENT_INSTANCE_CHANGED,
  EVENT_ORG_UNIT_CHANGED,
  EVENT_TOGGLE_NAVIGATION
} from './config';

export default {
  name: 'app',

  metaInfo () {
    const head = {
      title: this.__orgUnitName ? `TOPS ∙ ${this.__orgUnitName}` : 'TOPS',
      base: { target: '_blank', href: import.meta.env.VITE_BASE },
      meta: [
        // Leave this one in index.html since it creates an encoding problem when built.
        // { charset: 'utf-8' },
        { 'http-equiv': 'X-UA-Compatible', content: 'IE=edge' },
        { 'http-equiv': 'Cache-Control', content: 'no-cache, no-store, must-revalidate' },
        { 'http-equiv': 'Pragma', content: 'no-cache' },
        { 'http-equiv': 'Expires', content: '-1' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: projectPackage.description },
        { name: 'application-name', content: toUpper(projectPackage.name) },

        { name: 'msapplication-TileColor', content: '#252324' },
        { name: 'msapplication-TileImage', content: `${ASSET_PATH}/favicons/mstile-144x144.png` },
        { name: 'msapplication-square70x70logo', content: `${ASSET_PATH}/favicons/mstile-70x70.png` },
        { name: 'msapplication-square150x150logo', content: `${ASSET_PATH}/favicons/mstile-150x150.png` },
        { name: 'msapplication-wide310x150logo', content: `${ASSET_PATH}/favicons/mstile-310x150.png` },
        { name: 'msapplication-square310x310logo', content: `${ASSET_PATH}/favicons/mstile-310x310.png` },
        { name: 'mobile-web-app-capable', content: 'yes' },

        { name: 'theme-color', content: '#3F6A97', media: '(prefers-color-scheme: light)' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: `${ASSET_PATH}/favicons/favicon.ico` },
        { rel: 'apple-touch-icon-precomposed', sizes: '57x57', href: `${ASSET_PATH}/favicons/apple-touch-icon-57x57.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '114x114', href: `${ASSET_PATH}/favicons/apple-touch-icon-114x114.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '72x72', href: `${ASSET_PATH}/favicons/apple-touch-icon-72x72.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '144x144', href: `${ASSET_PATH}/favicons/apple-touch-icon-144x144.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '60x60', href: `${ASSET_PATH}/favicons/apple-touch-icon-60x60.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '120x120', href: `${ASSET_PATH}/favicons/apple-touch-icon-120x120.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '76x76', href: `${ASSET_PATH}/favicons/apple-touch-icon-76x76.png` },
        { rel: 'apple-touch-icon-precomposed', sizes: '152x152', href: `${ASSET_PATH}/favicons/apple-touch-icon-152x152.png` },
        { rel: 'icon', type: 'image/png', sizes: '196x196', href: `${ASSET_PATH}/favicons/favicon-196x196.png` },
        { rel: 'icon', type: 'image/png', sizes: '96x96', href: `${ASSET_PATH}/favicons/favicon-96x96.png` },
        { rel: 'icon', type: 'image/png', sizes: '32x32', href: `${ASSET_PATH}/favicons/favicon-32x32.png` },
        { rel: 'icon', type: 'image/png', sizes: '16x16', href: `${ASSET_PATH}/favicons/favicon-16x16.png` },
        { rel: 'icon', type: 'image/png', sizes: '128x128', href: `${ASSET_PATH}/favicons/favicon-128.png` },

        { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/element-ui/1.3.5/theme-default/index.css' },

        { rel: 'stylesheet', href: `${ASSET_PATH}/fontawesome/css/all.min.css` }
      ],
      script: [
        { src: 'https://kit.fontawesome.com/7f62c8697c.js', async: true },
        { src: 'https://js.stripe.com/v3/' },
        { src: '//fast.appcues.com/56492.js' }
      ]
    };

    if (import.meta.env.VITE_API_MODE === 'Production') {
      head.script.push({ src: `${ASSET_PATH}/clarity.js`, async: true });
    }

    return head;
  },

  components: {},

  data () {
    return {
      duration: 400,
      version: VERSION,
      product: PRODUCT,
      easing: 'easeInOutCirc',
      intervalHeartbeat: null
    };
  },

  computed: {
    ...mapGetters([
      '__state',
      '__currentView',
      '__orgUnitName',
      'USER__products',
      'INSTANCE__isValid'
    ]),

    canSelectCompany () {
      return this.__state.user.OrgUnits.length > 1;
    }
  },

  mounted () {
    if (!window.name) {
      this.INSTANCE__setWindowName();
    }

    setTimeout(() => {
      window.localStorage.removeItem('entry_point');
    }, 60 * 1000);

    this.$hub.$on(EVENT_INSTANCE_CHANGED, this.onInstanceChanged);

    if (this.__state.instance.Key) {
      this.onInstanceChanged();
    }

    // Handle a change to the User (triggered by sign in/out or change in Product/Org Unit selected)
    this.$hub.$on(EVENT_USER_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_USER_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }
    });

    // Handle a change to the list of available Products for the user.
    // Triggered by sign in/out or change in Product/Org Unit selected.
    this.$hub.$on(EVENT_PRODUCTS_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_PRODUCTS_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }

      if (!this.INSTANCE__isValid) return;

      if (this.USER__products.length < 2) {
        this.showError('User does not have access to any products');
        this.showLogin();
        return;
      }

      // If current Product exists in the list, preserve it.
      if (!!this.__state.product.key &&
        this.__state.product.key !== PRODUCTKEY_WEB_PORTAL &&
        includes(this.USER__products, this.__state.product.key)) {
        return;
      }

      // Select the Product that isn't the Web Portal.
      if (this.USER__products.length === 2) {
        let selectableProduct = find(this.USER__products, product => {
          return product.key !== PRODUCTKEY_WEB_PORTAL;
        });

        this.__selectProduct(selectableProduct.key);
      }
    });

    // Handle a change to the Org Unit (user selected new Org Unit)
    this.$hub.$on(EVENT_ORG_UNIT_CHANGED, () => {
      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_ORG_UNIT_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }

      // When company changes:
      // - Prefetch call inspection items and use to toggle the inspections section visibilty.
      // - Get company settings to assist with route availability.
      if (this.__state.orgUnitKey) {
        this.TOPSCOMPANY__getCallInspectionItems({
          callback: response => {
            this.TOPSCOMPANY__setHasCallInspectionItems(!!response.length);
          }
        });

        this.$nextTick(() => {
          this.TOPSCOMPANY__getSettings({
            callback: response => {
              this.TOPSCOMPANY__setSettings(response);
            }
          });
        });
      }

      this.$store.state.price.services = [];
      this.$store.state.price.customers = [];
      this.$store.state.price.unitTypes = [];
      this.$store.state.price.activeServiceKey = null;
      this.$store.state.price.activeCustomerKey = null;
      
      this.$store.state.payment.selectedPaymentKey = '';
      this.$store.state.payment.selectedCustomerKey = '';
      this.$store.state.payment.selectedSubcompanyKey = '';
      this.$store.state.payment.selectedSubcompany = {};
    });

    this.$hub.$on(EVENT_LOG_OUT, () => {
      this.$router.push({ name: 'SignIn' });
    });

    this.$hub.$on(EVENT_LOGGED_OUT, () => {
      this.$hub.$emit(EVENT_TOGGLE_NAVIGATION, false);
    });

    this.$hub.$on(EVENT_ERROR, message => {
      if (message) {
        this.$notify.error({
          title: 'Error',
          message: message
        });
      }
    });

    this.$hub.$on(EVENT_WARNING, message => {
      this.$notify.warning({
        title: 'Warning',
        message: message
      });
    });

    this.$hub.$on(EVENT_INFO, message => {
      this.$notify.info({
        title: 'Info',
        message: message
      });
    });

    this.$hub.$on(EVENT_SUCCESS, message => {
      this.$notify.success({
        title: 'Success',
        message: message
      });
    });
  },

  beforeDestroy () {
    // Clear heartbeat interval
    if (this.intervalHeartbeat !== null) {
      window.clearInterval(this.intervalHeartbeat);
      this.intervalHeartbeat = null;
    }

    // Clean up event listeners
    this.$hub.$off(EVENT_INSTANCE_CHANGED);
    this.$hub.$off(EVENT_USER_CHANGED);
    this.$hub.$off(EVENT_ROLE_CHANGED);
    this.$hub.$off(EVENT_GET_VIEWS);
  },

  methods: {
    ...mapActions([
      '__selectProduct',
      'INSTANCE__setWindowName',
      'TOPSCOMPANY__getSettings',
      'TOPSCOMPANY__setSettings',
      'TOPSCOMPANY__getCallInspectionItems',
      'TOPSCOMPANY__setHasCallInspectionItems'
    ]),

    onInstanceChanged() {
      if (this.intervalHeartbeat !== null) {
        clearInterval(this.intervalHeartbeat);
      }

      this.intervalHeartbeat = setInterval(() => this.sendHeartbeat(), 120 * 1000);

      if (this.__state.instance.Key === '') {
        this.showLogin();
      }

      if (this.__state.appMode === 'DEBUG') {
        console.log(`%cevent → %c${EVENT_INSTANCE_CHANGED}`, 'font-variant: small-caps; color: #B10DC9', 'font-weight: bold');
      }
    },

    async sendHeartbeat() {
      try {
        await tops.Instance.Heartbeat({ viewName: this.__currentView });
      } catch (error) {
        console.error('Failed to send heartbeat');
      }
    },

    notifications () {
      console.log('notifications');
    },

    messages () {
      console.log('messages');
    },

    showError (message) {
      this.$hub.$emit(EVENT_ERROR, message);
    },

    showLogin () {
      this.$hub.$emit(EVENT_TOGGLE_NAVIGATION, false);
      this.$router.push({ name: 'SignIn' });
    }
  }
};
</script>
