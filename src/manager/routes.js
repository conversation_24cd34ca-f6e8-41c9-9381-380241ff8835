import StandardLayout from '@/layouts/Standard.vue';
import { ACCESS_CODE, PRODUCTKEY_MANAGER } from '@/config';

const managerRoutes = {
  path: '/manager',
  components: { default: StandardLayout },
  meta: { product: PRODUCTKEY_MANAGER },
  children: [
    { 
      path: 'towlien', 
      label: 'TowLien Setup', 
      name: 'TowLien', 
      component: () => import('@/components/administration/TowLien.vue'), 
      meta: { 
        navigatorContext: 'primary', 
        accessRight: ACCESS_CODE.towlien.manage 
      } 
    }
  ]
};

export default managerRoutes;
