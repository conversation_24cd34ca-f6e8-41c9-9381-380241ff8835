/**
 * This filters out 'Vue warn' from `console.error` so that we
 * can focus on real errors. Comment this out if you want to
 * proactively work with Vue warnings.
 */
const originalError = console.error;
console.error = function (...args) {
  if (!args.some(arg => typeof arg === 'string' && arg.includes('Vue warn'))) {
    originalError.apply(console, args);
  }
};

import Vue from 'vue';
import CompositionApi from '@vue/composition-api';
import vuex from 'vuex';
import { createPinia, PiniaVuePlugin } from 'pinia';
import App from './App.vue';

import axios from 'axios';
import Bowser from 'bowser';
import store from './store/index.js';
import Meta from 'vue-meta';
import Device from '@/utils/device.js';
import routes from './router';
import Hub from './events/hub.js';
import VueAxios from 'vue-axios';
import Cookies from 'cookies-js';
import keyCodes from '@/utils/keycodes.js';
import ElementUI from 'element-ui';
import PortalVue from 'portal-vue';
import VueRouter from 'vue-router';
import locale from 'element-ui/lib/locale/lang/en';
import { includes, get, isNil, trim } from 'lodash-es';
Vue.prototype.$hub = Hub;

// Lazy load Sentry in production
if (import.meta.env.PROD && import.meta.env.VITE_API_MODE === 'Production') {
  import('@sentry/browser').then(({ init, Integrations }) => {
    init({
      dsn: 'https://<EMAIL>/1362370',
      integrations: [new Integrations.Vue({ Vue })]
    });
  });
}

// Lazy load GSAP
async function registerGsap(Vue) {
  const { gsap } = await import('gsap');
  const { Flip } = await import('gsap/dist/Flip');
  const { ScrollToPlugin } = await import('gsap/dist/ScrollToPlugin');
  gsap.registerPlugin(Flip, ScrollToPlugin);
  Vue.prototype.$gsap = gsap;
}
registerGsap(Vue);

import dateInput from './components/inputs/Date.vue';
import textInput from './components/inputs/Text.vue';
import timeInput from './components/inputs/Time.vue';
import phoneInput from './components/inputs/Phone.vue';
import yesNoInput from './components/inputs/YesNo.vue';
import numberInput from './components/inputs/Number.vue';
import selectInput from './components/inputs/Select.vue';
import userIdInput from './components/inputs/UserId.vue';
import checkboxInput from './components/inputs/Checkbox.vue';
import dateTimeInput from './components/inputs/DateTime.vue';
import textareaInput from './components/inputs/Textarea.vue';
import cardTypeInput from './components/inputs/CardType.vue';
import employeeInput from './components/inputs/Employee.vue';
import customerInput from './components/inputs/Customer.vue';
import usernameInput from './components/inputs/Username.vue';
import inputTowType from './components/inputs/TowType/Index.vue';
import reasonInput from './components/inputs/Reason.vue';
import cancelReasonInput from './components/inputs/CancelReason.vue';
import shortCodeInput from './components/inputs/ShortCode.vue';
import trueFalseInput from './components/inputs/TrueFalse.vue';
import subcompanyInput from './components/inputs/Subcompany.vue';
import suggestionInput from './components/inputs/Suggestion.vue';
import affirmativeInput from './components/inputs/Affirmative.vue';
import paymentTypeInput from './components/inputs/PaymentType.vue';
import selectStateInput from './components/inputs/SelectState.vue';
import selectSimpleInput from './components/inputs/SelectSimple.vue';
import saleCustomerInput from './components/inputs/SaleCustomer.vue';
import selectReportInput from './components/inputs/SelectReport.vue';
import selectSearchInput from './components/inputs/SelectSearch.vue';
import selectBooleanInput from './components/inputs/SelectBoolean.vue';
import addressSuggestorInput from './components/inputs/AddressSuggestor.vue';

import AppTip from './components/features/Tip.vue';
import tabItem from './components/tabs/TabItem.vue';
import tabGroup from './components/tabs/TabGroup.vue';
import group from './components/features/Group.vue';
import cardType from './components/features/CardType.vue';
import gridForm from './components/features/GridForm.vue';
import titleBar from './components/features/TitleBar.vue';
import accordian from './components/features/Accordian.vue';
import dataPoint from './components/features/DataPoint.vue';
import modalCard from './components/features/ModalCard.vue';
import footerBar from './components/features/FooterBar.vue';
import modalPlain from './components/features/ModalPlain.vue';
import placeholder from './components/features/placeholder.vue';
import formSection from './components/features/FormSection.vue';
import paymentType from './components/features/PaymentType.vue';
import employeeName from './components/features/EmployeeName.vue';
import standardForm from './components/features/StandardForm.vue';
import buttonControl from './components/features/ButtonControl.vue';
import flyoutControl from './components/features/FlyoutControl.vue';
import condensedForm from './components/features/CondensedForm.vue';
import loadIndicator from './components/features/LoaderIndicator.vue';
import dropdownControl from './components/features/DropdownControl.vue';
import modifiedMetadata from './components/features/ModifiedMetadata.vue';
import formSectionController from './components/features/FormSectionController.vue';

import { EVENT_ERROR } from './config.js';

import {
  usd,
  json,
  towSale,
  verbalDate,
  verbalTime,
  affirmative,
  wholeNumber,
  relativeDate,
  simpleDateTime
} from '@/utils/filters.js';

import { upper } from '@/utils/directives.js';

const bowser = Bowser.getParser(window.navigator.userAgent);

const browser = {
  name: bowser.getBrowserName(),
  version: bowser.getBrowserVersion()
};

const os = {
  name: bowser.getOSName(),
  version: bowser.getOSVersion()
};

Vue.prototype.$awaitNextTicks = async function (count) {
  for (let index = 0; index < count; index++) {
    await new Promise(resolve => {
      Vue.nextTick(() => {
        resolve();
      });
    });
  }
};

Vue.use(vuex);
Vue.use(Meta);
Vue.use(VueRouter);
Vue.use(PortalVue);
Vue.use(VueAxios, axios);
Vue.use(ElementUI, { locale });
Vue.use(CompositionApi);
Vue.use(PiniaVuePlugin);

const pinia = createPinia();

const GoogleMapsPlugin = {
  install(Vue, options) {
    Vue.prototype.$googleMapsSettings = options || {}
  }
}

Vue.use(GoogleMapsPlugin, {
  client: 'gme-towxchange'
})

let router = new VueRouter({
  base: import.meta.env.VITE_BASE,
  scrollBehavior: (to, from, savedPosition) => savedPosition || { x: 0, y: 0 },
  routes
});

// Set entry_point on mount
let entryPoint = window.localStorage.getItem('entry_point') || '';

if (entryPoint.length < 3 && !includes(entryPoint, 'sign-in')) {
  window.localStorage.setItem('entry_point', window.location.hash);
}

router.beforeEach((to, from, next) => {
  if (!store.getters.INSTANCE__isValid && !includes(to.path, 'sign-in')) {
    next('/sign-in');
  } else {
    next();
  }
});

Vue.config.keyCodes = keyCodes;

Vue.filter('usd', usd);
Vue.filter('json', json);
Vue.filter('towSale', towSale);
Vue.filter('verbalDate', verbalDate);
Vue.filter('verbalTime', verbalTime);
Vue.filter('affirmative', affirmative);
Vue.filter('wholeNumber', wholeNumber);
Vue.filter('relativeDate', relativeDate);
Vue.filter('simpleDateTime', simpleDateTime);

Vue.directive('upper', upper);

store.commit('SET_APP_MODE', import.meta.env.VITE_APP_MODE);

// Register custom controls
Vue.component('app-group', group);
Vue.component('app-date', dateInput);
Vue.component('app-text', textInput);
Vue.component('app-time', timeInput);
Vue.component('app-phone', phoneInput);
Vue.component('app-yesno', yesNoInput);
Vue.component('app-number', numberInput);
Vue.component('app-select', selectInput);
Vue.component('app-user-id', userIdInput);
Vue.component('InputTowType', inputTowType);
Vue.component('InputReason', reasonInput);
Vue.component('InputCancelReason', cancelReasonInput);
Vue.component('app-checkbox', checkboxInput);
Vue.component('app-textarea', textareaInput);
Vue.component('app-customer', customerInput);
Vue.component('app-username', usernameInput);
Vue.component('InputCardType', cardTypeInput);
Vue.component('InputEmployee', employeeInput);
Vue.component('app-date-time', dateTimeInput);
Vue.component('app-shortcode', shortCodeInput);
Vue.component('app-truefalse', trueFalseInput);
Vue.component('app-suggestion', suggestionInput);
Vue.component('app-subcompany', subcompanyInput);
Vue.component('app-affirmative', affirmativeInput);
Vue.component('InputPaymentType', paymentTypeInput);
Vue.component('app-select-simple', selectSimpleInput);
Vue.component('app-select-state', selectStateInput);
Vue.component('app-sale-customer', saleCustomerInput);
Vue.component('app-select-report', selectReportInput);
Vue.component('app-select-search', selectSearchInput);
Vue.component('app-select-boolean', selectBooleanInput);
Vue.component('app-address-suggestor', addressSuggestorInput);

Vue.component('AppTip', AppTip);
Vue.component('tab-item', tabItem);
Vue.component('tab-group', tabGroup);
Vue.component('app-modal', modalCard);
Vue.component('app-titlebar', titleBar);
Vue.component('app-footerbar', footerBar);
Vue.component('app-grid-form', gridForm);
Vue.component('app-accordian', accordian);
Vue.component('app-data-point', dataPoint);
Vue.component('app-button', buttonControl);
Vue.component('control-flyout', flyoutControl);
Vue.component('app-loader', loadIndicator);
Vue.component('form-section', formSection);
Vue.component('PaymentType', paymentType);
Vue.component('CardType', cardType);
Vue.component('EmployeeName', employeeName);
Vue.component('app-slim-modal', modalPlain);
Vue.component('app-placeholder', placeholder);
Vue.component('app-dropdown', dropdownControl);
Vue.component('app-standard-form', standardForm);
Vue.component('app-condensed-form', condensedForm);
Vue.component('app-modified-metadata', modifiedMetadata);
Vue.component('form-section-controller', formSectionController);

new Vue({
  render: h => h(App),
  store,
  router,
  pinia
}).$mount('#app');

// Get the App Mode from the API Server before we do anything else
let parameters = {
  Operation: {
    Noun: 'Application',
    Verb: 'GetMode',
    ProductKey: 1000,
    Mode: store.getters.__state.appMode,
    ResponseData: 'JSON'
  }
};

Vue.axios.post(import.meta.env.VITE_TXI_API, parameters)
  .then(async response => {
    if (get(response.data, 'Result', '') !== 'SUCCESS') {
      Hub.$emit(EVENT_ERROR, response.data.Error);
    } else {
      store.commit('SET_API_MODE', response.data.Data.Mode);

      if (Cookies('txc_SessionID') && !Cookies('ignore_txc_SessionID')) {
        const device = new Device();

        const ipAddress = await window.fetch('https://ipapi.co/json/', { referrerPolicy: 'no-referrer' })
          .then(response => response.json())
          .then(data => data.ip)
          .catch(error => console.error(error));

        let parameters = {
          Operation: {
            Noun: 'Instance',
            Verb: 'CreateFromExistingSession',
            ProductKey: 1000,
            Mode: store.getters.__state.appMode,
            ResponseData: 'JSON'
          },
          Data: {
            SessionID: Cookies('txc_SessionID'),
            Product: 1000,
            DeviceID: device.id,
            DeviceInfo: `Browser: ${browser.name} ${browser.version}; OS: ${os.name} ${os.version}; IP: ${ipAddress}`
          }
        };

        Vue.axios.post(import.meta.env.VITE_TXI_API, parameters)
          .then(response => {
            let data = (isNil(response.data.Data)) ? response.data : response.data.Data;

            store.commit('SET_INSTANCE_DATA', data);
            store.commit('SET_WINDOW_NAME');

            if (!isNil(data.User)) {
              store.commit('SET_USER_DATA', data.User);
            }

            if (!isNil(data.Role)) {
              store.commit('SET_ROLE_DATA', data.Role);
            }

            let entryPoint = window.localStorage.getItem('entry_point');

            if (entryPoint) {
              Vue.nextTick(() => {
                router.push({ path: trim(entryPoint, '#') });
              });
            } else {
              Vue.nextTick(() => {
                router.push({ name: 'Default' });
              });
            }
          })
          .catch(() => {
            router.push({ name: 'SignIn' });
          });
      } else if (import.meta.env.VITE_REQUIRE_EXTERNAL_AUTHENTICATION === 'true') {
        window.location = import.meta.env.VITE_EXTERNAL_AUTHENTICATION_ROUTE + '?redirect=' + encodeURI(window.location.href);
      }
    }
  })
  .catch(error => {
    Hub.$emit(EVENT_ERROR, error.Message);
  });

window.AppcuesSettings = { enableURLDetection: true };
