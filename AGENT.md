# AGENT.md - TOPS Browser Development Guide

## Build & Test Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm test` - Run all tests with vitest
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:coverage` - Run tests with coverage report
- `pnpm lint` - Run ESLint and auto-fix issues
- `pnpm lint:check` - Run ESLint without auto-fixing
- Single test: `pnpm test <test-file>` (e.g., `pnpm test src/utils/tops.test.js`)

## Architecture
- **Framework**: Vue 2.7 with Composition API, Vue Router
- **Build**: Vite with TypeScript support
- **Testing**: Vitest with jsdom environment, setup in `tests/setup.ts`
- **Structure**: Legacy modular Vuex store (`src/store/modules/`), experimental Pinia stores (`src/stores/`), global components (`src/components/`), utilities (`src/utils/`), tower components (`src/tower/`)
- **API**: Legacy API layer in `src/api/`, modern API layer in `src/utils/tops.js`, response transformations in `src/utils/responseTransformers.js`, request transformations in `src/utils/requestTransformers.js`

## Code Style & Conventions
- **Components**: PascalCase filenames and in templates where possible (e.g., `<AppButton>`)
- **Modern Code**: Favor modern and experimental JavaScript, HTML, and CSS APIs and patterns.
- **New Code**: Use Composition API for new code, and continue using Options API where already used
- **Lean Components**: Prefer using a store when state is needed, composables for stateless shared logic, then components and child components to keep logic in the lowest possible scope.
- **Proximity**: Create new folders as needed to group related logic for clarity and reuse, including composables and stores.
- **Imports**: Use `@/` alias for `src/`
- **Styles**: Element UI and Bulma are deprecated, prefer custom components prefixed with `app-`
- **Types**: TypeScript strict mode enabled, prefer explicit typing
- **Simple**: Write clean, minimal, and readable code.
- **Self-documenting Code**: Keep comments minimal, prefer descriptive variable and function names.
- **Deprecated Dependencies**: Avoid and strategically refactor away from Element UI, Bulma, and global Vue filters, `is_js`, `moment`, `jquery`, and global registration (i.e. `this.$_`)
- **Design principles**: When designing UI or architecture, apply these principles when relevant:
  - Miller’s Law: Limit choices shown at once (7 ± 2 items).
  - Hick’s Law: Fewer, simpler choices = faster decisions.
  - Fitts’ Law: Bigger and closer targets are easier to click.
  - Jakob’s Law: Follow common patterns users expect.
  - Pareto Principle: Focus on the 20% of features that deliver 80% of value.
  - Tesler’s Law: Hide complexity from the user, not the code.
  - Aesthetic-Usability Effect: Clean design feels easier to use.

### Testing
- Write **Vitest** regression tests when fixing bugs.
